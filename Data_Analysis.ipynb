import os
import re
import json
import numpy  as np
import pandas as  pd
import altair as  alt
!pip install -q "vegafusion[embed]>=1.6.0" "vl-convert-python>=1.6.0"
alt.data_transformers.enable("vegafusion")

import matplotlib.pyplot as plt

import ast
import difflib
import base64
from PIL import Image
from io import BytesIO

# !pip install -q spacy
import spacy
nlp = spacy.load("en_core_web_sm")

!pip install -q anthropic
from typing import List, Dict
from anthropic import Anthropic


ANTHROPIC_API_KEY="************************************************************************************************************"
antropic_client = Anthropic(api_key=ANTHROPIC_API_KEY)
claude_model = "claude-3-5-sonnet-20241022"

import gspread
import pandas as pd
from google.auth import default
from google.colab import auth
auth.authenticate_user()
creds, _ = default()
gc = gspread.authorize(creds)

from google.colab import drive
drive.mount('/gdrive', force_remount=True)

%cd /gdrive/MyDrive/PhD/lab/DSPM/DSPM\ Instructor\ Materials/
!ls

import importlib
import data_analysis_utils
importlib.reload(data_analysis_utils)
from data_analysis_utils import *

# from data_analysis_utils import prepostsurvey_mapping, presurvey_mapping, postsurvey_mapping, hw0_postsurvey_mapping, hws_postsurvey_mapping, task_categories, column_groups, scales
# from data_analysis_utils import presurvey_reverse_mapping, hw0_postsurvey_reverse_mapping, hws_postsurvey_reverse_mapping, postsurvey_reverse_mapping
# from data_analysis_utils import hw_instr, hw0_instructions, hw1_instructions, hw2_instructions, hw3_instructions, hw4_instructions, SYSTEM_PROMPT

# import from google ƒsheets
def get_googlesheet(key, n=0, andrewid=False):
  spreadsheet = gc.open_by_key(key)
  worksheet = spreadsheet.get_worksheet(n)
  df = pd.DataFrame(worksheet.get())
  # Code for making the first row as header. Remove if not needed.
  df.columns = df.iloc[0]
  df = df.drop(0)
  if andrewid: df["andrewid"] = df["Email Address"].str.split("@").str[0]

  # convert appropriate columns to datetime & int & float
  for col in ["datetime_timestamp", "datetime_since_started"]:
    if col in df.columns:
      df[col] = pd.to_datetime(df[col], format='ISO8601')
  for col in ["timestamp", "time_since_started"]:
    if col in df.columns:
      df[col] = df[col].astype(int)
  if "duration_seconds" in df.columns:
    df["duration_seconds"] = df["duration_seconds"].astype(float)

  # convert to list
  if "prompt_exec_clarity" in df.columns:
    df["prompt_exec_clarity"] = df["prompt_exec_clarity"].apply(ast.literal_eval)
  return df

def map_likert_scores(df, column_groups, scales):
    for group, columns in column_groups.items():
        scale = scales[group]
        for col in columns:
            if col in df.columns:
                df[f"{col}_score"] = df[col].map(scale)
    return df

presurvey = get_googlesheet('1Ces7pDebiuUTB-SJZF8f9LFKhRE_y6bM4riFAvsTqRw', andrewid=True)
presurvey.rename(columns=presurvey_reverse_mapping, inplace=True)

# convert presurvey age column to int
presurvey['age'] = presurvey['age'].astype(int)

# clean up first languages
presurvey['first_language'] = presurvey['first_language'].str.replace('Mandarin', 'Chinese')

# clean up majors
presurvey['major'] = presurvey['major'].str.replace(r'.*(Chemical Engineering|Information Systems and Software Engineering|Chemical engineering|Information Technology|Bachelor in Information Systems|Learning Sciences / Information Science).*', 'Computer Science / Engineering', regex=True)
presurvey['major'] = presurvey['major'].str.replace('Agricultural Economics', 'Business / Marketing / Communication Studies')
presurvey['major'].unique()


# --- Apply mappings ---
presurvey = map_likert_scores(presurvey, column_groups, scales)
presurvey["perceived_expertise_data_science_score"] = presurvey[[f"perceived_expertise_{t}_score" for t in task_categories]].mean(axis=1)
presurvey['major'].unique()

# filter presurvey to only keep the columns with the postfix _score and andrewid
presurvey_score = presurvey[[col for col in presurvey.columns if col.endswith("_score") or col in["andrewid", "age", "gender", "race", "first_language", "major"]]]
print("presurvey_score columns:")
presurvey_score.columns

# import postsurveys
hw0_postsurvey = get_googlesheet('1mjU9w76e_eapypyoj2d3gUsewyqh-OXeeysIcx32Khg', andrewid=True)
hw1_postsurvey = get_googlesheet('1A5Hq8lO5VZzG9C9oMtbnQylpKMZUwO6zn-XpKwTvpLA', andrewid=True)
hw2_postsurvey = get_googlesheet('1V1ApGRK1TyBTVW3nv3XXFjteYTcUVLw6isafvjU0_c8', andrewid=True)
hw3_postsurvey = get_googlesheet('15cLRB2NfBtKrObFWtAR0qWLaEqkDKe_g1VolGPUq0bI', andrewid=True)
hw4_postsurvey = get_googlesheet('15VAhyx1UlYTuA9atWp7HUXaONq_TqFKtpcqnAv8MrjI', andrewid=True)
postsurvey = get_googlesheet('1lI9Z8ra4BE7c8OhqzTwk5C6WwuuZt1RECI-vBYhDvDQ', andrewid=True)

# --- Extract score columns ---
def format_df_and_dfscore(df, reverse_mapping):
    df.rename(columns=reverse_mapping, inplace=True)
    df = map_likert_scores(df, column_groups, scales)
    df_score = df[[col for col in df.columns if col.endswith("_score") or col == "andrewid"]]
    if "task_used_llm" in df.columns:
      df["task_no_llm"] = df["task_no_llm"].str.split(", ")
      df["task_used_llm"] = df["task_used_llm"].str.split(", ")
      df_score["task_used_llm"] = df["task_used_llm"]
    return df, df_score

hw0_postsurvey, hw0_postsurvey_score = format_df_and_dfscore(hw0_postsurvey, hw0_postsurvey_reverse_mapping)
hw0_postsurvey_score.columns

# plot the distribution of hw0_postsurvey["task_used_llm"]
hw0_postsurvey["task_used_llm"].explode().value_counts() # .plot(kind="bar")

hw1_postsurvey, hw1_postsurvey_score = format_df_and_dfscore(hw1_postsurvey, hws_postsurvey_reverse_mapping)
hw2_postsurvey, hw2_postsurvey_score = format_df_and_dfscore(hw2_postsurvey, hws_postsurvey_reverse_mapping)
hw3_postsurvey, hw3_postsurvey_score = format_df_and_dfscore(hw3_postsurvey, hws_postsurvey_reverse_mapping)
hw4_postsurvey, hw4_postsurvey_score = format_df_and_dfscore(hw4_postsurvey, hws_postsurvey_reverse_mapping)
postsurvey, postsurvey_score = format_df_and_dfscore(postsurvey, postsurvey_reverse_mapping)
postsurvey_score.columns

def extract_hw(andrewid, hw="hw0"):
  path = hw_path[hw]
  # print("Extracting", hw, "for", andrewid, path)
  try:
    notebook, histories, notebook_content, history_content = None, [], None, []
    for file in os.listdir(path + andrewid):
      if file.endswith(".ipynb"):
        notebook = path + andrewid + os.sep + file
      if file.endswith(".json"):
        history = path + andrewid + os.sep + file
        histories.append(history)
    if notebook is None: print(f"{hw} {andrewid}: No notebook found")
    else:
      with open(notebook, 'r') as f:
        notebook_content = f.read()
    if len(histories) == 0: print(f"{hw} {andrewid}: No history found")
    else:
      for history in histories:
        with open(history, 'r') as f:
          content = json.load(f)
          history_content.append(content)
    return notebook_content, history_content

  except Exception as e:
    print(f"{hw} {andrewid}: Error {e}")
    return None


histories = extract_hw("elakra", "hw2")[1]
for h in histories: print(h['started'], h['version'], len(h['events']))

def pprint(d): print(json.dumps(d, indent=4))

def deep_copy_keys(d):
    if isinstance(d, dict):
        new_d = {}
        for k, v in d.items():
            new_d[k] = deep_copy_keys(v)
        return new_d
    else:
      if d in list(all_event_types.keys()): return d
      else: return ""

all_event_types = {'converse': [], 'generateCode': [], 'editCodeCell': [], 'executeCode': [], 'executeCodeComplete': []}

# videos to look at: aafonja, suhailk, carolinefan-sijiaf, achavess
def get_recording_link(andrewid):
  try:
    return hw0_postsurvey[hw0_postsurvey['andrewid'] == andrewid]['Link to Zoom recording'].iloc[0]
  except:
    return hw0_postsurvey[hw0_postsurvey['andrewid'] == andrewid]['Link to Zoom recording']


andrewid = 'usa' #'aafonja' # "suhailk" # 'usa' "achavess" # 'sijiaf'
history = extract_hw(andrewid, 'hw1')[1][0]

#list(history["events"][i]["event"].keys())
for i in range(len(history["events"])):
  eventType = history["events"][i]["event"]['eventType']
  all_event_types[eventType] = deep_copy_keys(history["events"][i]["event"])

print(all_event_types)
history["events"][0]

def is_not_just_comment(text):
  if len(text.strip()) == 0: return False
  lines = text.strip().split('\n')
  for line in lines:
      if not line.startswith('#'):
          return True  # Found a line that's not a comment
  return False

def is_python_code(text):
    try:
        if not is_not_just_comment(text): return False
        ast.parse(text)
        return True
    except (SyntaxError, ValueError):
        return False

def extract_user_additions_with_copied_tag(instruction, user_prompt):
    instr_tokens = instruction.split()
    user_tokens = user_prompt.split()
    sm = difflib.SequenceMatcher(None, instr_tokens, user_tokens)
    result = []
    num_prefix = 2
    copied_instruction, customized_instruction = False, False
    for opcode, i1, i2, j1, j2 in sm.get_opcodes():
        if opcode == 'equal':
          if i2 > i1 + num_prefix*2:
            copied_instruction = True
            label = ['[copied: '] + instr_tokens[i1:i1+num_prefix] + ['...'] + instr_tokens[i2-num_prefix:i2] + [']']
            result.extend(label) # if label not in result else None
          else: result.extend(user_tokens[j1:j2])
        elif opcode in ('insert', 'replace'):
          customized_instruction = True
          result.extend(user_tokens[j1:j2])
        # ignore 'delete' (only from instruction, not present in user input)

    return copied_instruction, customized_instruction, ' '.join(result)


# 1210 edit_code for hw0 before filtering, after
def get_code_diff_edit(old_code, new_code, last_gen_response):
    # misclassified popup over cell / generated output in cell
    # meaningless edits, e.g., - #, - # statistics
    # ignore entries where new_code include "Follow link (ctrl + click)" or old_code include "DataFrame: df_"
    code = old_code + new_code
    if len(code) < 15 or "Follow link (ctrl + click)" in code or "DataFrame: df_" in code:
      return None, None

    old_lines = old_code.strip().splitlines()
    new_lines = new_code.strip().splitlines()

    edit_content = "code"
    # if each line in old_lines and new_lines all startswith "#": event_name: edit_comment
    if all(line.startswith('#') for line in old_lines) and all(line.startswith('#') for line in new_lines):
      edit_content = "comment"

    # copied generated output response (entire code? partial code?)
    if new_code in last_gen_response:
      code_event = f"copied_from_generated_only"
    else:
      copied, _, edited_newcode = extract_user_additions_with_copied_tag(last_gen_response, new_code)
      if copied:
        code_event = f"copied_from_generated_partially"
        # print(edited_newcode)
      else:
        code_event = f"edit_{edit_content}"


    diff = difflib.ndiff(old_lines, new_lines)
    diff = [line for line in diff if line.startswith('- ') or line.startswith('+ ')]
    # diff = list(difflib.unified_diff(
    #   old_lines, new_lines, fromfile='old_code', tofile='new_code', lineterm=''
    # ))

    return code_event, '\n'.join(diff)

def parse_prompt_events(request, hw="hw0"):
  prompt = request["user_prompt"]
  code = request["current_code"]
  # press View plots
  if prompt.startswith("The summarization of the data you are generating visualizations for is:") and prompt.endswith("What are your top recommendations?"):
    return "press_view_recommended_plots", prompt
  # press Explain code
  if prompt.startswith("Given the following code:") and "Explain this selection of code:" in prompt:
    return "press_explain_code", prompt
  # press Explain error
  if prompt.startswith("The following source files:") and "Failed with the traceback:" in prompt:
    return "press_explain_error", prompt

  # copy-pasted instructions
  if len(prompt) > 100: # for long prompts, check for instruction overlaps
    copied_instruction, customized_instruction, cleaned_prompt = extract_user_additions_with_copied_tag(hw_instr[hw], prompt)
    if copied_instruction and not customized_instruction: return "copied_instruction_only", cleaned_prompt
    elif copied_instruction and customized_instruction: return "copied_instruction_customized", cleaned_prompt
    elif "Traceback (most recent call last)" in prompt: return "copied_error_message", prompt

    # copy-pasted notebook to prompt
    copied_code, customized_code, cleaned_prompt = extract_user_additions_with_copied_tag(code, prompt)
    code_prefix = "code_" if is_python_code(prompt) else ""
    if copied_code and not customized_code: return f"copied_{code_prefix}from_notebook_only", cleaned_prompt
    # selectively copy-pasted to prompt
    elif copied_code and customized_code: return f"copied_{code_prefix}from_notebook_customized", cleaned_prompt

    # in all other cases
    return "long_prompt", prompt

  # press generate code with df
  if code.startswith("variable_name:\ndf"):
    return "press_generate_code_with_df", prompt

  # user enter prompt / use auto-suggested prompt
  else: return "enter_prompt", prompt


def decode_img(img_str, timestamp=None, andrewid=None, hw="hw0"):
  path=hw_path[hw]
  # Decode the Base64 string
  image_data = base64.b64decode(img_str)
  file_name = f"{path+andrewid}/{timestamp}.png"
  # Save it as an image file
  if andrewid and not os.path.exists(file_name):
    # if filename already exist, skip
    with open(file_name, "wb") as file:
        file.write(image_data)
  else:
      # Display the image inline
      image = Image.open(BytesIO(image_data))
      image.show()
  return file_name

def parse_executeCodeComplete(event, andrewid='images', hw='hw0'):
  timestamp = event['timestamp']
  results = event['event']['results']
  stdout, error, code = results["stdout"], results["error"], results["code"]
  if error is not None:
    return "executed_error", error['ename'] + ": " + error['evalue']
  if 'data' in results:
    data = {}
    if "text/plain" in results["data"]:
      data["text/plain"] = results["data"]["text/plain"]
    if not len(stdout) == 0: data['stdout'] = stdout
    # key = '_'.join(list(results["data"].keys()))]
    if 'image/png' in results["data"]:
      file_name = decode_img(results["data"]["image/png"], timestamp, andrewid, hw)
      data['image/png'] = file_name
      return "executed_display_image", data
    # if 'text/html' in results["data"]: return "display_html", data
    # if 'text/plain' in results["data"]:
    return "executed_display_text", data
  if len(stdout) == 0:
    if not is_python_code(code): return "executed_no_code", None
    return "executed_no_output", stdout
  if stdout.startswith("<class 'pandas.core.frame.DataFrame'>"): return "executed_dataframe", stdout
  # print(results)
  return "executed_output", stdout


def merge_event_name(event_name, event_type):
    if event_name in ['executed_dataframe', 'executed_display_image', 'executed_display_text',
                      'executed_no_code', 'executed_no_output', 'executed_output']:
        return 'executed_no_alerted_error'

    elif event_name in ['copied_code_from_notebook_customized', 'copied_code_from_notebook_only',
                        'copied_from_notebook_customized', 'copied_from_notebook_only',
                        'copied_error_message', 'copied_instruction_customized', 'copied_instruction_only']:
        return 'copied_from_instruction/notebook'

    elif event_name in ['press_generate_code_with_df', 'press_view_recommended_plots']:
        return 'press_generate_code/plots'

    elif event_name in ['copied_from_generated_only', 'copied_from_generated_partially']:
        return 'copied_from_generation'

    elif event_name in ['edit_code', 'edit_comment']:
        return 'edit_code/comment'

    elif event_name in ['enter_prompt', 'long_prompt'] and event_type == 'converse':
        return 'customize_prompt_chat'

    elif event_name in ['enter_prompt', 'long_prompt'] and event_type == 'generateCode':
        return 'customize_prompt_code'

    else:
        return event_name  # default fallback

def query_claude(prompt: str) -> str:
    response = antropic_client.messages.create(
        model=claude_model,
        max_tokens=100,
        temperature=0,
        system=SYSTEM_PROMPT,
        messages=[
            {"role": "user", "content": f"Prompt: {prompt}"}
        ]
    )
    return response.content[0].text.strip()

def quote_identifiers(s):
    return re.sub(r'(?<!["\'])\b([a-zA-Z_][a-zA-Z0-9_]*)\b(?!["\'])', r"'\1'", s)

def tag_prompt_claude(prompt):
    eval_fail = True
    while eval_fail:
      try:
        result = query_claude(prompt)
        # Auto-quote identifiers that aren't quoted
        result_fixed = quote_identifiers(result)

        tags = ast.literal_eval(result_fixed)
        assert isinstance(tags, tuple)
        assert len(tags) == 2
        assert isinstance(tags[0], str)
        assert isinstance(tags[1], list)
        eval_fail = False
      except Exception as e:
        print(e, result, "retrying...")
    return tags

def tag_multiple_prompts(prompts: List[str]):
    results = []
    for prompt in prompts:
      tags = tag_prompt_claude(prompt)
      results.append({"prompt": prompt, "tags": tags})
    return results

# Example strings
s1 = "direct_request, [vague_verb, vague_object]"
s2 = "'direct_request', ['vague_verb', 'vague_object']"
s3 = '"no_intent", [vague_verb, vague]'
s4 = 'happy, [vague_turn, hello"]'
s5 = '"heppy", ["she", "ahe"]'

for s in [s1, s2, s3, s4, s5]:
  print("fixed: ", quote_identifiers(s))

def convert_to_list(s):
    try:
      if s is None or s == 'None': return (None, None)
      if "Reasoning" in s:
        s = s.split("Reasoning")[0]
        print(s)
      # Add quotes around barewords (e.g., no_intent → 'no_intent')
      s = re.sub(r'(?<!["\'])\b([a-zA-Z_][a-zA-Z0-9_]*)\b(?!["\'])', r"'\1'", s)
      # if '"' not in s or "'" not in s: #   print(s, after_s) #   s = after_s
      result = ast.literal_eval(s) #eval(s)
      assert isinstance(result, tuple)
      return result
    except Exception as e: # (SyntaxError, ValueError, TypeError)
      print(e, s)
      return [s, s]

test_prompts = [
    "how can I replace all values to a specific value?",
    "Give me the statistics code for this",
    "I wrote this code df_cars['Car Make'].unique()"
]

"""
for result in tag_multiple_prompts(test_prompts):
    print(f"> {result['prompt']}")
    print(f"  → {result['tags']} {type(result['tags'])} \n")

> how can I replace all values to a specific value?
  → ('conceptual_question', []) <class 'tuple'>

> Give me the statistics code for this
  → ('direct_request', ['vague_verb', 'vague_object']) <class 'tuple'>

> I wrote this code df_cars['Car Make'].unique()
  → ('no_intent', ['no_verb', 'no_object']) <class 'tuple'>
"""

check_prompts = ["copied_instruction_customized", "copied_from_notebook_customized", "copied_code_from_notebook_customized", "long_prompt", "enter_prompt", "press_generate_code_with_df"] #  "copied_error_message",

# build a df with the columns: andrewid, event_category, event_name, event_content, timestamp
# event_category = prompt or code
# event_name = prompt_event: press_view_recommended_plots, etc.; or code_event: executeCode?, executeCodeComplete, editCodeCell
# event_content = prompt for prompt_event, code for executeCode?, code_error/stdout for executeCodeComplete, code_diff for editCodeCell
def process_history_per_student(andrewid, hw='hw0'):
  result = extract_hw(andrewid, hw)
  if result is None: return []
  final_notebook, histories = result
  rows = []
  last_gen_response = ""

  for history in histories:
    for event in history["events"]:
      event_category = "code"
      eventType = event['event']["eventType"]
      prompt_intent, prompt_exec_clarity = "None", "None"
      # print("###", eventType, "###")
      if eventType == "converse" or eventType =="generateCode":
        last_gen_response = event['event']['response']
        event_category = "prompt"
        prompt_event, prompt = parse_prompt_events(event['event']['request'], hw)
        event_name, event_content = prompt_event, prompt
        if event_name in ["long_prompt", "enter_prompt"]:
           prompt_intent, prompt_exec_clarity = tag_prompt_claude(event_content)
        # if prompt_event in check_prompts: print(prompt_event, prompt)
        # print("### Response: ", event['event']['response'])
      elif eventType == "executeCodeComplete":
        code_event, output = parse_executeCodeComplete(event, andrewid, hw)
        event_name, event_content = code_event, output
      elif eventType == "editCodeCell":
        event_name, event_content = get_code_diff_edit(event['event']['oldCode'], event['event']['newCode'], last_gen_response)
        if event_name is None: continue
        # event_name, event_content = "edit_code", code_diff
      else:
        assert(eventType == "executeCode")
        continue # skip this type
        # event_name, event_content = "execute_code", event['event']['code']

      time_since_started = event['timestamp'] - history['started']
      rows.append(
          {"hw": hw, 'andrewid': andrewid, 'timestamp': event['timestamp'], 'time_since_started': time_since_started,
            'event_category': event_category, 'event_type': eventType,
            'event_name': event_name, 'event_content': event_content,
            'event_merged': merge_event_name(event_name, eventType),
            'prompt_intent': prompt_intent, 'prompt_exec_clarity': prompt_exec_clarity,
            # Convert the time columns to datetime objects
            'datetime_timestamp': pd.to_datetime(event['timestamp'], unit='ms').tz_localize('UTC').tz_convert('US/Eastern'),
            'duration_seconds': pd.to_timedelta(time_since_started, unit='ms').total_seconds(),
            'datetime_since_started': pd.to_datetime(time_since_started, unit='ms')
            })

  return rows

# save to google sheet
def save_to_gsheet(df, n=0, sheet_key='1xAf22AmpBP-Is68OxrFuRcbbT3b2VHZnHfO8bNz7pFY'):
  spreadsheet = gc.open_by_key(sheet_key)
  worksheet = spreadsheet.get_worksheet(n)
  # worksheet = spreadsheet.sheet1
  df_values_str = [[str(val)[:30000] for val in row] for row in df.values]
  worksheet.update([df.columns.values.tolist()] + df_values_str)

def get_processed_history_hw(hw='hw0', save=False):
  rows = []
  for andrewid in andrew_id_list:
    try:
      rows.extend(process_history_per_student(andrewid, hw))
    except Exception as e:
      print(f"{hw} {andrewid}: Error processing {e}")
      continue
  df = pd.DataFrame(rows)

  if save: # save to csv & google sheet
    df.to_csv(f"DSPM_{hw}_event_logs.csv", index=False)
    save_to_gsheet(df, int(hw[-1]))

  return df

# df_hw0 = get_processed_history_hw('hw0', save=True)
# df_hw2 = get_processed_history_hw('hw2', save=True)
# save_to_gsheet(df_hw1, 1)
# before expanding to multiple histories files, len(events) = 1494, after => 2543 => after attaching think-alouds 2827

# read df_hw from gsheet
event_log_sheet_key = '1xAf22AmpBP-Is68OxrFuRcbbT3b2VHZnHfO8bNz7pFY'
df_hw0 = get_googlesheet(key=event_log_sheet_key, n=0)
df_hw1 = get_googlesheet(key=event_log_sheet_key, n=1)
df_hw2 = get_googlesheet(key=event_log_sheet_key, n=2)
df_hw3 = get_googlesheet(key=event_log_sheet_key, n=3)
df_hw4 = get_googlesheet(key=event_log_sheet_key, n=4)

# df_hw1.describe(include='all')
for i, df in enumerate([df_hw0, df_hw1, df_hw2, df_hw3, df_hw4]):
  print(f"hw{i} entries: ", df.shape[0])

df_hw0.info()

# df = pd.DataFrame(process_history_per_student('jgu2', hw='hw1'))
# df = pd.DataFrame(process_history_per_student('sjashnan', hw='hw2'))
# df = pd.DataFrame(process_history_per_student('hpasha', 'hw4'))
# df.to_csv("redo_student.csv", index=False)
# df

grading_sheet_key = '1f4_cwsmG4bEgGxL7MZBqZQzL84fFLGo8DuNw--xfAp4'
grading_conditions = get_googlesheet(grading_sheet_key, 1)
hw0_grading = get_googlesheet(grading_sheet_key, 2)
hws_grading = get_googlesheet(grading_sheet_key, 3)
hw0_grading_irr = get_googlesheet(grading_sheet_key, 4)

for df in [hw0_grading, hws_grading, hw0_grading_irr]:
    df["score"] = pd.to_numeric(df["score"], errors="coerce")

# find hw0 unique andrewids where item valid_notebook has a score of 0
print(hw0_grading[(hw0_grading["item"] == "valid_notebook") & (hw0_grading["score"] == 0)]["andrewid"].unique())

# filter out the rows where task = logistics
hw0_grading_irr = hw0_grading_irr[hw0_grading_irr["task"] != "logistics"]
hw0_grading = hw0_grading[hw0_grading["task"] != "logistics"]

# for hw0, filter out gsumukh, jgu2, tsohani
# for hw2 & 3, filter out wenyili (no log history upload)
# dropouts = ishengc, elianah, etripath, kviknesh, hengyuez
exclude_andrewids = {
  "hw0": ["gsumukh", "jgu2", "tsohani"], #  "elianah", "kviknesh" - no recording/code of prompt turns
  "hws": ["ishengc", "elianah", "etripath", "kviknesh"] + ["wenyili"] # dropouts & technical issue
}
for hw, andrewids in exclude_andrewids.items():
  if hw == "hw0":
    hw0_grading = hw0_grading[~hw0_grading["andrewid"].isin(andrewids)]
    df_hw0 = df_hw0[~df_hw0["andrewid"].isin(andrewids)]
  else:
    hws_grading = hws_grading[~hws_grading["andrewid"].isin(andrewids)]
    df_hw1 = df_hw1[~df_hw1["andrewid"].isin(andrewids)]
    df_hw2 = df_hw2[~df_hw2["andrewid"].isin(andrewids)]
    df_hw3 = df_hw3[~df_hw3["andrewid"].isin(andrewids)]
    df_hw4 = df_hw4[~df_hw4["andrewid"].isin(andrewids)]


for i, df in enumerate([df_hw0, df_hw1, df_hw2, df_hw3, df_hw4]):
  print(f"hw{i} entries: ", df.shape[0])

df_hw4[df_hw4["andrewid"] == "wenyili"]

# @title
# prompt: find students who is novice or beginner for programming but advanced or expert for data cleaning
def andrewids(df): return df['andrewid'].to_list()

def get_expertise_subsets(df, domain, col, novice_score_max=2, expert_score_min=3):
  novice_df = df[df[col] <= novice_score_max]
  expert_df = df[df[col] >= expert_score_min]
  print(f"novice (<{novice_score_max}) in {domain}: {novice_df.shape[0]}")
  print(f"expert (>{expert_score_min}) in {domain}: {expert_df.shape[0]}")
  return {"novice": novice_df, "expert": expert_df}

expertise_columns = {
    "programming": "perceived_expertise_programming_score",
    "data science": "perceived_expertise_data_science_score",
    "communication": "perceived_expertise_communication_score",
    "llm": "perceived_expertise_llm_score"
}

# Dictionary to store the filtered dataframes
perceived_expert_dfs = {}
for domain, col in expertise_columns.items():
    perceived_expert_dfs[domain] = get_expertise_subsets(presurvey, domain, col)

novice_programming, expert_programming = perceived_expert_dfs['programming']['novice'], perceived_expert_dfs['programming']['expert']
novice_ds, expert_ds = perceived_expert_dfs['data science']['novice'], perceived_expert_dfs['data science']['expert']

print("Students who are novice or beginner in programming but advanced or expert in data science:")
# find intersect
novice_programming[novice_programming["andrewid"].isin(expert_ds["andrewid"])]

print("Students who are novice or beginner in data science but advanced or expert in programming:")
beginnerDS_expertP = novice_ds[novice_ds["andrewid"].isin(expert_programming["andrewid"])]
andrewids(beginnerDS_expertP)

perceived_expert_dfs['communication']['novice']

# prompt: plot distribution of event_name


def plot_event_name_distr(df=None, avg=False):
    total_students = df['andrewid'].nunique()
    if avg:
        data = df.groupby(['event_merged', 'event_name', 'event_category']).size().reset_index(name='count')
        data['value'] = data['count'] / total_students
        x_field = alt.X('value:Q', title='Avg Uses per Student')
        tooltip = ['event_name', 'event_merged', 'value']
    else:
        x_field = alt.X('count():Q', title='Total Count')
        tooltip = ['event_name:N', 'count():Q']
        data = df
    return alt.Chart(data).mark_bar().encode(
        x=x_field,
        y=alt.Y('event_merged:N', title=None, sort='-x'),
        color='event_category:N',
        tooltip=tooltip
    ).properties(height=200, width=100).interactive()


plot_event_name_distr(df_hw0, avg=True)

# Visualize the distribution of prompt tags
def viz_prompt_tags(df, avg=False):
    total_students = df['andrewid'].nunique()
    print("Num students:", total_students)
    data = df.explode('prompt_exec_clarity').dropna(subset=['prompt_exec_clarity'])

    if avg:
        data = (data.groupby(['prompt_exec_clarity', 'prompt_intent', 'event_type'])
            .size().reset_index(name='count'))
        data['value'] = round(data['count'] / total_students, 2)
        x_field = alt.X('value:Q', title='Avg Uses per Student')
        tooltip = ['prompt_exec_clarity', 'prompt_intent', 'event_type', 'value']
    else:
        x_field = alt.X('count():Q', title='Total Count')
        tooltip = ['prompt_exec_clarity', 'prompt_intent', 'event_content', 'andrewid'] #, 'count()'

    return alt.Chart(data).mark_bar().encode(
        y=alt.Y('prompt_exec_clarity:N', title='Prompt Clarity Tag'),
        x=x_field,
        column=alt.Column('prompt_intent:N', title='Prompt Intent'),
        color='event_type:N',
        tooltip=tooltip
    ).properties(
        title=f'Prompt Tag Distribution (Average/{total_students})' if avg else 'Prompt Tag Distribution (Total)',
        width=200, height=100
    ).resolve_scale(x='shared', y='shared'
    ).interactive()

viz_prompt_tags(df_hw0, avg=True)

# display the event plots
def plot_timestamp_logs(df=None,width=500,height=200,show_event_distr=False,avg=False):
  print("Num students:", df['andrewid'].nunique())
  base = alt.Chart(df).mark_tick(thickness=3,opacity=0.2).encode(
      x=alt.X('datetime_since_started:T', axis=alt.Axis(format='[%d]%H:%M:%S', title='Duration ([day]hr:mm:ss)')),
      y=alt.Y('event_merged:N', title=''), #event_name
      color='event_category:N',
      tooltip=['andrewid', 'event_name', 'event_category', 'event_type', 'event_content']
    ).properties(
      width=width,
      height=height,
  ).interactive()

  if show_event_distr:
    return alt.hconcat(base, plot_event_name_distr(df,avg)
    ).resolve_scale(y='shared'
    ).resolve_legend(color='shared')
  else:
    return base

plot_timestamp_logs(df_hw0, show_event_distr=True,avg=True)

def subset(df, include_andrewids=None, include_event_category=None, exclude_event_names=None, duration_upperbound=None):
  if include_andrewids is not None:
    if not isinstance(include_andrewids, list): include_andrewids = andrewids(include_andrewids)
    df = df[df['andrewid'].isin(include_andrewids)]
  if include_event_category is not None:
    df = df[df['event_category'] == include_event_category]
  if exclude_event_names is not None:
    df = df[~df['event_name'].isin(exclude_event_names)]
  if duration_upperbound is not None:
    df = df[df['duration_seconds'] <= duration_upperbound]
  return df

# hw0: Filter out rows where duration is greater than 90 minutes
# the duration column is in seconds (90 * 60 seconds)
df_filtered_hw0 = subset(df_hw0, duration_upperbound=90*60)

plot_timestamp_logs(df_filtered_hw0, show_event_distr=True)

# dfs is a dictionary of label to df_hw, e.g., {"hw0": df_hw0, }
def plot_logs_by_hw(dfs, plot='timestamp_plot',avg=False):
  charts = []
  for label, df in dfs.items():
    if plot == 'timestamp_plot':
      chart = plot_timestamp_logs(df, width=350, height=120, show_event_distr=True,avg=avg).properties(title=label)
      charts.append(chart)
    elif plot == 'prompt_tag_plot':
      chart = viz_prompt_tags(df,avg).properties(title=label)
      charts.append(chart)
  return alt.vconcat(*charts)#.resolve_scale(x='independent', y='shared')


def plot_timestamp_logs_by_user(df):
  return alt.Chart(df).mark_tick(size=30,thickness=3,opacity=0.5).encode(
      x=alt.X('datetime_since_started:T', axis=alt.Axis(format='%H:%M:%S', title='Duration (hr:mm:ss)')),
      y='andrewid:N',
      color='event_type:N',
      tooltip=['andrewid', 'event_name', 'event_category', 'event_content'],

  ).properties(
      width=900,
      height=300,
      title='Event Name Throughout Timestamps'
  ).interactive()

# Facet the chart by the expertise group so that the novice and expert charts appear side by side.
def plot_logs_by_expertise(df, score_column_name, novice_score_max=2, expert_score_min=3, plot='timestamp_plot', show_event_distr=True,avg=False):

    merged_df = df.merge(presurvey[['andrewid', score_column_name]], on='andrewid', how='left')
    merged_df['expertise_group'] = merged_df[score_column_name].apply(
        lambda x: 'Novice' if x <= novice_score_max else ('Expert' if x >= expert_score_min else np.nan))
    # If a score falls in between, drop those rows from the faceted chart.
    merged_df = merged_df.dropna(subset=['expertise_group'])

    # Compute counts for each expertise group and create a new label column with counts
    group_counts = merged_df.groupby('expertise_group')['andrewid'].nunique().to_dict()
    merged_df['expertise_group_label'] = merged_df['expertise_group'].map(
        lambda g: f"{g} (n={group_counts.get(g,0)})")

    # base = plot_timestamp_logs(merged_df, width=350)
    # faceted_chart = base.facet(column=alt.Column('expertise_group_label:N')
    # )
    charts = []
    for label in sorted(merged_df['expertise_group_label'].unique()):
        subset = merged_df[merged_df['expertise_group_label'] == label]
        # Create the hconcat chart for this group.
        if plot == 'timestamp_plot':
          chart = plot_timestamp_logs(subset, width=350, height=120, show_event_distr=show_event_distr,avg=avg).properties(title=label)
          charts.append(chart)
        elif plot == 'prompt_tag_plot':
          chart = viz_prompt_tags(subset,avg).properties(title=label)
          charts.append(chart)

    # Vertically concatenate the charts.
    vconcat_chart = alt.vconcat(*charts
    ).resolve_scale(
        x='independent',
        y='shared'
    ).properties(title='Expertise by '+score_column_name)
    return vconcat_chart

plot_timestamp_logs(subset(df_filtered_hw0, beginnerDS_expertP), show_event_distr=True)

plot_timestamp_logs(subset(df_filtered_hw0, novice_programming), show_event_distr=True)

viz_prompt_tags(subset(df_filtered_hw0, beginnerDS_expertP),avg=True)

plot_logs_by_expertise(subset(df_filtered_hw0, include_event_category='prompt'),
                       'perceived_expertise_programming_score', avg=True)

plot_logs_by_expertise(df_filtered_hw0, 'perceived_expertise_programming_score',
                       novice_score_max=2, expert_score_min=3, plot='prompt_tag_plot',
                       avg=True)

# plot_logs_by_expertise(df_filtered_hw0, 'perceived_expertise_data_science_score')

plot_logs_by_expertise(subset(df_filtered_hw0, exclude_event_names=['edit_code']),
                       'perceived_expertise_data_science_score',
                       avg=True
                       )

plot_logs_by_expertise(df_filtered_hw0, 'perceived_expertise_data_science_score',
                       plot='prompt_tag_plot', avg=False)


plot_logs_by_expertise(subset(df_filtered_hw0, include_event_category='prompt'),
                       'perceived_expertise_llm_score', show_event_distr=False) # exclude_event_names=['edit_code']

plot_logs_by_expertise(df_filtered_hw0, 'perceived_expertise_llm_score', plot='prompt_tag_plot')

# filter by more than self-perceived expertise:

# year_experience_python_score
# year_experience_jupyter/colab_score
# year_experience_excel_score
plot_logs_by_expertise(subset(df_filtered_hw0,exclude_event_names=['edit_code']), 'year_experience_python_score') #'year_experience_jupyter/colab_score') #

plot_logs_by_expertise(subset(df_filtered_hw0), 'year_experience_python_score',
                       novice_score_max=1, expert_score_min=2, plot='prompt_tag_plot',
                       avg=True)

plot_logs_by_expertise(subset(df_filtered_hw0), 'year_experience_jupyter/colab_score',
                       novice_score_max=1, expert_score_min=2, plot='prompt_tag_plot',
                       avg=True
                       )

# plot_logs_by_expertise(subset(df_filtered_hw0), 'year_experience_excel_score', novice_score_max=3, expert_score_min=4, plot='prompt_tag_plot')

plot_timestamp_logs_by_user(subset(df_filtered_hw0, beginnerDS_expertP, exclude_event_names=['edit_code']))

plot_timestamp_logs_by_user(subset(df_filtered_hw0, beginnerDS_expertP, include_event_category='prompt'))

presurvey_score.columns.to_list()
hw0_postsurvey_score.describe(include='all')

# df_hw1 = get_processed_history_hw('hw1', save=True)
df_hw1.head()

print(df_hw1.shape)
plot_timestamp_logs(df_hw1)

dfs = {"hw0_all_tasks": df_filtered_hw0, "hw1_data_cleaning": df_hw1, "hw2_eda": df_hw2, "hw3_ml": df_hw3, "hw4_data_storytelling": df_hw4 }

plot_logs_by_hw(dfs, avg=True)

plot_logs_by_hw(dfs, plot='prompt_tag_plot', avg=False)

# print all recording_link for hw0_postsurvey

for andrewid in andrew_id_list:
# videos to look at: aafonja, suhailk, carolinefan-sijiaf, achavess
  response = get_recording_link(andrewid)
  print(andrewid, response)
  # print(andrewid)
  # print(response.split(" ")[0] if len(response) > 0 else None)
  # print(response.split(" ")[-1] if (len(response) > 0 and len(response.split(" "))>1) else "No Passcode")

hw0_grading_irr.describe(include="all")

from sklearn.metrics import cohen_kappa_score

# pivot the scores so each grader is a column
scores_wide = hw0_grading_irr.pivot_table(
    index=["andrewid", "task", "item"],
    columns="grader",
    values="score"
).reset_index()

# scores_wide = scores_wide.dropna(subset=["jk", "yb"])

percent_agreement = (scores_wide["jk"] == scores_wide["yb"]).mean()
kappa = cohen_kappa_score(scores_wide["jk"], scores_wide["yb"], weights="quadratic")

print(f"Percent agreement: {percent_agreement:.2%}")
print(f"Cohen's kappa: {kappa:.3f}")

grading_conditions.head()

# summary statistics for hw0 four tasks, how many of them have no time to attempt the task, probably remove
hw0_grading.head()

# for each row of hw0_grading, check if the second digit of hw0_grading['task'] is in any of the value of the list in hw0_postsurvey_score['task_used_llm']
def check_llm_usage(row):
  task_digit = row['task'][1]  # Extract the second digit from the task string
  andrewid = row['andrewid']
  try:
    tasks_used_llm = hw0_postsurvey_score[hw0_postsurvey_score['andrewid'] == andrewid]['task_used_llm']
    if len(tasks_used_llm) == 0: return None
    tasks_used_llm = tasks_used_llm.iloc[0]
    # print(type(tasks_used_llm), tasks_used_llm)
    for task in tasks_used_llm:
      if task_digit in task:
        return True
    return False
  except Exception as e:
    print(andrewid, task_digit, e)

hw0_grading['used_llm'] = hw0_grading.apply(check_llm_usage, axis=1)

# filter out hw_grading with used_llm column being None (4 students)
hw0_grading = hw0_grading[hw0_grading['used_llm'].notna()]
hw0_grading.shape

hw0_grading.groupby('task')['score'].describe()

# group by task across all items
task_scores = hw0_grading.groupby(['andrewid', 'task', 'used_llm'])['score'].sum().reset_index()
task_scores.describe(include='all')

task_scores.to_csv("task_scores.csv", index=False)

alt.Chart(task_scores).mark_boxplot().encode(
    alt.X('task:N', title='Task'),
    alt.Y('score:Q', stack=None, title='Score'),
    alt.Color('used_llm:N'),  # Color by LLM usage
    alt.XOffset("used_llm")
).properties(
    title='Distribution of Scores by Task and LLM Usage',
    width=400,
    height=300
)

# calculate the statsitical significance between used & not used llm task scores
# between and within subject
from scipy.stats import ttest_ind, ttest_rel
from scipy.stats import mannwhitneyu, wilcoxon
import statsmodels.formula.api as smf

# Between-subjects t-test (independent samples)
used_scores = task_scores[task_scores['used_llm'] == True]['score']
not_used_scores = task_scores[task_scores['used_llm'] == False]['score']
t_stat_ind, p_val_ind = ttest_ind(used_scores, not_used_scores, equal_var=False)
# Mann-Whitney U Test (between-subjects) - no need for exact match sample size
mw_stat, mw_p = mannwhitneyu(used_scores, not_used_scores, alternative='two-sided')

# Within-subjects t-test (paired samples)
# pivot the table to get scores for each user for used and not used LLM conditions
pivot_df = task_scores.pivot_table(index=['andrewid', 'task'], columns='used_llm', values='score').dropna()
pivot_df.columns = ['not_used', 'used'] if False in pivot_df.columns else ['used', 'not_used']
t_stat_rel, p_val_rel = ttest_rel(pivot_df['used'], pivot_df['not_used'])

# Wilcoxon Signed-Rank Test (within-subjects), requires matched pairs and no zero-differences
valid_pairs = pivot_df.dropna()
if not valid_pairs.empty and (valid_pairs['used'] != valid_pairs['not_used']).any():
    wilcoxon_stat, wilcoxon_p = wilcoxon(valid_pairs['used'], valid_pairs['not_used'])
else:
    wilcoxon_stat, wilcoxon_p = (None, None)

{
    "t_ind_between_subjects": {"t_statistic": t_stat_ind, "p_value": p_val_ind},
    "t_paired_within_subjects": {"t_statistic": t_stat_rel, "p_value": p_val_rel},
    "mann_whitney_u_test": {"statistic": mw_stat, "p_value": mw_p},
    "wilcoxon_test": {"statistic": wilcoxon_stat, "p_value": wilcoxon_p},
}


# Mixed-effects model: score ~ used_llm + (1 | andrewid)
# Encode 'used_llm' as int for modeling
task_scores['used_llm_int'] = task_scores['used_llm'].astype(int)
# Fit model
mixed_model = smf.mixedlm("score ~ used_llm_int", task_scores, groups=task_scores["andrewid"]).fit()

mixed_model.summary()

# Merge with the original task_scores DataFrame
merged_df = task_scores.merge(hw0_postsurvey_score, on='andrewid', how='left')
merged_df.head()

merged_df = merged_df[merged_df['task'].isin(['t1_data_clean', 't2_eda'])]

merged_df.head()

# Mapping from task names to relevant score columns
task_to_columns = {
    "t1_data_clean": {
        "difficulty": "perceived_difficulty_data_cleaning_score",
        "success": "perceived_success_data_cleaning_score"
    },
    "t2_eda": {
        "difficulty": "perceived_difficulty_eda_score",
        "success": "perceived_success_eda_score"
    },
    # "t3_ml": {
    #     "difficulty": "perceived_difficulty_ml_score",
    #     "success": "perceived_success_ml_score"
    # },
    # "t4_data_story": {
    #     "difficulty": "perceived_difficulty_data_storytelling_score",
    #     "success": "perceived_success_data_storytelling_score"
    # }
}

# Create new columns for difficulty and success scores based on task
merged_df['perceived_difficulty'] = merged_df.apply(
    lambda row: row[task_to_columns[row['task']]['difficulty']], axis=1)
merged_df['perceived_success'] = merged_df.apply(
    lambda row: row[task_to_columns[row['task']]['success']], axis=1)

# Mixed-effects model: score ~ used_llm + perceived_difficulty + perceived_success + (1 | andrewid)
model_formula = "score ~ used_llm_int + perceived_difficulty + perceived_success"
mixed_model_ext = smf.mixedlm(model_formula, merged_df, groups=merged_df["andrewid"]).fit()

mixed_model_ext.summary()

presurvey_score.to_csv("presurvey_score.csv")
merged_df = task_scores.merge(presurvey_score, on='andrewid', how='left')

presurvey_score.columns

# Model 1: Overall perceived expertise
model_overall_formula = (
    "score ~ used_llm_int + year_experience_python_score + "
    "perceived_expertise_llm_score + perceived_expertise_communication_score + "
    "perceived_expertise_data_science_score"
)
# model_overall_formula = (
#     "score ~ used_llm_int + year_experience_python_score + " # perceived_expertise_programming_score
#     "perceived_expertise_llm_score + year_experience_excel_score + " # perceived_expertise_communication_score
#     "perceived_expertise_data_science_score"
# )
model_overall = smf.mixedlm(model_overall_formula, merged_df, groups=merged_df["andrewid"]).fit()

# Model 2: Task-specific perceived expertise (based on task)
task_specific_expertise_map = {
    "t1_data_clean": "perceived_expertise_data_cleaning_score",
    "t2_eda": "perceived_expertise_eda_score",
    "t3_ml": "perceived_expertise_ml_score",
    "t4_data_story": "perceived_expertise_data_storytelling_score"
}

# Add a task-specific expertise column
merged_df["task_specific_expertise"] = merged_df.apply(
    lambda row: row[task_specific_expertise_map[row["task"]]], axis=1
)

# Build model with task-specific expertise
model_task_specific_formula = "score ~ used_llm_int + task_specific_expertise"
model_task_specific = smf.mixedlm(model_task_specific_formula, merged_df, groups=merged_df["andrewid"]).fit()

model_overall.summary()



model_task_specific.summary()