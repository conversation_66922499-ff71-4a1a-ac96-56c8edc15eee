prepostsurvey_mapping = {
    # perceived expertise
    "perceived_expertise_programming": "How would you assess your level of expertise in the following areas: [Programming]",
    "perceived_expertise_communication": "How would you assess your level of expertise in the following areas: [Communication]",
    "perceived_expertise_llm": "How would you assess your level of expertise in the following areas: [GenAI/LLM]",
    "perceived_expertise_llm_communication": "Please rate the following in terms of how much you agree or disagree with each statement. [I am able to have LLMs complete the tasks I instruct.]",
    "perceived_expertise_data_cleaning": "How would you assess your level of expertise in the following areas: [Data cleaning]",
    "perceived_expertise_eda": "How would you assess your level of expertise in the following areas: [Exploratory data analysis]",
    "perceived_expertise_ml": "How would you assess your level of expertise in the following areas: [Machine learning]",
    "perceived_expertise_data_storytelling": "How would you assess your level of expertise in the following areas: [Data-driven storytelling]",
    "perceived_expertise_data_car": "How would you assess your level of expertise in the following areas: [Car industry]",
    "perceived_expertise_data_videogame": "How would you assess your level of expertise in the following areas: [Video game]",
    # confidence in self abilities
    "confidence_programming": "How confident are you in your ability to do the following tasks on your own? [Programming]",
    "confidence_communication": "Please rate the following in terms of how much you agree or disagree with each statement. [I am confident in my communication ability in general.]",
    "confidence_llm": "Please rate the following in terms of how much you agree or disagree with each statement. [I am confident in my ability to use GenAI/LLMs.]",
    "confidence_llm_communication": "Please rate the following in terms of how much you agree or disagree with each statement. [I am confident in my ability to communicate to GenAI/LLM so that it does what I want.]",
    "confidence_data_cleaning": "How confident are you in your ability to do the following tasks on your own? [Data cleaning]",
    "confidence_eda": "How confident are you in your ability to do the following tasks on your own? [Exploratory data analysis]",
    "confidence_ml": "How confident are you in your ability to do the following tasks on your own? [Machine learning]",
    "confidence_data_storytelling": "How confident are you in your ability to do the following tasks on your own? [Data-driven storytelling]",
    # confidence & trust in AI
    "trust_llm": "Please rate the following in terms of how much you agree or disagree with each statement. [Generally, I trust GenAI/LLM.]",
    "confidence_llm_programming": "How confident are you in GenAI/LLM's ability to do the following tasks? [Programming]",
    "confidence_llm_data_cleaning": "How confident are you in GenAI/LLM's ability to do the following tasks? [Data cleaning]",
    "confidence_llm_eda": "How confident are you in GenAI/LLM's ability to do the following tasks? [Exploratory data analysis]",
    "confidence_llm_ml": "How confident are you in GenAI/LLM's ability to do the following tasks? [Machine learning]",
    "confidence_llm_data_storytelling": "How confident are you in GenAI/LLM's ability to do the following tasks? [Data-driven storytelling]",
    # interest
    "interest_programming": "How would you assess your level of interest in the following areas: [Programming]",
    "interest_communication": "How would you assess your level of interest in the following areas: [Communication]",
    "interest_llm": "How would you assess your level of interest in the following areas: [GenAI/LLM]",
    "interest_data_cleaning": "How would you assess your level of interest in the following areas: [Data cleaning]",
    "interest_eda": "How would you assess your level of interest in the following areas: [Exploratory data analysis]",
    "interest_ml": "How would you assess your level of interest in the following areas: [Machine learning]",
    "interest_data_storytelling": "How would you assess your level of interest in the following areas: [Data-driven storytelling]",
    "interest_data_car": "How would you assess your level of interest in the following areas: [Car industry]",
    "interest_data_videogame": "How would you assess your level of interest in the following areas: [Video game]",
    
}

presurvey_mapping = {
    # expertise approximations
    "year_experience_excel": "How many years of experience do you have in using the following tools? [Excel]",
    "year_experience_python": "How many years of experience do you have in using the following tools? [Python]",
    "year_experience_jupyter/colab": "How many years of experience do you have in using the following tools? [Jupyter/Colab notebook]",
    "year_experience_stats": "How many years of experience do you have in using the following tools? [STATS]",
    "year_experience_r": "How many years of experience do you have in using the following tools? [R]",
    "year_experience_pandas": "How many years of experience do you have in using the following tools? [pandas]",
    "year_experience_altair": "How many years of experience do you have in using the following tools? [altair]",
    "year_experience_sklearn": "How many years of experience do you have in using the following tools? [scikit-learn]",
    # use genai frequency
    "frequency_llm_general": "How frequently do you use GenAI/LLM for: [In general]",
    "frequency_llm_programming": "How frequently do you use GenAI/LLM for: [Programming]",
    "frequency_llm_communication": "How frequently do you use GenAI/LLM for: [Communication]",
    "frequency_llm_data_cleaning": "How frequently do you use GenAI/LLM for: [Data cleaning]",
    "frequency_llm_eda": "How frequently do you use GenAI/LLM for: [Exploratory data analysis]",
    "frequency_llm_ml": "How frequently do you use GenAI/LLM for: [Machine learning]",
    "frequency_llm_data_storytelling": "How frequently do you use GenAI/LLM for: [Data-driven storytelling]",
    # demographics
    "age": "How old are you?",
    "gender": "What's your gender?",
    "race": "Choose one or more races you consider yourself to be",
    "first_language": "What's your first language?",
    "major": "What major did you study for undergraduate/college?",
    "background": "What's your background before you join CMU?\n(You can copy/paste the line of your background introduction in the #social channel of slack)",
    "program": 'What program are you studying now at CMU?',
    # free response
    "class_expectation": 'What do you want to learn from this class? Anything else you want us to know about?',
}


postsurvey_mapping = {
    # free response questions
    "challenge_used_llm": 'What was challenging for you during the tasks when you used GenAI?',
    "llm_use": 'How did you use GenAI during the tasks?',
    "llm_helpful": 'Did you find using GenAI helpful during the tasks? Why or why not?',
    "help_needed": 'What do you think could help you further improve your performance with GenAI on these tasks?',
    # learning & general comment
    "class_learning": 'What did you learn from this class? What do you wish that you could learn more about?',
    "comment": 'Anything else you want us to know about?'
}

hw0_postsurvey_mapping = {
    # perceived success: How successful were you in performing the four tasks of HW0? How satisfied were you with your performance? [Part 1. Data cleaning]
    "perceived_success_data_cleaning": "How successful were you in performing the four tasks of HW0? How satisfied were you with your performance? [Part 1. Data cleaning]",
    "perceived_success_eda": "How successful were you in performing the four tasks of HW0? How satisfied were you with your performance? [Part 2. Exploratory data analysis]",
    "perceived_success_ml": "How successful were you in performing the four tasks of HW0? How satisfied were you with your performance? [Part 3. Machine learning]",
    "perceived_success_data_storytelling": "How successful were you in performing the four tasks of HW0? How satisfied were you with your performance? [Part 4. Data-driven storytelling]",

    # perceived stress: How irritated, stressed, and annoyed did you feel during the four tasks of HW0? [Part 1. Data cleaning]
    "perceived_stress_data_cleaning": "How irritated, stressed, and annoyed did you feel during the four tasks of HW0? [Part 1. Data cleaning]",
    "perceived_stress_eda": "How irritated, stressed, and annoyed did you feel during the four tasks of HW0? [Part 2. Exploratory data analysis]",
    "perceived_stress_ml": "How irritated, stressed, and annoyed did you feel during the four tasks of HW0? [Part 3. Machine learning]",
    "perceived_stress_data_storytelling": "How irritated, stressed, and annoyed did you feel during the four tasks of HW0? [Part 4. Data-driven storytelling]",

    # perceived difficulty: How mentally demanding/difficult were the four tasks of HW0? [Part 1. Data cleaning]
    "perceived_difficulty_data_cleaning": "How mentally demanding/difficult were the four tasks of HW0? [Part 1. Data cleaning]",
    "perceived_difficulty_eda": "How mentally demanding/difficult were the four tasks of HW0? [Part 2. Exploratory data analysis]",
    "perceived_difficulty_ml": "How mentally demanding/difficult were the four tasks of HW0? [Part 3. Machine learning]",
    "perceived_difficulty_data_storytelling": "How mentally demanding/difficult were the four tasks of HW0? [Part 4. Data-driven storytelling]",

    # perceived effort: How hard did you have to work for the four tasks of HW0? [Part 1. Data cleaning]
    "perceived_effort_data_cleaning": "How hard did you have to work for the four tasks of HW0? [Part 1. Data cleaning]",
    "perceived_effort_eda": "How hard did you have to work for the four tasks of HW0? [Part 2. Exploratory data analysis]",
    "perceived_effort_ml": "How hard did you have to work for the four tasks of HW0? [Part 3. Machine learning]",
    "perceived_effort_data_storytelling": "How hard did you have to work for the four tasks of HW0? [Part 4. Data-driven storytelling]",

    # perceived engagement: How interesting/engaging were the four tasks of HW0? [Part 1. Data cleaning]
    "perceived_engagement_data_cleaning": "How interesting/engaging were the four tasks of HW0? [Part 1. Data cleaning]",
    "perceived_engagement_eda": "How interesting/engaging were the four tasks of HW0? [Part 2. Exploratory data analysis]",
    "perceived_engagement_ml": "How interesting/engaging were the four tasks of HW0? [Part 3. Machine learning]",
    "perceived_engagement_data_storytelling": "How interesting/engaging were the four tasks of HW0? [Part 4. Data-driven storytelling]",

    # confidence: How confident are you that you did well on the four tasks of HW0? [Part 1. Data cleaning]
    "confidence_data_cleaning": "How confident are you that you did well on the four tasks of HW0? [Part 1. Data cleaning]",
    "confidence_eda": "How confident are you that you did well on the four tasks of HW0? [Part 2. Exploratory data analysis]",
    "confidence_ml": "How confident are you that you did well on the four tasks of HW0? [Part 3. Machine learning]",
    "confidence_data_storytelling": "How confident are you that you did well on the four tasks of HW0? [Part 4. Data-driven storytelling]",

    # helpseek: How much did you ask your peer for help for the four tasks of HW0? [Part 1. Data cleaning]
    "helpseek_data_cleaning": "How much did you ask your peer for help for the four tasks of HW0? [Part 1. Data cleaning]",
    "helpseek_eda": "How much did you ask your peer for help for the four tasks of HW0? [Part 2. Exploratory data analysis]",
    "helpseek_ml": "How much did you ask your peer for help for the four tasks of HW0? [Part 3. Machine learning]",
    "helpseek_data_storytelling": "How much did you ask your peer for help for the four tasks of HW0? [Part 4. Data-driven storytelling]",

    # task info
    "task_used_llm": "Which tasks did you do with GenAI?",
    "task_no_llm": "Which tasks did you do without GenAI?",
    "used_llm": "Which GenAI did you use for the tasks?",

    # free response questions
    "challenge_no_llm": "What was challenging for you during the tasks when you could not use GenAI?",
    "help_peer": 'What help did you offer to or receive from peers? What questions do you ask them instead of Google or GenAI?',
}

hws_postsurvey_mapping = {
    # 'How successful were you in performing the task? How satisfied were you with your performance? [Part 1. Data cleaning]',
    "perceived_success_data_cleaning": "How successful were you in performing the task? How satisfied were you with your performance? [Part 1. Data cleaning]",
    "perceived_success_eda": "How successful were you in performing the task? How satisfied were you with your performance? [Part 2. Exploratory data analysis]",
    "perceived_success_ml": "How successful were you in performing the task? How satisfied were you with your performance? [Part 3. Machine learning]",
    "perceived_success_data_storytelling": "How successful were you in performing the task? How satisfied were you with your performance? [Part 4. Data-driven storytelling]",

    # 'How mentally demanding/difficult were the task? [Part 1. Data cleaning]',
    "perceived_difficulty_data_cleaning": "How mentally demanding/difficult was the task? [Part 1. Data cleaning]",
    "perceived_difficulty_eda": "How mentally demanding/difficult was the task? [Part 2. Exploratory data analysis]",
    "perceived_difficulty_ml": "How mentally demanding/difficult was the task? [Part 3. Machine learning]",
    "perceived_difficulty_data_storytelling": "How mentally demanding/difficult was the task? [Part 4. Data-driven storytelling]",

    # 'How hard did you have to work for the task? [Part 1. Data cleaning]',
    "perceived_effort_data_cleaning": "How hard did you have to work for the task? [Part 1. Data cleaning]",
    "perceived_effort_eda": "How hard did you have to work for the task? [Part 2. Exploratory data analysis]",
    "perceived_effort_ml": "How hard did you have to work for the task? [Part 3. Machine learning]",
    "perceived_effort_data_storytelling": "How hard did you have to work for the task? [Part 4. Data-driven storytelling]",

    # 'How irritated, stressed, and annoyed did you feel during the task? [Part 1. Data cleaning]',
    "perceived_stress_data_cleaning": "How irritated, stressed, and annoyed did you feel during the task? [Part 1. Data cleaning]",
    "perceived_stress_eda": "How irritated, stressed, and annoyed did you feel during the task? [Part 2. Exploratory data analysis]",
    "perceived_stress_ml": "How irritated, stressed, and annoyed did you feel during the task? [Part 3. Machine learning]",
    "perceived_stress_data_storytelling": "How irritated, stressed, and annoyed did you feel during the task? [Part 4. Data-driven storytelling]",

    # 'How interesting/engaging was the task? [Part 1. Data cleaning]',
    "perceived_engagement_data_cleaning": "How interesting/engaging was the task? [Part 1. Data cleaning]",
    "perceived_engagement_eda": "How interesting/engaging was the task? [Part 2. Exploratory data analysis]",
    "perceived_engagement_ml": "How interesting/engaging was the task? [Part 3. Machine learning]",
    "perceived_engagement_data_storytelling": "How interesting/engaging was the task? [Part 4. Data-driven storytelling]",

    # 'How confident are you that you did well on the task? [Part 1. Data cleaning]',
    "confidence_data_cleaning": "How confident are you that you did well on the task? [Part 1. Data cleaning]",
    "confidence_eda": "How confident are you that you did well on the task? [Part 2. Exploratory data analysis]",
    "confidence_ml": "How confident are you that you did well on the task? [Part 3. Machine learning]",
    "confidence_data_storytelling": "How confident are you that you did well on the task? [Part 4. Data-driven storytelling]",

    # 'How much did you ask your peer/classmate for help with the task? [Part 1. Data cleaning]',
    "helpseek_data_cleaning": "How much did you ask your peer/classmate for help with the task? [Part 1. Data cleaning]",
    "helpseek_eda": "How much did you ask your peer/classmate for help with the task? [Part 2. Exploratory data analysis]",
    "helpseek_ml": "How much did you ask your peer/classmate for help with the task? [Part 3. Machine learning]",
    "helpseek_data_storytelling": "How much did you ask your peer/classmate for help with the task? [Part 4. Data-driven storytelling]",

    # other questions
    "used_llm": "What GenAI did you use for this homework?",
    "challenge_used_llm": 'What was challenging for you during the tasks when you used GenAI?',
    "llm_use": 'How did you use GenAI during the tasks?',
    "llm_helpful": 'Did you find using GenAI helpful during the tasks? Why or why not?',
    "help_needed": 'What do you think could help you further improve your performance with GenAI on these tasks?',
    "help_peer": 'What help did you offer to or receive from peers/ classmates? What questions do you ask them instead of Google or GenAI?',
    "comment": 'Anything else you want to comment about this task or in general?',
}


### REVERSE MAPPINGs
presurvey_reverse_mapping = {v: k for k, v in (prepostsurvey_mapping | presurvey_mapping).items()}
hw0_postsurvey_reverse_mapping = {v: k for k, v in (postsurvey_mapping | hw0_postsurvey_mapping).items()}
hws_postsurvey_reverse_mapping = {v: k for k, v in (postsurvey_mapping | hws_postsurvey_mapping).items()}
postsurvey_reverse_mapping = {v: k for k, v in (prepostsurvey_mapping | postsurvey_mapping).items()}


# --- Define task categories used across multiple scales ---
task_categories = ["data_cleaning", "eda", "ml", "data_storytelling"]

# --- Define Likert scales ---
scales = {
    "perceived_success": {
            "Not successful/ satisfied at all": 1,
            "Slightly successful/ satisfied": 2,
            "Moderately successful/ satisfied": 3,
            "Very successful/ satisfied": 4,
            "Extremely successful/ satisfied": 5,
    },
    "perceived_difficulty": {
            "Not difficult at all": 1,
            "Slightly difficult": 2,
            "Moderately difficult": 3,
            "Very difficult": 4,
            "Extremely difficult": 5,
    },
    "perceived_effort": {
            "I do not need to work hard at all": 1,
            "Slightly effortful": 2,
            "Moderately effortful": 3,
            "Very effortful": 4,
            "I need to work extremely hard": 5,
    },
    "perceived_stress": {
            "Not frustrating at all": 1,
            "Slightly frustrating": 2,
            "Moderately frustrating": 3,
            "Very frustrating": 4,
            "Extremely frustrating": 5,
    },
    "perceived_engagement": {
            "Not interesting at all": 1,
            "Slightly interesting": 2,
            "Moderately interesting": 3,
            "Very interesting": 4,
            "Extremely interesting": 5,
    },
    "helpseek": {
            "Not at all": 1,
            "A little bit": 2,
            "Moderately": 3,
            "Very much": 4,
            "All the time": 5,
    },
    "confidence": {
            "Not confident at all": 1,
            "Slightly confident": 2,
            "Moderately confident": 3,
            "Very confident": 4,
            "Extremely confident": 5,
    },
    "agreement": {
            "Strongly disagree": 1,
            "Somewhat disagree": 2,
            "Neither agree or disagree": 3,
            "Somewhat agree": 4,
            "Strongly agree": 5,
    },
    "interest": {
            "Not interested at all": 1,
            "Slightly interested": 2,
            "Moderately interested": 3,
            "Very interested": 4,
            "Extremely interested": 5,
    },
    "frequency": {
            "Never": 1,
            "Rarely": 2,
            "Monthly": 3,
            "Weekly": 4,
            "Daily": 5,
    },
    "year_experience": {
            "0": 1, "1-2": 2, "3-5": 3, "6-10": 4, "10+": 5
    },
    "expertise": {
            "Novice (not familiar at all)": 1,
            "Beginner (slightly familiar)": 2,
            "Intermediate (moderately familiar)": 3,
            "Advanced (very familiar)": 4,
            "Expert (extremely familiar)": 5,
    }
}

# --- Define columns to map for each scale using task_categories ---
column_groups = {
    "perceived_success": [f"perceived_success_{t}" for t in task_categories],
    "perceived_difficulty": [f"perceived_difficulty_{t}" for t in task_categories],
    "perceived_effort": [f"perceived_effort_{t}" for t in task_categories],
    "perceived_stress": [f"perceived_stress_{t}" for t in task_categories],
    "perceived_engagement": [f"perceived_engagement_{t}" for t in task_categories],
    "helpseek": [f"helpseek_{t}" for t in task_categories],
    "confidence": ["confidence_programming"] + [f"confidence_{t}" for t in task_categories],
    "agreement": ["perceived_expertise_llm_communication", "confidence_communication", "confidence_llm", "confidence_llm_communication", "trust_llm"],
    "interest": ["interest_programming", "interest_communication", "interest_llm"] + [f"interest_{t}" for t in task_categories] + ["interest_data_car", "interest_data_videogame"],
    "frequency": ["frequency_llm_general", "frequency_llm_programming", "frequency_llm_communication"] + [f"frequency_llm_{t}" for t in task_categories],
    "year_experience": ["year_experience_excel", "year_experience_python", "year_experience_jupyter/colab", "year_experience_stats", "year_experience_r", "year_experience_pandas", "year_experience_altair", "year_experience_sklearn"],
    "expertise": ["perceived_expertise_programming", "perceived_expertise_communication", "perceived_expertise_llm"] + [f"perceived_expertise_{t}" for t in task_categories],
}


### HW PATHS & IDs

andrew_id_list = ["aafonja", "usa", "barora", "abhimava", "rupalc", "achavess", "ishengc", "alexding", "sijiaf", "gsumukh", "jgu2", "elianah", "radhikaj", "sjashnan", "suhailk", "rkkim", "elakra", "mlal", "wenyili", "gracelia", "charlenl", "nmedaram", "somyameh", "sohammon", "mnandiga", "anshp", "hpasha", "kqasba", "ramkausr", "krastogi", "ssagar2", "vsakhark", "agshaik", "naxs", "ankitshu", "tsohani", "csoska", "ashwin2", "yiwentan", "dtayal", "etripath", "kviknesh", "nivedity", "hyanaman"]
name_list = ["Afonja, Aminat", "Ahuja, Upasna", "Arora, Bhavya", "Bhimavarapu, Aditya Teja", "Chauhan, Rupal", "Chaves Solis, Angelica", "Chen, I-Sheng (Eason)", "Ding, Alex", "Fan, Caroline", "Gadavilli, Sumukh", "Gu, Jonathan", "Huang, Eliana", "Jain, Radhika", "Jashnani, Shriya", "Khan, Suhail", "Kim, Roni", "Lakra, Esha", "Lal, Mukul", "Li, Wenyi", "Liao, Grace", "Lin, Charlene", "Medarametla, Nikhil", "Mehta, Somya", "Mondal, Soham", "Nandigama, Manasa Reddy", "Pandey, Ansh", "Pasha, Hassaan", "Qasba, Karan", "Ramalingan, Ram Kaushik", "Rastogi, Kritika", "Sagar, Siddhant", "Sakharkar, Vrushal", "Shaik, Abdul", "Sharma, Nax", "Shukla, Ankit", "Sohani, Tejas", "Soska, Cody", "Swaminathan, Ashwin", "Tan, Yi Wen", "Tayal, Devisha", "Tripathi, Eishita", "Viknesh, Khanishkaa", "Yadav, Nivedita", "Yanamandra, Himakar"]

name_string_list = [name.lower().replace(" ", "").replace(",", "").replace("-", "").replace("(", "").replace(")", "") for name in name_list]
name2andrew = dict(zip(name_string_list, andrew_id_list))

hw0_submission_path = "/gdrive/MyDrive/PhD/lab/DSPM/DSPM Instructor Materials/HW0/hw0_submission_formatted/"
hw1_submission_path = "/gdrive/MyDrive/PhD/lab/DSPM/DSPM Instructor Materials/HW1-4/hw1_submission_formatted/"
hw2_submission_path = "/gdrive/MyDrive/PhD/lab/DSPM/DSPM Instructor Materials/HW1-4/hw2_submission_formatted/"
hw3_submission_path = "/gdrive/MyDrive/PhD/lab/DSPM/DSPM Instructor Materials/HW1-4/hw3_submission_formatted/"
hw4_submission_path = "/gdrive/MyDrive/PhD/lab/DSPM/DSPM Instructor Materials/HW1-4/hw4_submission_formatted/"
think_aloud_hw2_path = "/gdrive/MyDrive/PhD/lab/DSPM/DSPM Instructor Materials/HW1-4/hw1-4_think_alouds/hw2/"
think_aloud_hw3_path = "/gdrive/MyDrive/PhD/lab/DSPM/DSPM Instructor Materials/HW1-4/hw1-4_think_alouds/hw3/"
think_aloud_hw4_path = "/gdrive/MyDrive/PhD/lab/DSPM/DSPM Instructor Materials/HW1-4/hw1-4_think_alouds/hw4/"

hw_path = {"hw0": hw0_submission_path, "hw1": hw1_submission_path,
           "hw2": hw2_submission_path, "hw3": hw3_submission_path,
           "hw4": hw4_submission_path,
           "think_aloud_hw2": think_aloud_hw2_path,
           "think_aloud_hw3": think_aloud_hw3_path,
           "think_aloud_hw4": think_aloud_hw4_path}


### HW INSTRUCTIONS

hw0_instructions = """
# %% [markdown]
**[Insert your Name].**
Data Science for Product Managers. Spring 2025.

Save a copy of the notebook in your Drive, name it as `DSPM-HW0.ipynb`

Start a Zoom recording for your work when you're working on HW0 during lecture and paste the recording link to the post-survey.

Refer to the homework setup instruction here: https://docs.google.com/document/d/13SlSQE-2sYAcRjYcylJFMSo3jI1ei4ON_okzjzj59ZY/edit?usp=sharing
# %%
# @title
print("Unique identifier for HW0 Tampermonkey script, do not modify or delete!")
# %% [markdown]
# Task
You're a product manager of the Honda Accord team, and the company is aiming to launch an online car price valuation tool to help buyers and sellers of used and new Honda Accord cars.
Your team have collected a dataset for you by manually searching for 2008 to 2022 Honda Accords for sale in the United States. You'll analyze the data to gather some insights to present to your team and guide product development.
# %%
import numpy as np
import pandas as pd
import altair as alt
# %%
df_cars = pd.read_csv("https://docs.google.com/uc?id=1mQt7hP9LgT-7W61VaDd8iN32k6tZN4Y1&export=download")
# %%
# check out the first five rows of df_cars
df_cars.head()
# %%
# %% [markdown]
# Part 1.  Data Cleaning.

Fix columns with inconsistent formatting and prepare dataset for analysis. You may need to come back to do more data cleaning if the later analysis run into errors.

Follow the template below, use a markdown text cell to indicate each column you clean and describe each of the cleaning or processing of the data file that you did using comments in a code cell.
%% [markdown]
### Column 1: [Car Make]
%%

%% [markdown]
df_cars["Car Make"].unique()
%%
Write your cleaning steps here before your code
%% [markdown]
### Column 2: [Car Model]
%%
Write your cleaning steps here before your code
%% [markdown]
### Column 3: [Dealer or Individual?]
%%
Write your cleaning steps here before your code
%% [markdown]
### Column 4: [Price]
%%
Write your cleaning steps here before your code
%% [markdown]
### Column 5: [Year]
%%
Write your cleaning steps here before your code
%% [markdown]
# Part 2.  Exploratory Data Analysis (EDA)

Perform EDA to understand data distribution, relationships, and potential insights.
EDA should include looking at some summary statistics, recognizing any issues with the data or key insights, and visual inspection of important data columns.

Build preliminary understanding and try to think of what questions would be insightful or nuanced. Follow the template below, use a markdown text cell to indicate each question & your discovered insight, and use comments in code cells to explain how did you derive the insights.
Use multiple sources to support your insights whenever applied, such as both statistics (pandas or numpy) and visualization (Altair).
# %%
# If you haven't finished cleaning data above, use this dataset for the analysis below
df_cars_cleaned = pd.read_csv("https://docs.google.com/uc?id=16HrJkNhh6crldo4Uh8cvkG5PavJIEMEU&export=download")
df_cars_cleaned.info()
# %% [markdown]
### Q1: [question you want to ask about the dataset]
Insight: [your answer]
%%
statistics
%%
visualization
%% [markdown]
### Q2: [question you want to ask about the dataset]
Insight: [your answer]
%%
statistics
%%
visualization
%% [markdown]
### Q3: [question you want to ask about the dataset]
Insight: [your answer]
%%
statistics
%%
visualization
%% [markdown]
### Q4: [question you want to ask about the dataset]
Insight: [your answer]
%%
statistics
%%
visualization
%% [markdown]
### Q5: [question you want to ask about the dataset]
Insight: [your answer]
%%
statistics
%%
visualzation
%% [markdown]
# Part 3. Machine Learning Modeling

You think it can be a good idea to build a ML model for your product using your dataset.
You could use the scikit-learn python library to implement your models. Document/explain your process. Specifically,
compare and interpret model performance and feature importance when you use different features,
decide which is the best performing model.

Feel free to try more than one type of model or performance scores for bonus points.
%%

%% [markdown]
# Part 4. Data-Driven Storytelling and Reflection

Now, after all the data cleaning and analysis, it's time to put together your report to the team as the product manager. Describe what you learned about this dataset and how your insights are relevant to the product design. This section should contain visualizations and textual descriptions. Some questions that you can cover in your report include:

What features should exist for the product you're envisioning, as supported by the insights you discovered in the data?
What do your ML models' performance values mean? Why are there performance differences with different features?
What limitations exist for the data or the insights?
What can be improved in the data collection and analysis pipeline for better results next time?
%% [markdown]

%%

# %% [markdown]
# Submit your Homework
1. download `DSPMuserData-HW0.json` by clicking the `Download History` button
2. download this `DSPM-HW0.ipynb` notebook
3. zip the notebook and history files, name it as `andrewid.zip` and upload the zip to Canvas
4. complete the HW0 [post-survey](https://forms.gle/SZhGG7TTg8wHic7z8), and submit the link to Zoom recording in the survey.
"""

hw1_instructions = '''
"""
**[Insert your Name].**
Data Science for Product Managers. Spring 2025.

Save a copy of the notebook in your Drive, name it as `DSPM-HW1.ipynb`

Update the installed script here by clicking `Reinstall version 2025-03-18`: https://greasyfork.org/en/scripts/527667-colab-save-history
- Because of our data storage mechanism in the backend, we have enabled the functionality to periodically save your data when we suspect information will be lost.
- **This means, whenever you see an auto pop-up of downloading history, please download and save that json file.** Old history will be cleared afterwards, so please be sure to **save** and do not click cancel.
- However, please still remember to manually download a final version when you finish! In submission, please make sure you upload all the history files downloaded.

Notes:
* You can freely use Google and the built-in Gemini in the Colab notebook environment with the script.
* Do not use other GenAI tools such as ChatGPT.
* Do not open multiple tabs or make multiple copies of the same homework file.
* You can earn bonus point if you sign up for a 60-90min think aloud interview for your homework. Slack DM `Christina Ma` to join the queue.
"""

# @title
print("Unique identifier for HW1 Tampermonkey script, do not modify or delete!")

"""# Task
You're a product manager of Steam, a video game distribution platform, and the company is aiming to launch an analytics platform for game developers that provides insights into market trends. Your team have collected a dataset for you by scraping and manually added some popular games on Steam since 2000. You'll be using what you learned in DSPM to analyze the data to gather some insights to present to your team and guide product development.
"""

import numpy as np
import pandas as pd
import altair as alt

df_games = pd.read_csv("https://docs.google.com/uc?id=1rNnE9dBQabrSNLeELbJr1nxfv5c28d3A&export=download")

# check out the first five rows of df_cars
df_games.head()

"""# Part 1.  Data Cleaning.

Fix columns with inconsistent formatting and prepare dataset for analysis. You may need to come back to do more data cleaning if the later analysis run into errors.

Follow the template below, use a markdown text cell to indicate each column you clean and describe each of the cleaning or processing of the data file that you did using comments in a code cell.

### Column 1: [INSERT COLUMN NAME]
"""

# Write your cleaning steps here before your code

"""### Column 2: [INSERT COLUMN NAME]"""

# Write your cleaning steps here before your code

"""### Column 3: [INSERT COLUMN NAME]"""

# Write your cleaning steps here before your code

"""### Column 4: [INSERT COLUMN NAME]"""

# Write your cleaning steps here before your code

"""### Column 5: [INSERT COLUMN NAME]"""

# Write your cleaning steps here before your code

"""# Submit your Homework
1. download `DSPMuserData-HW1.json` by clicking the `Download History` button
2. download this `DSPM-HW1.ipynb` notebook
3. zip the notebook and history (all of them, if you're prompted to download automatically during your work) files.
4. Name your zip file as `andrewid.zip` and upload the zip to Canvas. Do not name it anything else.
5. complete the HW1 [post-survey](https://forms.gle/k8VffEuAaYXTBPkj9).
"""
'''

hw2_instructions = '''
"""
**[Insert your Name].**
Data Science for Product Managers. Spring 2025.

Save a copy of the notebook in your Drive, name it as `DSPM-HW2-{andrewid}.ipynb`

Notes:
* Whenever you see an auto pop-up of downloading history, please download and save that json file. Old history will be cleared afterwards, so please be sure to **save** and do not click cancel.
* You can freely use Google and the built-in Gemini in the Colab notebook environment with the script.
* Do not use other GenAI tools such as ChatGPT.
* Do not open multiple tabs or make multiple copies of the same homework file.
* You can earn bonus point if you sign up for a 60-90min think aloud interview for your homework: https://canvas.cmu.edu/courses/45491/assignments/831296
"""

# @title
print("Unique identifier for HW2 Tampermonkey script, do not modify or delete!")

"""# Task
You're a product manager of Steam, a video game distribution platform, and the company is aiming to launch an analytics platform for game developers that provides insights into market trends. Your team have collected a dataset for you by scraping and manually added some popular games on Steam since 2000. You'll be using what you learned in DSPM to analyze the data to gather some insights to present to your team and guide product development.
"""

import numpy as np
import pandas as pd
import altair as alt
import ast

# load a cleaned dataset
df_games = pd.read_csv("https://docs.google.com/uc?id=1DMlNi5sdOvkjUAvrgukixW9BRHJFywc3&export=download")
df_games['released_on'] = pd.to_datetime(df_games['released_on'])
df_games['developer'] = df_games['developer'].apply(ast.literal_eval)
df_games['genre'] = df_games['genre'].apply(ast.literal_eval)
df_games.info()

# check out the first five rows of df_games
df_games.head()

"""# Part 2.  Exploratory Data Analysis (EDA)

Perform EDA to understand data distribution, relationships, and potential insights.
EDA should include looking at some summary statistics, recognizing any issues with the data or key insights, and visual inspection of important data columns.

Build preliminary understanding and try to think of what questions would be insightful or nuanced. Follow the template below, use a markdown text cell to indicate each question & your discovered insight, and use comments in code cells to explain how did you derive the insights.
Use multiple sources to support your insights whenever applied, such as both statistics (pandas or numpy) and visualization (Altair).

### Q1: [question you want to ask about the dataset]
Insight: [your answer]
"""

# statistics

# visualization

"""### Q2: [question you want to ask about the dataset]
Insight: [your answer]
"""

# statistics

# visualization

"""### Q3: [question you want to ask about the dataset]
Insight: [your answer]
"""

# statistics

# visualization

"""### Q4: [question you want to ask about the dataset]
Insight: [your answer]
"""

# statistics

# visualization

"""### Q5: [question you want to ask about the dataset]
Insight: [your answer]
"""

# statistics

# visualzation

"""# Submit your Homework
1. download `DSPMuserData-HW2.json` by clicking the `Download History` button
2. download this `DSPM-HW2-{andrewid}.ipynb` notebook
3. zip the notebook and history (all of them, if you're prompted to download automatically during your work) files.
4. Name your zip file as `andrewid.zip` and upload the zip to Canvas. Do not name it anything else.
5. complete the HW2 [post-survey](https://forms.gle/6ToHXTGyRGhbNxEi7).
"""
'''

hw3_instructions = '''
"""
**[Insert your Name].**
Data Science for Product Managers. Spring 2025.

Save a copy of the notebook in your Drive, name it as `DSPM-HW3-{andrewid}.ipynb`

Notes:
* Whenever you see an auto pop-up of downloading history, please download and save that json file. Old history will be cleared afterwards, so please be sure to **save** and do not click cancel.
* You can freely use Google and the built-in Gemini in the Colab notebook environment with the script.
* Do not use other GenAI tools such as ChatGPT.
* Do not open multiple tabs or make multiple copies of the same homework file.
"""

# @title
print("Unique identifier for HW3 Tampermonkey script, do not modify or delete!")

"""# Task
You're a product manager of Steam, a video game distribution platform, and the company is aiming to launch an analytics platform for game developers that provides insights into market trends. Your team have collected a dataset for you by scraping and manually added some popular games on Steam since 2000. You'll be using what you learned in DSPM to analyze the data to gather some insights to present to your team and guide product development.
"""

import numpy as np
import pandas as pd
import altair as alt
import ast

# load a cleaned dataset
df_games = pd.read_csv("https://docs.google.com/uc?id=1DMlNi5sdOvkjUAvrgukixW9BRHJFywc3&export=download")
df_games['released_on'] = pd.to_datetime(df_games['released_on'])
df_games['developer'] = df_games['developer'].apply(ast.literal_eval)
df_games['genre'] = df_games['genre'].apply(ast.literal_eval)
df_games.info()

# check out the first five rows of df_games
df_games.head()

"""# Part 3. Machine Learning Modeling

You think it can be a good idea to build a ML model for your product using your dataset.
You could use the scikit-learn python library to implement your models. Document/explain your process. Specifically,
compare and interpret model performance and feature importance when you use different features,
decide which is the best performing model.

Feel free to try more than one type of model or performance scores for bonus points.
"""


"""# Submit your Homework
1. download `DSPMuserData-HW3.json` by clicking the `Download History` button
2. download this `DSPM-HW3-{andrewid}.ipynb` notebook
3. zip the notebook and history (all of them, if you're prompted to download automatically during your work) files.
4. Name your zip file as `andrewid.zip` and upload the zip to Canvas. Do not name it anything else.
5. complete the HW3 [post-survey](https://forms.gle/xCDFpXtdrNxhvRdF6).
"""
'''

hw4_instructions = '''
"""

**[Insert your Name].**
Data Science for Product Managers. Spring 2025.

Save a copy of the notebook in your Drive, name it as `DSPM-HW4-{andrewid}.ipynb`

Notes:
* Whenever you see an auto pop-up of downloading history, please download and save that json file. Old history will be cleared afterwards, so please be sure to **save** and do not click cancel.
* You can freely use Google and the built-in Gemini in the Colab notebook environment with the script.
* Do not use other GenAI tools such as ChatGPT.
* Do not open multiple tabs or make multiple copies of the same homework file.
"""

# @title
print("Unique identifier for HW4 Tampermonkey script, do not modify or delete!")

"""# Task
You're a product manager of Steam, a video game distribution platform, and the company is aiming to launch an analytics platform for game developers that provides insights into market trends. Your team have collected a dataset for you by scraping and manually added some popular games on Steam since 2000. You'll be using what you learned in DSPM to analyze the data to gather some insights to present to your team and guide product development.
"""

import numpy as np
import pandas as pd
import altair as alt
import ast

# load a cleaned dataset
df_games = pd.read_csv("https://docs.google.com/uc?id=1DMlNi5sdOvkjUAvrgukixW9BRHJFywc3&export=download")
df_games['released_on'] = pd.to_datetime(df_games['released_on'])
df_games['developer'] = df_games['developer'].apply(ast.literal_eval)
df_games['genre'] = df_games['genre'].apply(ast.literal_eval)
df_games.info()

# check out the first five rows of df_games
df_games.head()

"""# Part 4. Data-Driven Storytelling and Reflection

Now, after all the data cleaning and analysis, it's time to put together your report to the team as the product manager. Describe what you learned about this dataset and how your insights are relevant to the product design. This section should contain visualizations and textual descriptions. Some questions that you can cover in your report include:

What features should exist for the product you're envisioning, as supported by the insights you discovered in the data?
What do your ML models' performance values mean? Why are there performance differences with different features?
What limitations exist for the data or the insights?
What can be improved in the data collection and analysis pipeline for better results next time?
"""



"""# Submit your Homework
1. download `DSPMuserData-HW4.json` by clicking the `Download History` button
2. download this `DSPM-HW4-{andrewid}.ipynb` notebook
3. zip the notebook and history (all of them, if you're prompted to download automatically during your work) files.
4. Name your zip file as `andrewid.zip` and upload the zip to Canvas. Do not name it anything else.
5. complete the HW4 [post-survey](https://forms.gle/QUu31eSQXSGdDCUU8).
"""
'''

hw_instr = {"hw0": hw0_instructions, "hw1": hw1_instructions, "hw2": hw2_instructions, "hw3": hw3_instructions, "hw4": hw4_instructions}



# System instruction for prompt tagging
SYSTEM_PROMPT = """You are an expert in analyzing user prompts for LLM-powered notebook.
Given a user input, return tags describing the intent and execution clarity.
Use this output format:
intent tag string, [execution clarity tag strings]

Intent tags (choose one):
- direct_request
- implied_request
- no_intent
- conceptual_question (if the prompt can be answered without context, and no actions need to be performed)

Execution clarity tags (choose one for each if not conceptual_question):
- no_verb, vague_verb, clear_verb (whether what should the LLM do is clear, clear_verb if prompt included execution details such as what specific statistics or visualizations to perform)
- no_object, vague_object, clear_object (whether what should the LLM perform the action on is clear, usually vague_object if using it/this/the above, etc.)


Examples:
* “I wrote this code df_cars["Car Make"].unique()”
  - Output: "no_intent", ["no_verb", "no_object"]
* “give me stats for my question”
  - Reasoning: need to infer what `stats` is needed (vague_verb) and what's `my question` (vague_object);
  - Output: "direct_request", ["vague_verb", "vague_object"]
* “now help with part 3 of my assignment”
  - Reasoning: need to infer what help is needed for verb;
  - Output: "direct_request", ["vague_verb", "clear_object"]
* “fill in empty rows with "Honda" keyword”
  - Reasoning: clear on how data cleaning should be performed, but vague on which column should be filled;
  - Output: "direct_request", ["clear_verb", "vague_object"]
* “how can i identify a column from a table?”
  - Reasoning: can answer without context;
  - Output: "conceptual_question", []
* “can u give me the new code”
  - Reasoning: unclear what's the action for new code, missing context on what's the new code operating on
  - Output: "direct_request", ["vague_verb", "no_object"]
* “how many cars were sold in 2014?”
  - Reasoning: need to infer the implied request - answer this question with the dataset;
  - Output: "implied_request", ["no_verb", "no_object"]
* “whats the correct code”
  - Reasoning: need to infer the implied request - debug the code, give me the correct code;
  - Output: "implied_request", ["no_verb", "vague_object"]
* “How about visualization? what kind of visualization is more suitable for this insight.”
  - Reasoning: need to infer the implied request - give me the most suitable visualization for this insight, also unclear what's `this insight`;
  - Output: "implied_request", ["no_verb", "vague_object"]
* “how do I clean my data?“
  - Output: "conceptual_question", []
* “How can I build an ML model“:
  - Output: "conceptual_question", []
* "why is the data type for 'description' column still object when I have already run the code to change it to string?"
  - Output: "conceptual_question", []

Output only tag strings in lists without reasoning.
"""