<PERSON>tamp,Email <PERSON>dress,Name,perceived_expertise_programming,perceived_expertise_communication,perceived_expertise_llm,perceived_expertise_data_cleaning,perceived_expertise_eda,perceived_expertise_ml,perceived_expertise_data_storytelling,perceived_expertise_data_car,perceived_expertise_data_videogame,interest_programming,interest_communication,interest_llm,interest_data_cleaning,interest_eda,interest_ml,interest_data_storytelling,interest_data_car,interest_data_videogame,confidence_programming,confidence_data_cleaning,confidence_eda,confidence_ml,confidence_data_storytelling,confidence_llm_programming,confidence_llm_data_cleaning,confidence_llm_eda,confidence_llm_ml,confidence_llm_data_storytelling,confidence_communication,confidence_llm,confidence_llm_communication,trust_llm,perceived_expertise_llm_communication,challenge_used_llm,llm_use,llm_helpful,help_needed,class_learning,comment,andrewid,confidence_programming_score,confidence_data_cleaning_score,confidence_eda_score,confidence_ml_score,confidence_data_storytelling_score,perceived_expertise_llm_communication_score,confidence_communication_score,confidence_llm_score,confidence_llm_communication_score,trust_llm_score,interest_programming_score,interest_communication_score,interest_llm_score,interest_data_cleaning_score,interest_eda_score,interest_ml_score,interest_data_storytelling_score,interest_data_car_score,interest_data_videogame_score,perceived_expertise_programming_score,perceived_expertise_communication_score,perceived_expertise_llm_score,perceived_expertise_data_cleaning_score,perceived_expertise_eda_score,perceived_expertise_ml_score,perceived_expertise_data_storytelling_score
4/21/2025 15:06:04,<EMAIL>,Bhavya Arora ,Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Novice (not familiar at all),Novice (not familiar at all),Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Not interested at all,Not interested at all,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Moderately confident,Strongly agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,It was difficult at the first but i slowly learned ,To write the python code,It was helpful to code and find bugs ,Learning some programming on my own ,I got comfortable with data. I would like to learn more in depth. ,,barora,2,2,2,2,2,4,5,4,4,4,4,4,4,4,4,4,4,1,1,2,2,2,2,2,2,2
4/21/2025 15:42:05,<EMAIL>,Rupal Chauhan,Intermediate (moderately familiar),Advanced (very familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Moderately interested,Very interested,Very interested,Slightly interested,Slightly interested,Very interested,Very interested,Slightly interested,Slightly interested,Moderately confident,Very confident,Moderately confident,Moderately confident,Moderately confident,Very confident,Very confident,Very confident,Very confident,Very confident,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Getting the right outcome,I used GenAI for writing codes during the assignment tasks,"Yes, It was very helpful as it helped me write code and helped fix error in my code.","Probably, a bit more clarity on the problem statement could help me further improve my performance with genAI on these tasks.","I learnt overall data analysis, reading data, understanding insights and storytelling. Also, How can we program to clean or analyze data.",,rupalc,3,4,3,3,3,4,4,4,4,4,3,4,4,2,2,4,4,2,2,3,4,2,3,3,3,3
4/21/2025 15:52:30,<EMAIL>,Ram Kaushik Ramalingan,Expert (extremely familiar),Expert (extremely familiar),Advanced (very familiar),Expert (extremely familiar),Expert (extremely familiar),Expert (extremely familiar),Expert (extremely familiar),Beginner (slightly familiar),Beginner (slightly familiar),Extremely interested,Extremely interested,Extremely interested,Very interested,Very interested,Extremely interested,Extremely interested,Moderately interested,Moderately interested,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Strongly agree,Strongly agree,Strongly agree,Somewhat agree,Strongly agree,Structuring prompts accordingly to get right results,"Used it to perform tasks that seemed repetitive, as well as with libraries I haven't used before",Very helpful. Saved coding time by a significant margin,Having clear rubric on what's expected from each assignment,Refreshed my Python knowledge.,,ramkausr,5,5,5,5,5,5,5,5,5,4,5,5,5,4,4,5,5,3,3,5,5,4,5,5,5,5
4/21/2025 17:05:02,<EMAIL>,Yi Wen Tan,Beginner (slightly familiar),Advanced (very familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Novice (not familiar at all),Moderately interested,Extremely interested,Very interested,Slightly interested,Very interested,Very interested,Very interested,Moderately interested,Not interested at all,Slightly confident,Slightly confident,Moderately confident,Moderately confident,Very confident,Very confident,Very confident,Moderately confident,Very confident,Slightly confident,Strongly agree,Neither agree or disagree,Somewhat agree,Neither agree or disagree,Somewhat agree,"The Gen AI sometimes could not troubleshoot the programming errors that I encountered. Like I know I needed to convert the object into string or integer data type, but somehow I just couldn't get it to work even using Gemini to troubleshoot.",I usually just write a prompt and edit the prompt accordingly to get Gemini to do what I needed to do.,"Yes, it was largely helpful, especially as I didn't have a programming background and I only knew the broad concepts of the python programming language.","A template on how to do the HW tasks would be very useful for non-technical students like me, since the lectures only taught the what and why, but the homework assignments were asking us to do the how, which we had not much idea on how to begin.","The concepts taught in the lectures were useful, but the assignments did not match the lectures. Instead of getting us to code, the assignments should have gotten us to design a data science problem and taught us how should a product manager tackle the problem.",The guest lecture was especially useful to contextualize how PMs make use of data science concepts in their work. I wish the class could have been centered around such product management specific case studies instead of teaching us the pure data science theory and concepts.,yiwentan,2,2,3,3,4,4,5,3,4,3,3,5,4,2,4,4,4,3,1,2,4,3,3,3,3,3
4/21/2025 17:27:32,<EMAIL>,Nivedita Yadav,Novice (not familiar at all),Advanced (very familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Not confident at all,Slightly confident,Moderately confident,Slightly confident,Slightly confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Strongly agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Getting rid of the errors initially ,Coming up with smart prompts ,Yes because it would give me the code and even explain it ,Not sure ,How to prompt Gen AI for programming related tasks ,,nivedity,1,2,3,2,2,4,5,4,4,4,4,4,4,4,4,4,4,4,4,1,4,2,2,2,2,2
4/22/2025 10:42:13,<EMAIL>,Upasna A,Intermediate (moderately familiar),Expert (extremely familiar),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Slightly interested,Extremely interested,Extremely interested,Moderately interested,Moderately interested,Moderately interested,Moderately interested,Moderately interested,Moderately interested,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Strongly agree,Somewhat agree,Somewhat agree,Strongly agree,Neither agree or disagree,Only if I were to have some practice or pre recorded videos of similar tasks I would not only be able to complete the task but also do it better and understand it better,For everything across all assignments ,"Yes, I wouldn’t have been able to complete my assignments had I not used AI","Just pre recorded videos, and how to powerfully use GenAI other than prompts","Theory was great all the concepts explained in the class were nice, since the topic is so important I could understanding it better through pre recorded videos to help doing the assignments better as they are hands on",,usa,1,1,1,1,1,3,5,4,4,5,2,5,5,3,3,3,3,3,3,3,5,1,1,1,1,1
4/22/2025 10:47:04,<EMAIL>,Ansh Pandey,Beginner (slightly familiar),Advanced (very familiar),Advanced (very familiar),Beginner (slightly familiar),Beginner (slightly familiar),Novice (not familiar at all),Intermediate (moderately familiar),Beginner (slightly familiar),Beginner (slightly familiar),Moderately interested,Moderately interested,Very interested,Not interested at all,Moderately interested,Very interested,Very interested,Moderately interested,Moderately interested,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Moderately confident,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Somewhat agree,Somewhat agree,Neither agree or disagree,Strongly disagree,Strongly disagree,"Debugging code generated by gemini- absolutely terrible, never ever going to use it","generate code, brainstorm to find most insightful plots for analytics PMs",nope- very bad at debugging,gemini could improve RAG implementation to reduce back and forth when prormpting,Better directions in the assignment could've guided efforts better,,anshp,1,1,1,1,3,1,4,4,3,1,3,3,4,1,3,4,4,3,3,2,4,4,2,2,1,3
4/22/2025 14:14:00,<EMAIL>,grace,Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Moderately interested,Moderately interested,Moderately interested,Moderately interested,Moderately interested,Moderately interested,Moderately interested,Slightly interested,Slightly interested,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Somewhat agree,Somewhat agree,Somewhat agree,Neither agree or disagree,Neither agree or disagree,Sometimes it doesn't quite give the right code,I would ask it to explain the instructions or help me implement code,Yes because it gave me somewhere to start,NA,NA,,gracelia,4,4,4,4,4,3,4,4,4,3,3,3,3,3,3,3,3,2,2,4,4,4,4,4,4,4
4/22/2025 15:22:13,<EMAIL>,Alex Ding,Intermediate (moderately familiar),Expert (extremely familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Moderately confident,Moderately confident,Slightly confident,Slightly confident,Very confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Somewhat agree,Strongly agree,Strongly agree,Somewhat agree,Strongly agree,clarify the specific area for GenAI to do the things,"asking for coding help, different model test, recommendation on ML step","Really helpful, saving lots of time finding useful code",Get more understanding for different model's presents and structure,"Basic Data clean, different type of data visionary, useful ML model, some use case study  ",,alexding,3,3,2,2,4,5,4,5,5,4,4,4,4,4,4,4,4,4,4,3,5,3,3,3,3,3
4/22/2025 16:13:47,<EMAIL>,Radhika Jain,Beginner (slightly familiar),Expert (extremely familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Novice (not familiar at all),Novice (not familiar at all),Moderately interested,Extremely interested,Extremely interested,Moderately interested,Extremely interested,Extremely interested,Extremely interested,Not interested at all,Not interested at all,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Very confident,Very confident,Very confident,Very confident,Very confident,Strongly agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,the one thing which Gemini could not do was identify the col name no matter how many times I wrote a hash(#) in front of it.  it would always make that mistake and I would have t correct it. Initially I never understood the error but once I understood I got the hang of it ,I used Gemini for almost all the assignments ,yes it was extremely helpful since I dont code as much it would not be possible for me to complete the assignment if gemini were not available ,if it could help me understand the errors a little better ,ya I wish this was a longer class with more in-depth data science work ,,radhikaj,2,2,2,2,2,4,5,4,4,4,3,5,5,3,5,5,5,1,1,2,5,3,3,3,3,3
4/22/2025 16:34:13,<EMAIL>,Manasa Nandigama,Beginner (slightly familiar),Advanced (very familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Very interested,Extremely interested,Extremely interested,Very interested,Very interested,Very interested,Very interested,Very interested,Slightly interested,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Strongly agree,Somewhat agree,Neither agree or disagree,Neither agree or disagree,Somewhat agree,Giving right prompts,"Asked for help whenever I was stuck, used for code generation","Yes, it helped me understand what to do",Giving right prompts ,"I learnt the basics of data cleaning, EDA, ML and story telling. It would've been good if I could learn it much deeper.",,mnandiga,3,3,3,3,3,4,5,4,3,3,4,5,5,4,4,4,4,4,2,2,4,3,2,2,2,2
4/22/2025 18:02:58,<EMAIL>,Vrushal Sakharkar,Intermediate (moderately familiar),Advanced (very familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Very interested,Extremely interested,Very interested,Very interested,Very interested,Very interested,Very interested,Moderately interested,Moderately interested,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Somewhat agree,Neither agree or disagree,Somewhat agree,Neither agree or disagree,Neither agree or disagree,To give specific prompt for the task that I want to achieve and then explain through followup prompt what I wanted.,"I used to understand the code and the error and sometimes to design the visualisation for me,",Yes it was helpful as I wouldn't have been able to do the task without GenAI,Basic understanding of the deliverables and the tasks or knowledge about Python would help me visualise and understand the context better,I wish there were masterclass covering technical part for someone unfamiliar with the python or small tasks for understanding the basics,,vsakhark,2,2,2,2,2,3,4,3,4,3,4,5,4,4,4,4,4,3,3,3,4,3,2,2,2,2
4/22/2025 19:07:54,<EMAIL>,Cody Soska,Advanced (very familiar),Advanced (very familiar),Expert (extremely familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Very interested,Very interested,Very interested,Very interested,Extremely interested,Moderately interested,Moderately interested,Slightly interested,Slightly interested,Moderately confident,Moderately confident,Moderately confident,Slightly confident,Moderately confident,Very confident,Very confident,Very confident,Extremely confident,Slightly confident,Strongly agree,Strongly agree,Strongly agree,Neither agree or disagree,Somewhat agree,"Honestly, I still had trouble understanding what assignment outputs should look like. Adding an example of each score code to the rubric would help me know what you wanted","I got used to having to reiterate things to gemini (""the '#' symbol is not a hash symbol and mean 'number of' to a human read; ignore it and access the columns by index)",YES! I could not have done any of this without genAI and I've done all of this without GenAI. This is a realistic workflow and how people would actually do this kind of work in the real world. Very valuable.,"Being able to use other AI models would help, but I know this would compromise the experiment design",I learned about machine learning - how it actually looks when written,This is a great class and I'm sorry the students were so rude. You can probably tell them to shut up or separate them; it was really unbelievably unprofessional to see grad students behave this way.,csoska,3,3,3,2,3,4,5,5,5,3,4,4,4,4,5,3,3,2,2,4,4,5,2,2,2,3
4/22/2025 19:40:19,<EMAIL>,Ashwin Swaminathan,Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Novice (not familiar at all),Novice (not familiar at all),Moderately interested,Very interested,Very interested,Slightly interested,Slightly interested,Slightly interested,Moderately interested,Not interested at all,Not interested at all,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Very confident,Moderately confident,Slightly confident,Moderately confident,Slightly confident,Somewhat agree,Somewhat agree,Somewhat agree,Neither agree or disagree,Somewhat agree,"Refining the output by adjusting the code. Since I used GenAI to write the code, it was frustrating to adjust the prompt with hyper-specific details to get the output I envisioned.",Primarily to write code for my outputs. ,In many assignments I observed Gemini made mistakes in capturing variable names. It also often did things that I specifically asked it not to do in my prompts. It was hence useful to only a certain extent.,"GenAI should be able to get a preview of the dataset so that it could write code relevant to my requirements more accurately. I found myself typing every column name manually in my prompts. Being a beginner, it often took me several iterations to identify all the relevant columns.",My coding experience was limited and I learned a little coding before GenAI existed. I did not put it to practical use in a workplace. Through our assignments I learned how powerful GenAI is in generating code and visualizing. ,I wish the assignment guidelines were clearer. They often seemed open-ended which made it more challenging for beginners. Clearer guidelines would also make the learning outcomes more clear.,ashwin2,2,2,2,2,2,4,4,4,4,3,3,4,4,2,2,2,3,1,1,3,4,4,2,2,2,3
4/22/2025 20:35:14,<EMAIL>,Hassaan Pasha,Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Extremely interested,Extremely interested,Extremely interested,Very interested,Very interested,Extremely interested,Extremely interested,Moderately interested,Extremely interested,Very confident,Very confident,Very confident,Very confident,Very confident,Extremely confident,Extremely confident,Very confident,Extremely confident,Very confident,Strongly agree,Strongly agree,Somewhat agree,Somewhat agree,Strongly agree,Coming up with the right prompts to get the output I require,"I gave it context, data, and what I wanted from it and would then assess the output. From there, I would either redo it, make edits or just complete the task.",Extremely helpful since the assignments did not have much context and everything had to be driven by the student.,More context on the assignment/task requirement.,"I learned how to analyze and clean complex data, perform EDA, create models, visually represent the learnings. I wish I could learn more about the same using different datasets than just one.",N/A,hpasha,4,4,4,4,4,5,5,5,4,4,5,5,5,4,4,5,5,3,5,3,4,4,4,4,4,4
4/22/2025 20:36:33,<EMAIL>,Aminat Afonja,Novice (not familiar at all),Expert (extremely familiar),Novice (not familiar at all),Beginner (slightly familiar),Beginner (slightly familiar),Novice (not familiar at all),Beginner (slightly familiar),Novice (not familiar at all),Novice (not familiar at all),Very interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Moderately interested,Moderately interested,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Moderately confident,Not confident at all,Not confident at all,Slightly confident,Not confident at all,Somewhat agree,Neither agree or disagree,Neither agree or disagree,Strongly disagree,Strongly disagree,"I noticed a connection between the assignments, but the questions didn’t really highlight that link, so it was hard to see the similarities.",I used Gemini to generate codes,"While GenAI provided code suggestions, there was no way to confirm if the code was correct or not.","What’s most helpful is to understand the code and then use GenAI to validate or cross-check the understanding. Relying solely on GenAI hasn’t been very effective, and I was hoping this class would help me develop that understanding.","I learned how to phrase questions effectively when using Gemini to get help. I enrolled in the course hoping to learn topics like SQL, Python, and machine learning, and to gain knowledge  in Data Science that I could apply in real-world situations.","The course didn’t meet my expectations. Although I used GenAI to help with my assignment questions, I was hoping to receive feedback on any mistakes I made so I could learn and improve.",aafonja,1,1,1,1,1,1,4,3,3,1,4,5,5,5,5,5,5,3,3,1,5,1,2,2,1,2
4/22/2025 22:48:18,<EMAIL>,Mukul Lal,Beginner (slightly familiar),Advanced (very familiar),Beginner (slightly familiar),Beginner (slightly familiar),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Slightly interested,Very interested,Moderately interested,Slightly interested,Slightly interested,Not interested at all,Moderately interested,Not interested at all,Not interested at all,Slightly confident,Slightly confident,Not confident at all,Not confident at all,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Strongly agree,Neither agree or disagree,Somewhat agree,Neither agree or disagree,Neither agree or disagree,"It was easy to prompt GenAI to get an output, I wasn't sure about the validity of the output as I don't have a background in statistics and I left programming more than 12 years back","For all the python coding, and hoping it was giving the right results","I can't answer this because like I said, I have zero idea about Python. So if the code generated by GenAI was compiling, I mostly believed it was working",Can't think of anything,"Basics of data cleaning, EDA and ML. I wish there was more focus on the application of data science in the life of a product manager, and more focus on teaching python programming and statistics. As PMs, I assume that is where we will add value, rather than coding",,mlal,2,2,1,1,2,3,5,3,4,3,2,4,3,2,2,1,3,1,1,2,4,2,2,1,1,1
4/23/2025 0:00:24,<EMAIL>,Soham Mondal,Expert (extremely familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Very interested,Moderately interested,Very interested,Very interested,Very interested,Extremely interested,Very interested,Moderately interested,Slightly interested,Extremely confident,Very confident,Very confident,Very confident,Very confident,Very confident,Extremely confident,Extremely confident,Extremely confident,Moderately confident,Somewhat agree,Strongly agree,Strongly agree,Somewhat agree,Strongly agree,"One of the biggest challenges I faced while using GenAI during the final assignment — Data-Driven Storytelling and Reflection - was structuring the data and framing the problem based on insights from the previous two assignments. Unlike tasks like EDA or model building, where objectives are clearly defined, the storytelling component required a deeper interpretation of expectations, which was difficult to decode. Understanding what exactly the assignment demanded in terms of narrative framing and insight synthesis posed a significant hurdle.

Across all four assignments - Data Cleaning, Exploratory Data Analysis, Machine Learning, and Data Storytelling - GenAI proved helpful when guided with the right prompts, especially in generating Python code, suggesting models, or performing text explanations. However, the challenge lies in how effectively the user communicates intent. For instance, GenAI works well if the user can phrase a clear and specific request, but edge cases - such as identifying the most suitable ML model for a unique dataset or ensuring complete data integrity during cleaning - still require manual intervention or expert oversight.

A noteworthy platform-specific difference was with Google Gemini, which only debugged isolated segments of code rather than end-to-end. This made holistic testing and debugging more difficult compared to other LLMs that could trace context throughout a script.

In summary, while GenAI was uniformly helpful across all assignments, the degree of ambiguity in task framing and handling of complex edge cases were the most significant challenges - especially in the storytelling phase.","I have used Gen AI mostly for debugging code but at times I did see that Claude/Anthropic (strong LLMs) are able to fully solve the entire problem thoroughly with greater than 99% accuracy if all inputs are fed to the prompt properly. But there was no end to the refinement mechanisms. So the code has to be reiterated and refined.

Personally I took help of Claude of only some aspects like framing the problem and what the assignment actually wants and then get a template and then coded myself so that I get the hang and feel of the process and also debug the code with Google Gemini whenever necessary or I solved by own.","Yes, I found using GenAI helpful during the tasks - especially for accelerating initial implementation, debugging, and breaking down complex functions into manageable steps. In assignments like Data Cleaning and Exploratory Data Analysis (EDA), GenAI was particularly effective in suggesting standard preprocessing techniques, helping identify missing values, and generating exploratory plots with appropriate code snippets. These are well-defined tasks where GenAI performs reliably if prompted correctly.

For the Machine Learning assignment, GenAI was still useful for prototyping baseline models and explaining algorithm choices. However, when it came to selecting the most contextually appropriate model, tuning hyperparameters, or interpreting nuanced performance metrics (e.g., precision-recall trade-offs), I needed to supplement the output with manual insights and domain understanding.

The most challenging use case for GenAI was during the final assignment on Data-Driven Storytelling. Here, the task required not just code, but critical thinking, narrative flow, and synthesizing insights across previous tasks. While GenAI could assist with writing or outlining, the creative and strategic framing still had to come from me.

In summary, GenAI was most helpful for structured, repetitive tasks (like cleaning or basic modeling), and less so for strategic decision-making or storytelling. Across all assignments, its usefulness was highly dependent on how well I could frame the question and how clear the task requirements were.","To further improve performance while using GenAI on these tasks, one major enabler would be tight integration of powerful LLMs like Claude or Anthropic directly within development environments. Embedding these models contextually where developers are actively coding could significantly accelerate low-fidelity coding tasks - such as data wrangling, model prototyping, or exploratory analysis. This would reduce friction and streamline the process of iterating on code while staying within the flow of development.

However, it's important to note that certain systems-level programming tasks are inherently harder to automate. For example, when implementing machine learning models for embedded systems - where optimization techniques like quantization, pruning, or compression are used - the process becomes much more nuanced. These tasks often involve hardware-specific constraints and require iterative performance evaluation using metrics like confusion matrices or dropout rates, which are difficult to fully abstract through GenAI alone.

Across the assignments - from Data Cleaning to Machine Learning and Storytelling - a consistent pattern was that GenAI excels in automating well-scoped tasks, such as generating boilerplate code or offering basic analysis recommendations. But when the assignments demanded interpretation, strategic judgment, or model optimization (as in the Machine Learning and Storytelling phases), the limitations of GenAI became more apparent.","This class was excellent, and I learned a great deal about the foundational pillars of data science and how they apply to product development and strategic decision-making. Specifically, I gained hands-on experience in:

Data Cleaning - ensuring high-quality inputs by identifying and resolving inconsistencies.

Exploratory Data Analysis (EDA) - discovering trends and patterns through visual and statistical tools.

Machine Learning Basics - applying foundational models to generate predictions and drive product insights.

Data-Driven Storytelling and Reflection - crafting compelling narratives that translate data into business value.

Beyond the core technical skills, the class significantly enhanced my ability to track and interpret product and process metrics - an essential competency in technical program management. It gave me the tools to work with real project data and apply data-driven thinking to scope definition, process optimization, and stakeholder communication.

This learning is directly aligned with my upcoming internship at Tenstorrent, where I will be managing AI hardware and software systems. The ability to extract actionable insights from technical data will be invaluable in optimizing workflows and driving product decisions.","The TAs should be more consistent in their evaluation metrics for assignments. In this course, I observed significant disparities in how assignments were graded, with no clearly defined central framework or standardization in place.

In one instance, I lost marks in a segment for a comment/refinement, whereas my friend - who did only that refinement - was told by their TA to do exactly what I had done. Essentially, the refinement suggestions we received were different, and I strongly believe I could have received full marks had I been evaluated by the same TA as my friend.

Additionally, several classmates appeared to falsify assignments and later requested regrades using entirely new, externally sourced versions. This creates bias in evaluation and results in unfair grading for those who submit original work.",sohammon,5,4,4,4,4,5,4,5,5,4,4,3,4,4,4,5,4,3,2,5,4,4,4,4,4,4
4/23/2025 0:07:43,<EMAIL>,Ankit Shukla,Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Beginner (slightly familiar),Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Slightly interested,Slightly interested,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Strongly agree,Strongly agree,Strongly agree,Neither agree or disagree,Neither agree or disagree,"One challenging thing was to get a working code from Gemini. Always got buggy code with errors, so had to fix them.","Similar, asked help to write or fix some code",Yes but not too much,I think the current situation is good,"Loved the class. Now I can confidently look at any data and take a stab at it. I can use terms like EDA, Analysis, Modelling etc. which are super helpful as a PM.",,ankitshu,5,5,5,5,5,3,5,5,5,3,5,5,5,5,5,5,5,2,2,4,4,4,4,4,4,4
4/23/2025 1:46:26,<EMAIL>,Shriya Jashnani,Beginner (slightly familiar),Expert (extremely familiar),Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Beginner (slightly familiar),Beginner (slightly familiar),Moderately interested,Extremely interested,Extremely interested,Very interested,Very interested,Very interested,Very interested,Slightly interested,Slightly interested,Slightly confident,Very confident,Very confident,Moderately confident,Very confident,Extremely confident,Very confident,Very confident,Very confident,Very confident,Strongly agree,Strongly agree,Strongly agree,Strongly agree,Strongly agree,"Most of the times, it was difficult to feed context because there were multiple sources to refer",To codify my logic and the analysis I had done,"Yes, with limited programming experience GenAI was helpful my logic into practice","Ability to invest context effectively through better prompting, etc.","I learned how everything ties up together to communicate a story, starting from data cleaning to exploratory data analysis to actually using machine learning models.",,sjashnan,2,4,4,3,4,5,5,5,5,5,3,5,5,4,4,4,4,2,2,2,5,3,4,4,3,4
4/23/2025 2:30:53,<EMAIL>,Suhail Khan,Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Moderately interested,Moderately interested,Extremely confident,Very confident,Very confident,Very confident,Very confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Somewhat agree,Somewhat agree,Somewhat agree,Neither agree or disagree,Somewhat agree,"Variable names not picked correctly. While refining model, it was not giving good approach and sometimes while fixing bugs, it  kind of stuck in loop solution.","Majorly to write code, instead of looking syntax and libraries to use. Also, sometime to brainstorm certain solutions.","Yes, it helped me to save time and write effecient code for basic tasks. ",It accuracy fainted while doing profound tasks like improving model performance and resolving complex bugs.,"I already had work as software engineer and worked briefly with machine learning previously, so I was bit comfortable. Although, few topic opened some prospective and certain new approach problems.","Last assignment was quite open ended, it'll be better to have some structure. Also, few graded prereading should be there before the lecture that would help students to have some concepts as many are not very much comfortable with coding in general. Reading that give basics of python, pandas, numpy, data types, functions, etc",suhailk,5,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,3,3,4,4,4,4,4,4,4
4/23/2025 10:11:01,<EMAIL>,Roni Kim,Novice (not familiar at all),Expert (extremely familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Expert (extremely familiar),Novice (not familiar at all),Novice (not familiar at all),Not interested at all,Extremely interested,Slightly interested,Not interested at all,Moderately interested,Slightly interested,Extremely interested,Not interested at all,Not interested at all,Not confident at all,Slightly confident,Slightly confident,Slightly confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Very confident,Moderately confident,Strongly agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Not really any challenges for me using GenAI during my assignments. ,I used it for the code that I need to run the program. ,I found it very helpful given that I'm a novice in coding. ,NA,It was a great experience in getting to learn how to run data analysis. ,,rkkim,1,2,2,2,5,4,5,4,4,4,1,5,2,1,3,2,5,1,1,1,5,3,2,3,2,5
4/23/2025 10:29:44,<EMAIL>,Nikhil Medarametla,Beginner (slightly familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Expert (extremely familiar),Expert (extremely familiar),Intermediate (moderately familiar),Slightly interested,Very interested,Extremely interested,Very interested,Very interested,Moderately interested,Extremely interested,Very interested,Moderately interested,Not confident at all,Very confident,Very confident,Moderately confident,Very confident,Extremely confident,Moderately confident,Moderately confident,Extremely confident,Very confident,Somewhat agree,Strongly agree,Somewhat agree,Somewhat agree,Strongly agree,"I did all the assignments myself, but I used GenAI mainly to help with coding and checking my logic. The challenge was making sure I fully understood the suggestions and didn’t just copy them sometimes I had to go back and tweak things to fit the task.","I mostly used it to get help with debugging code, understanding errors, and cross-checking my work. It was also useful when I needed a second opinion or a clearer explanation of a concept. But I always made sure to write the final version on my own.","Yes, it was helpful, especially when I got stuck or wanted to be sure my approach made sense. It didn’t do the work for me, but it acted like a smart assistant that helped me think things through and spot mistakes faster.","Maybe having some prompt examples or best practices would help use it more effectively. Also, knowing how to get more tailored responses that match assignment expectations would be great.","I learned how to apply data science techniques step-by-step from EDA to modeling—in a way that actually made sense. I wish we could spend a bit more time on model evaluation and interpreting results, as those areas still feel a little tricky.","The course gave me a lot of confidence to handle data problems on my own. I liked how GenAI was encouraged as a tool, not a shortcut it helped me learn better without taking away from the hands on part.

",nmedaram,1,4,4,3,4,5,4,5,4,4,2,4,5,4,4,3,5,4,3,2,4,4,4,4,3,5
4/23/2025 10:36:33,<EMAIL>,Karan Qasba,Beginner (slightly familiar),Expert (extremely familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Expert (extremely familiar),Intermediate (moderately familiar),Slightly interested,Very interested,Moderately interested,Moderately interested,Moderately interested,Very interested,Moderately interested,Extremely interested,Slightly interested,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Moderately confident,Moderately confident,Extremely confident,Moderately confident,Somewhat agree,Neither agree or disagree,Neither agree or disagree,Neither agree or disagree,Neither agree or disagree,"I had to give small details of the code as well to the GenAI, I thought it already has taken the input what is there on the screen. I was getting code errors more often.",It is good for certain tasks but not as good as chatgpt,"Yes, it is helpful to do certain tasks but not all",if it could read what is there on screen when i wanted to,I learned about cleaning data and how to show data using visualization.,Overall it was good that we were allowed to use GenAI,kqasba,5,5,5,5,5,3,4,3,3,3,2,4,3,3,3,4,3,5,2,2,5,3,2,2,2,2
4/23/2025 10:50:23,<EMAIL>,Kritika Rastogi,Beginner (slightly familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Beginner (slightly familiar),Novice (not familiar at all),Beginner (slightly familiar),Novice (not familiar at all),Novice (not familiar at all),Slightly interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Not interested at all,Not interested at all,Not confident at all,Very confident,Very confident,Not confident at all,Very confident,Very confident,Very confident,Very confident,Moderately confident,Moderately confident,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,I think the main issue I faced was during story telling assignment cause I needed to understand first what I needed to do before asking Gen AI to do it. I wasn't really able to instruct Gen AI to help me for that. ,I used it to code. I tried learning the basics but that was kind of tuff. ,"Yesss, I needed it for coding. I think it makes me more confident now. I feel I can use GEN AI tools at work to code. Reducing my dependency to the data folks. ",Learning prompt engineering ,I like what I learned about how theory and practical aspects were balanced. So it was very helpful! ,NA,krastogi,1,4,4,1,4,4,4,4,4,4,2,5,5,5,5,5,5,1,1,2,3,3,2,2,1,2
4/23/2025 11:04:38,<EMAIL>,Devisha Tayal,Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Slightly interested,Very interested,Extremely interested,Very interested,Very interested,Very interested,Very interested,Slightly interested,Slightly interested,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Moderately confident,Moderately confident,Very confident,Very confident,Moderately confident,Somewhat agree,Somewhat agree,Somewhat agree,Neither agree or disagree,Neither agree or disagree,Main challenge started before using GenAI and understanding what was needed to be done.,"I asked a lot of questions, shared some tasks as well to get insights.","Yes, it was useful in giving me a lot of ideas and helping me code.",Personal knowledge and how to prompt better,"I learnt about how data analysts work. The pipeline: data cleaning, EDA, ML model and communication became more clear.

I wish we had covered more basics and gotten deeper into how to make sense of data more.",,dtayal,2,2,2,2,2,3,4,4,4,3,2,4,5,4,4,4,4,2,2,3,3,3,2,2,2,2
4/23/2025 11:05:34,<EMAIL>,Charlene Lin,Beginner (slightly familiar),Advanced (very familiar),Intermediate (moderately familiar),Novice (not familiar at all),Beginner (slightly familiar),Beginner (slightly familiar),Beginner (slightly familiar),Novice (not familiar at all),Novice (not familiar at all),Slightly interested,Very interested,Extremely interested,Slightly interested,Very interested,Moderately interested,Moderately interested,Not interested at all,Not interested at all,Not confident at all,Slightly confident,Moderately confident,Slightly confident,Slightly confident,Slightly confident,Moderately confident,Moderately confident,Slightly confident,Slightly confident,Neither agree or disagree,Neither agree or disagree,Neither agree or disagree,Neither agree or disagree,Neither agree or disagree,"Even with generative AI, I feel like you still need to ask the right questions to get results that actually make sense to you—and to make sure you’re not missing any important steps in the process. Most importantly, I’ve found that having a general sense of how to approach the task really improves the outcome and significantly cuts down the time it takes.","What works best for me is having a rough idea of how to approach the task first, then using generative AI to help build a plan. I use my own understanding to iterate on that plan. Once I have an overall structure, I ask the AI to rephrase my request in plain language to form a more detailed prompt. Then I use that prompt to generate the code.","Yes, with that approach, I’ve found generative AI really helpful in translating my high-level understanding into actual execution.","As mentioned earlier, having a high-level sense of how to approach the project really helps. For example, knowing the key steps, what needs to be done in each one, and the logic behind moving from step to step. I don’t need to know every detail of the execution, but I do need to understand what success looks like at each stage so I know when it’s time to move on.","I feel the class could have been more helpful if it focused more on the aspects mentioned above—like understanding the key steps, what needs to be done at each stage, the logic of moving through the process, what success looks like, what to watch out for, and how to stay on track.

Even more importantly, I think the decision-making aspect deserves more attention. For example, walking through specific scenarios and discussing how to approach them—do we need a model? What are the trade-offs? Could EDA be more useful than building a model? And if a model is needed, which type would be most effective?—that kind of guidance would’ve made a big difference.",,charlenl,1,2,3,2,2,3,3,3,3,3,2,4,5,2,4,3,3,1,1,2,4,3,1,2,2,2
4/23/2025 11:33:30,<EMAIL>,Siddhant Sagar,Intermediate (moderately familiar),Beginner (slightly familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Novice (not familiar at all),Novice (not familiar at all),Novice (not familiar at all),Very interested,Very interested,Very interested,Moderately interested,Slightly interested,Very interested,Extremely interested,Moderately interested,Very interested,Moderately confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Slightly confident,Neither agree or disagree,Neither agree or disagree,Neither agree or disagree,Somewhat disagree,Somewhat disagree,Getting the right starting point.,In order to get the right starting point.,"For generating code yes, but there were misalignments at times with the ask",Asking the right questions.,Learnt about DS practices for PMs. Would like to learn more about data-driven storytelling.,,ssagar2,3,2,2,2,2,2,3,3,3,2,4,4,4,3,2,4,5,3,4,3,2,2,3,3,3,1
4/23/2025 11:51:08,<EMAIL>,Esha Lakra,Novice (not familiar at all),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Beginner (slightly familiar),Advanced (very familiar),Slightly interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Extremely interested,Moderately interested,Moderately interested,Not confident at all,Very confident,Very confident,Moderately confident,Moderately confident,Extremely confident,Slightly confident,Moderately confident,Very confident,Slightly confident,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat disagree,Somewhat agree,"No common similarities. Overall, it was fine to use them for what they can like execution (coding, outputs) and use our own thinking to direct them, at least that is how I approached the assignments.",Covered above^,"Yes. Again, in line, to the above - for coding & data analysis","None, I can think of now.","I broadly learnt all the 4 course topics- not about learning more, but learning what I did with more deeply for confidence.",,elakra,1,4,4,3,3,4,4,4,4,2,2,5,5,5,5,5,5,3,3,1,4,3,4,3,3,3
4/23/2025 12:14:29,<EMAIL>,venkata himakar yanamandra,Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Very interested,Moderately interested,Very interested,Very interested,Moderately interested,Moderately interested,Moderately interested,Not interested at all,Moderately interested,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Somewhat agree,Neither agree or disagree,Somewhat agree,Somewhat agree,Somewhat agree,errors gemini gave and fixing them,For data exploration and visualization aspects ,"It was helpful in few aspects, but most of the areas gemini was giving errors","Using better AI models can definetly help, with current accuracy inhouse gemini was not that useful","I have worked in this area professionally, I would have loved to explore AI agents and current landscape of LLMs and capabilities. ",,hyanaman,4,4,4,4,4,4,4,3,4,4,4,3,4,4,3,3,3,1,3,4,3,4,3,4,3,4
4/23/2025 12:25:43,<EMAIL>,Aditya Teja Bhimavarapu,Intermediate (moderately familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Very interested,Very interested,Very interested,Moderately interested,Moderately interested,Very interested,Extremely interested,Extremely interested,Extremely interested,Moderately confident,Very confident,Moderately confident,Moderately confident,Moderately confident,Very confident,Very confident,Moderately confident,Very confident,Moderately confident,Somewhat agree,Neither agree or disagree,Somewhat agree,Somewhat agree,Somewhat agree,To clearly describe the problem statement and what I think would be a good direction ,To deal with the syntax of programming when I can think of what questions to answer and get insights,"Yes, reduced burden of not learning python syntax, but also reduces the depth of understanding of a subject.",clearly describing the problem statement and what woulf be good solution so that It doesn't give unexpected results ,Learnt basics of ML and how to think about getting a project end to end. Would have liked more in depth understanding of ML models,,abhimava,3,4,3,3,3,4,4,3,4,4,4,4,4,3,3,4,5,5,5,3,4,3,4,4,4,3
4/23/2025 13:08:13,<EMAIL>,Nakshatra (Nax) Sharma,Intermediate (moderately familiar),Advanced (very familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Moderately interested,Very interested,Very interested,Very interested,Moderately interested,Very interested,Very interested,Moderately interested,Very interested,Moderately confident,Very confident,Moderately confident,Moderately confident,Very confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,It was tough when the code wouldn’t compile and I had to figure out what went wrong,"I mostly used it to get started or unstuck, especially when I wasn’t sure how to begin","It was helpful for quick ideas, but not always reliable when things broke.",Probably learning how to ask more specific questions and read the code better,"I learned how to use GenAI smarter, but I wish we dove deeper into debugging with it",,naxs,3,4,3,3,4,4,4,4,4,4,3,4,4,4,3,4,4,3,4,3,4,2,3,3,3,3
4/23/2025 14:27:15,<EMAIL>,Somya Mehta,Intermediate (moderately familiar),Advanced (very familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Novice (not familiar at all),Novice (not familiar at all),Very interested,Very interested,Very interested,Moderately interested,Very interested,Moderately interested,Very interested,Not interested at all,Not interested at all,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Very confident,Strongly agree,Strongly agree,Somewhat agree,Somewhat agree,Somewhat agree,Initially I struggled in understanding what prompts to give to GenAI to begin with,To get some ideas on how it will handle similar request and to generate code,Very helpful as I do not code in my day to day life,Basic prompts,Data driven story telling,,somyameh,3,3,3,3,3,4,5,5,4,4,4,4,4,3,4,3,4,1,1,3,4,3,3,3,3,3
4/23/2025 17:17:44,<EMAIL>,Abdul Gaffoor Shaik ,Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very interested,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Refining the last 20% code. It gives the base. ,For initial code. ,Yes ,More refinement ,Vibe coding ,,agshaik,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,3,3,3,3,3,3
4/24/2025 22:23:26,<EMAIL>,caroline fan,Intermediate (moderately familiar),Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Beginner (slightly familiar),Intermediate (moderately familiar),Moderately interested,Moderately interested,Very interested,Moderately interested,Very interested,Very interested,Very interested,Slightly interested,Moderately interested,Moderately confident,Very confident,Extremely confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,Somewhat agree,NA,coding,yes,prompt ,na,,sijiaf,3,4,5,4,4,4,4,4,4,4,3,3,4,3,4,4,4,2,3,3,3,4,4,4,4,4
5/3/2025 15:06:25,<EMAIL>,Jonathan Gu,Intermediate (moderately familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Extremely interested,Extremely interested,Extremely interested,Very interested,Very interested,Extremely interested,Extremely interested,Moderately interested,Moderately interested,Very confident,Extremely confident,Very confident,Moderately confident,Very confident,Moderately confident,Not confident at all,Very confident,Moderately confident,Slightly confident,Somewhat agree,Somewhat agree,Somewhat agree,Neither agree or disagree,Somewhat agree,It was sometimes difficult to get GenAI to do tasks exactly how I wanted it to.,"I used GenAI to understand assignments, as well as using it to guide me through what I need to do to complete it.","Yes, I found that GenAI was extremely helpful, especially when it came to explaining terms I didn't know.",I think using multiple GenAIs would have been helpful instead of just Gemini.,"I learned a lot about data cleaning, data analysis, and how machine learning is used in data. I wish we were able to learn about ML a bit more, but it is hard to in a mini.",,jgu2,4,5,4,3,4,4,4,4,4,3,5,5,5,4,4,5,5,3,3,3,4,3,4,4,3,4
5/4/2025 9:28:07,<EMAIL>,Tejas Sohani,Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Advanced (very familiar),Intermediate (moderately familiar),Intermediate (moderately familiar),Very interested,Very interested,Very interested,Very interested,Moderately interested,Very interested,Moderately interested,Moderately interested,Moderately interested,Extremely confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Very confident,Strongly agree,Somewhat agree,Strongly agree,Somewhat agree,Somewhat agree,"Across all assignment, understanding the format of assignment was challenging. In terms of what is expected.",I used Gen AI to understand errors and writing python code,"Gen AI was helpful, without it programming in python for ML would be difficult.",GenAI could improve working with external libraries to provide a solution. For example the story telling assignment libraries to create story telling were not well adapted by GenAI to provide code snippet or error details. ,I wish we could learn more about creating more complicated ML models and observe their use in real world scenario,,tsohani,5,4,4,4,4,4,5,4,5,4,4,4,4,4,3,4,3,3,3,4,4,3,4,4,4,4
