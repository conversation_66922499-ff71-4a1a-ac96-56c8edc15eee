<PERSON><PERSON><PERSON>,<PERSON><PERSON>dress,Name,used_llm,perceived_difficulty_ml,perceived_effort_ml,perceived_stress_ml,perceived_engagement_ml,perceived_success_ml,confidence_ml,helpseek_ml,challenge_used_llm,llm_use,llm_helpful,help_needed,help_peer,comment,andrewid,perceived_success_ml_score,perceived_difficulty_ml_score,perceived_effort_ml_score,perceived_stress_ml_score,perceived_engagement_ml_score,helpseek_ml_score,confidence_ml_score
4/9/2025 17:55:52,<EMAIL>,<PERSON>,<PERSON><PERSON>'s innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Extremely interesting,Extremely successful/ satisfied,Extremely confident,Not at all,"Structuring prompts, and solving niche errors",Used it to generate codes and solve errors,"Very helpful, as it gave the entire structure of writing ML code from start to finish within seconds",Having knowledge of different ML models,"I helped them understand how libraries work and how certain functions, like MultiLabelBinarizer, help in solving ML problems",,ramka<PERSON><PERSON>,5,3,3,2,5,1,5
4/9/2025 22:10:42,<EMAIL>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>'s innate GenAI (<PERSON>),Very difficult,I need to work extremely hard,Very frustrating,Moderately interesting,Moderately successful/ satisfied,Slightly confident,Not at all,Was not sure about what was to be done exactly,gemini GenAI to write code,yes in writing code,Clarity on task,NA,,rupalc,3,4,5,4,3,1,2
4/11/2025 10:44:45,<EMAIL>,Devisha Tayal,"Colab's innate GenAI (Gemini), ChatGPT",Very difficult,Very effortful,Moderately frustrating,Slightly interesting,Moderately successful/ satisfied,Moderately confident,A little bit,"When using Gemini, I ran into a lot of code issues which took about 30 minutes to resolve but they still kept popping which made me switch to chatgpt.","To understand more about the task, get suggestions and write code.","Yes, very helpful because I don't understand how to do this and GenAI helped me break down the steps.",using gemini was tough because we kept running into coding issues,How to use GenAI ,,dtayal,3,4,4,3,2,2,3
4/11/2025 23:34:53,<EMAIL>,Mukul Lal,"Colab's innate GenAI (Gemini), ChatGPT",Extremely difficult,Very effortful,Extremely frustrating,Not interesting at all,Not successful/ satisfied at all,Slightly confident,Not at all,I don't understand ML or Python. Not sure whatever I did was correct or not - with or without GenAI,All the tasks,Not sure because I can't validate the output,"Better understanding of data science and Python. Also, each of the step of building a model better - it felt as if I was shooting in the dark when I was completing the assignment",None,,mlal,1,5,4,5,1,1,2
4/12/2025 19:11:41,<EMAIL>,Suhail Khan,Colab's innate GenAI (Gemini),Slightly difficult,Slightly effortful,Not frustrating at all,Very interesting,Very successful/ satisfied,Very confident,Not at all,"Once I have attempted different models and then I tried to improve performance of my best performing model in my case random forest. I asked Gemini to help me improve, I tried multiple approaches but couldn't cross initial mean square error and R2. ",For writing code and sometime in brainstorming my approaches.,"Yes, I can write code faster and efficiently. No need to google anything just for simple format.","Precise variable names in code, suggest possible steps, etc.",I haven't asked anything as such.,,suhailk,4,2,2,1,4,1,4
4/13/2025 9:51:21,<EMAIL>,Manasa Nandigama,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,Giving right prompts,Asked how to create models,"Yes, it did explain me how we're supposed to go futher",Giving right prompts,None,,mnandiga,4,3,3,3,4,1,4
4/13/2025 11:31:13,<EMAIL>,Kritika Rastogi,"Colab's innate GenAI (Gemini), ChatGPT",Not difficult at all,Slightly effortful,Slightly frustrating,Extremely interesting,Extremely successful/ satisfied,Extremely confident,A little bit,Nothing at all ,I gave it the question and asked it to code and then asked for the prompt as well ,"Yessss, couldn't have figured it out without Gen AI. I","I think I just need to understand the various possibilities of the task. I am unaware of what ML can do, so it limits my thinking. But such exercises really help me. ",Just about discussing the possibilities of what can be done.,"No, I am good! ",krastogi,5,1,2,2,5,2,5
4/13/2025 14:26:47,<EMAIL>,Bhavya A,Colab's innate GenAI (Gemini),Very difficult,I need to work extremely hard,Moderately frustrating,Extremely interesting,Extremely successful/ satisfied,Moderately confident,Not at all,prompting,"i had identified tasks, and what i needed to do. Used genai for help in code correction",yes it helps understand the query and code,na,i did not consult or spoke with anyone in the peer,na,barora,5,4,5,3,5,1,3
4/13/2025 14:59:03,<EMAIL>,Upasna Ahuja,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Very frustrating,Very interesting,Very successful/ satisfied,Not confident at all,Not at all,To understand if I'm doing the assignment right? since i have absolutely no background to understand the results and if that's the end product needed for the assignment is difficult to understand.,used GenAI to create code as i don't code or have any background in DS coding. ,"Yes, especially to understand errors and also generate code at all. I wouldn't be able to do my assignment at all without it.","I think doing an assignment similar to what we have to submit in class. As it's very difficult for me to understand, even if it means it's a virtual lecture guided by the TA out of normal lectures, or a pre-recorded video of somebody doing the similar assignment on another data set and helping understand what the Gen AI is returning what the result means, what step I am on in the assignment how to reach the end goal to understand the concept. What every stage did and how to do it step by step.",I didn't ask anything to anybody as i didn't know what to ask for help in the 1st place.,"In general I think for people like me with 0 DS background helping with additional pre-recorded videos of the assignments of how to do them on different datasets or more such task videos that Product managers will actually need and have forever access to will help alot. I think also some additional data sets made available just to practice and the 1s shown in the pre-recorded videos too will help. Baby steps, from scratch. Lectures are great to understand the concept in theory but assignments have 3 lines problem statements and i have no idea if im doing it right or even understand what im doing.",usa,4,4,4,4,4,1,1
4/13/2025 16:15:47,<EMAIL>,Nivedita Yadav,Colab's innate GenAI (Gemini),Not difficult at all,Slightly effortful,Not frustrating at all,Very interesting,Moderately successful/ satisfied,Moderately confident,Not at all,It was pretty straight forward ,"Yes, by giving it prompts ","Yes, because it gave me the correct code without any errors ",Not sure ,Nothing ,,nivedity,3,1,2,1,4,1,3
4/13/2025 17:39:13,<EMAIL>,Roni Kim,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Extremely successful/ satisfied,Extremely confident,Not at all,There wasn't really a challenge,I used it for me to retrieve the code that I need,Very helpful. It sent me the code that I needed. ,NA,No I did not. ,NA,rkkim,5,3,3,2,4,1,5
4/13/2025 17:47:15,<EMAIL>,grace,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,sometimes it gave too much output,syntax,"yes, it gave sufficient info",na,na,,gracelia,3,3,3,3,3,3,3
4/13/2025 20:12:03,<EMAIL>,Shriya Jashnani,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Not frustrating at all,Moderately interesting,Extremely successful/ satisfied,Extremely confident,A little bit,To verify if I was missing out on any step,For python coding,"Yes, it helped to codify my thinking and analysis",Detailed understanding of all the models,What kind of different models can be used for this data,,sjashnan,5,3,3,1,3,2,5
4/13/2025 20:36:15,<EMAIL>,Angelica,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Very interesting,Moderately successful/ satisfied,Very confident,Not at all,keep track of my conversation when asking questions and generating code,to get overview on steps for machine learning and generating code,"Yes, it helped me a lot",how can i link the code i have created previously with the next steps that I need,I just used GenAI,,achavess,3,3,3,3,4,1,4
4/13/2025 21:43:30,<EMAIL>,Alex Ding,Colab's innate GenAI (Gemini),Very difficult,Moderately effortful,Moderately frustrating,Extremely interesting,Very successful/ satisfied,Very confident,Very much,How to analysis the result and coefficent,Asking for code and help on analysis,"kind of, need to edited code by self",NA,NA,,alexding,4,4,3,3,5,4,4
4/13/2025 21:56:42,<EMAIL>,Charlene Lin,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Very successful/ satisfied,Very confident,Moderately,"At first I didn't understand the overall strategy for building a model, so even I ask GenAI to perform the task, I couldn't right away imagine how to do the task. It was after someone explained high level concepts and also some tactics of choosing and building model, I can start using GenAI to complete the task on my own.","I first ask ChatGPT to lay out the entire five-step process. Combined with my understanding, which I got from a friend who had just done his Data Science project (from other school), I wrote what I want to do in Gemini within CoLab and asked it to improve my prompt. I then copied the prompt to coding blocks to generate the codes.","Very. After I got enough high level understanding and can sort of imaging how to go about the task, it was GenAI that helped me to complete the tasks.",I think with this task I found a smooth way to utilize GenAI- ask it to also re-write my prompt before I use the prompt to generate the code. ,"What is the benefit of building a model to gain insights as a platform PM / What can't I learn in the EDA 
Also, I didn't ask, but he explained what it means to use a different model in this case. If using a regression model, what are some tactics that can be used to incorporate categorical data?",,charlenl,4,3,3,3,3,3,4
4/13/2025 23:00:07,<EMAIL>,Hassaan Pasha,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,I need to work extremely hard,Very frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,"Knowing which model to use, which features to select, and what happens to your model performance if you add/drop features.",Asked it to write the code while I gave the prompts for what I needed to do.,Extremely helpful because I was not aware of the library.,"If there were actual questions in the assignment like which model to train, which features to add, what metrics to look for, and what to optimize. ",Nothing.,N/A,hpasha,4,3,5,4,4,1,4
4/14/2025 0:39:42,<EMAIL>,Aminat Afonja,Colab's innate GenAI (Gemini),Extremely difficult,I need to work extremely hard,Extremely frustrating,Not interesting at all,Not successful/ satisfied at all,Not confident at all,Not at all,This assignment is not clear. The instruction and guidance was not sufficient,Asked coding questions,"No, it picked columns that are not in the table and required manual adjustment.","I think the mode of teaching the course can be better improved for better understanding, so I can easily understand the task and identify codes with errors for easy fix",N/A,I think our assignments in this course should be explained for easy execution.,aafonja,1,5,5,5,1,1,1
4/14/2025 0:39:48,<EMAIL>,Vrushal Sakharkar,Colab's innate GenAI (Gemini),Extremely difficult,I need to work extremely hard,Very frustrating,Slightly interesting,Moderately successful/ satisfied,Moderately confident,A little bit,Understand the output from the Gemini and exactly enter my expectations as I have to go back and forth to understand concept first and then ask Gemini,I used it to guide me on the results and explain the code after getting the result,"I would say mixed as there were times when even after running the results, it was throwing errors and even after trying to understand the result. I wasn't able clear to proceed further",I think more understanding on the practical part of the task at hand would help me leverage GenAI better,My classmates also struggled to understand the exercise and hence wasn't able to get much help from them this time,,vsakhark,3,5,5,4,2,2,3
4/14/2025 0:45:09,<EMAIL>,Esha Lakra,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Extremely confident,A little bit,To find how to go about different steps for a particular type of model,Same - To find how to go about different steps for a particular type of model,Yes. Step wise assistance to understand and for the code,Not sure on this,None,-,elakra,3,3,3,3,3,2,5
4/14/2025 1:30:56,<EMAIL>,Nikhil Medarametla,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Slightly frustrating,Extremely interesting,Very successful/ satisfied,Extremely confident,A little bit,"The hardest part was figuring out how to ask the right questions. Sometimes I’d get a good answer, but it needed tweaking to really fit the assignment. Also, making sure the output wasn’t too generic and matched what the professor expected was a bit tricky.","I used it to help break down the steps, write clean and well-commented code, and generate explanations for each part of the assignment. It really helped me stay organized and confident while working through everything.","Yes, definitely. It saved time and helped me understand the structure of a proper machine learning workflow. It also made it easier to focus on the actual problem rather than getting stuck on code syntax.",Just learning how to ask more specific prompts would help. Sometimes I had to try a few times to get the exact format or tone I wanted.,"I helped a friend work on a similar assignment using the same dataset. We talked about how to define the target and what counts as a “bonus effort.” For stuff like grading expectations or how much detail is needed, I usually ask classmates.",It was actually fun using GenAI for this! Felt like I had a smart assistant helping me structure my work. Would love to use it more in future projects.,nmedaram,4,3,4,2,5,2,5
4/14/2025 1:31:16,<EMAIL>,Soham Mondal,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,To problem solve and understand machine learning models to be used for the business case   ,I used Google Gemini for solving errors in code if any,I used Google Gemini for solving errors in code if any,Gemini Colab can improve the performance by lowering debug time ,No I did it completely on my own.,,sohammon,3,3,3,3,3,3,3
4/14/2025 1:35:17,<EMAIL>,Somya Mehta,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Not frustrating at all,Very interesting,Very successful/ satisfied,Extremely confident,Moderately,"I was initially confused on what insights I can generate, I asked chatgpt what could I do. It gave me some examples and I quickly got the hang of it then",I used it to generate codes,"Yes, I do not know how to code, so getting codes generated easily was very helpful",I don't have any suggestions right now,To understand what insights they were generating,None,somyameh,4,3,3,1,4,3,5
4/14/2025 1:37:11,<EMAIL>,Aditya Teja B,Colab's innate GenAI (Gemini),Slightly difficult,Very effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,"Correcting errors, ensuring names of dataframes is consistant across code","To create syntax for the models, testing them and to evaluate performance of those models","Yes, Gen AI is helpful as it takes care of syntax and the model charecteristics",to think what is our hypothesis and understanding the dataset and model in more detail,None,,abhimava,4,2,4,2,4,1,4
4/14/2025 5:07:37,<EMAIL>,Jonathan Gu,Colab's innate GenAI (Gemini),Extremely difficult,I need to work extremely hard,Moderately frustrating,Very interesting,Slightly successful/ satisfied,Not confident at all,Not at all,"I am not too familiar with Machine Learning Models, so I really used Gemini to guide me through.","I asked Gemini to help debug my code, help generate how to do the regression calculations, with help understanding the data and how to use Machine Learning to model it.",I found GenAI extremely helpful during the task because I am not too confident on this subject so it helped a lot.,I think allowing us to be able to not have the character limit that Gemini has would improve my performance.,I did not work with peers and classmates on this assignment.,,jgu2,2,5,5,3,4,1,1
4/14/2025 10:55:39,<EMAIL>,Venkata Himakar Yanamandra,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Moderately frustrating,Moderately interesting,Extremely successful/ satisfied,Very confident,Not at all,"There are no limitations when we have GenAI in terms of implementation, it boil down how can you drive maximum impact","To understand errors, analysis part",I found it helpful but gemini innately giving errors which had to be manually fixed,"Help generate slightly different multiple versions of the final assignment once done, make GEN AI evaluate which approach will get the highest grade and highest impact for stakeholders",NA,The first assignment grading was very a bit confusing which led to checking the assignment multiple times to make sure rubric and any other possible cases for marking are taken care off .,hyanaman,5,3,4,3,3,1,4
4/14/2025 11:07:13,<EMAIL>,Caroline Fan,Colab's innate GenAI (Gemini),Slightly difficult,Slightly effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,None,Help me generate code quickly and debug,Very helpful,None,None,,sijiaf,4,2,2,2,4,1,4
4/14/2025 12:24:01,<EMAIL>,wenyili,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,havn't fully understand how to use those dif models,using prompt to generate coding,"yes, cause it can illustrate more detailed",/,/,,wenyili,3,3,3,3,3,3,3
4/14/2025 12:31:58,<EMAIL>,Siddhant Sagar,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,Vague output at times,To explore possible solutions,"Yes, mostly helpful",To give better reasoning on approach,More on subjective matters like possible approaches to take,,ssagar2,3,3,3,3,3,3,3
4/14/2025 12:35:17,<EMAIL>,Nakshatra Sharma,Colab's innate GenAI (Gemini),Very difficult,Moderately effortful,Very frustrating,Moderately interesting,Very successful/ satisfied,Moderately confident,Moderately,It was challenging to fine-tune models and ensure accurate predictions with complex datasets.,"I used GenAI to generate code, debug issues, and get suggestions for improving model performance.","Yes, it was helpful for speeding up coding, identifying errors, and offering multiple modeling options.",Access to more detailed explanations and guidance on model evaluation metrics would improve performance.,N/A,,naxs,4,4,3,4,3,3,3
4/14/2025 12:36:27,<EMAIL>,Ashwin Swaminathan,ChatGPT,Very difficult,I need to work extremely hard,Extremely frustrating,Slightly interesting,Moderately successful/ satisfied,Slightly confident,Very much,"Initially I used Gemini, but it gave a wrong code which was difficult to fix. ",I used GenAI to generate the code as I was not familiar with ML coding.,GenAI made errors which was difficult to troubleshoot due to my limited prior exposure to ML.,Background working with ML so that I could troubleshoot errors more easily.,I needed help in understanding basic ML concepts so that I could structure my prompts accordingly,This task was very challenging for people with limited or no prior knowledge of Machine Learning programming. It was also very difficult to interpret the results. ,ashwin2,3,4,5,5,2,4,2
4/14/2025 14:04:08,<EMAIL>,Sumukh Gadavilli,"Colab's innate GenAI (Gemini), ChatGPT",Very difficult,Very effortful,Very frustrating,Extremely interesting,Moderately successful/ satisfied,Moderately confident,Moderately,Finding exact input formats needed to feed it to a function,To give me a few example codes and their executes,"Yes, it gave me pointers well",Have an opportunity to re learn to code ,Understadning syntax,,gsumukh,3,4,4,4,5,3,3
4/16/2025 11:56:30,<EMAIL>,Ansh Pandey,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Very frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,very hard to broadly think about what and how to develop the model,Gemini to brainstorm models that i. build with respect to data available and what the rtaget variables can be. obviously also used to generate code and run evaluations,"Yes, for coming up with ideas and generating code",Debugging is extremely poor- I won't use gemini after this course,none,,anshp,4,4,4,4,4,1,4
4/16/2025 12:00:43,<EMAIL>,Tejas Sohani,Colab's innate GenAI (Gemini),Slightly difficult,Slightly effortful,Not frustrating at all,Very interesting,Very successful/ satisfied,Very confident,A little bit,To know the model evaluation a little bit Challenging ,To generate python code,Yes GEN AI was useful for writing python code,May be if GEN AI helps to learn more on python with prompts instead of generating complete code,To understand the feature selection,,tsohani,4,2,2,1,4,2,4
4/17/2025 6:06:21,<EMAIL>,Yi Wen Tan,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Moderately frustrating,Very interesting,Moderately successful/ satisfied,Moderately confident,Very much,Drafting the prompt to get the GenAI to do what I wanted it to do.,To generate the code for the prompts I drafted.,Very helpful. It could even generate me some basic insights which I could investigate and expand on further.,Some basic suggestions on the types of ML models to use would be helpful.,"Because I had missed the classes due to my mum's passing, I enlisted the help of a classmate to walk me through her basic steps for the assignment before I attempted to do mine.",NIL.,yiwentan,3,4,4,3,4,4,3
