andrewid,hw,task,item,grader,score,notes
aafonja,hw0,t1_data_clean,col1,jk,1,checked for unique values
aafonja,hw0,t1_data_clean,col2,jk,3,
aafonja,hw0,t1_data_clean,col3,jk,1,checked for unique values
aafonja,hw0,t1_data_clean,col4,jk,0,
aafonja,hw0,t1_data_clean,col5,jk,0,
aafonja,hw0,t2_eda,q1,jk,3,
aafonja,hw0,t2_eda,a1,jk,0,
aafonja,hw0,t2_eda,p1,jk,3,
aafonja,hw0,t2_eda,q2,jk,0,
aafonja,hw0,t2_eda,a2,jk,0,
aafonja,hw0,t2_eda,p2,jk,0,
aafonja,hw0,t2_eda,q3,jk,0,
aafonja,hw0,t2_eda,a3,jk,0,
aafonja,hw0,t2_eda,p3,jk,0,
aafonja,hw0,t2_eda,q4,jk,0,
aafonja,hw0,t2_eda,a4,jk,0,
aafonja,hw0,t2_eda,p4,jk,0,
aafonja,hw0,t2_eda,q5,jk,0,
aafonja,hw0,t2_eda,a5,jk,0,
aafonja,hw0,t2_eda,p5,jk,0,
aafonja,hw0,t3_ml,s1_train_test,jk,1,attempt was made
aafonja,hw0,t3_ml,s2_build_model,jk,0,
aafonja,hw0,t3_ml,s3_calc_performance,jk,0,
aafonja,hw0,t3_ml,s4_explain_feat_model,jk,0,
aafonja,hw0,t3_ml,s5_best_model,jk,0,
aafonja,hw0,t4_data_story,c1_goal,jk,0,
aafonja,hw0,t4_data_story,c2_stats_ml,jk,0,
aafonja,hw0,t4_data_story,c3_viz,jk,0,
aafonja,hw0,t4_data_story,c4_comm,jk,0,
aafonja,hw0,t4_data_story,c5_data_limit,jk,0,
aafonja,hw0,t1_data_clean,col1,yb,0,"does not actually clean, just checks for unique values"
aafonja,hw0,t1_data_clean,col2,yb,2,does not drop NaN
aafonja,hw0,t1_data_clean,col3,yb,0,
aafonja,hw0,t1_data_clean,col4,yb,0,
aafonja,hw0,t1_data_clean,col5,yb,0,
aafonja,hw0,t2_eda,q1,yb,3,advanced question
aafonja,hw0,t2_eda,a1,yb,0,
aafonja,hw0,t2_eda,p1,yb,2,bar chart is not needed and does not explain anything
aafonja,hw0,t2_eda,q2,yb,0,
aafonja,hw0,t2_eda,a2,yb,0,
aafonja,hw0,t2_eda,p2,yb,0,
aafonja,hw0,t2_eda,q3,yb,0,
aafonja,hw0,t2_eda,a3,yb,0,
aafonja,hw0,t2_eda,p3,yb,0,
aafonja,hw0,t2_eda,q4,yb,0,
aafonja,hw0,t2_eda,a4,yb,0,
aafonja,hw0,t2_eda,p4,yb,0,
aafonja,hw0,t2_eda,q5,yb,0,
aafonja,hw0,t2_eda,a5,yb,0,
aafonja,hw0,t2_eda,p5,yb,0,
aafonja,hw0,t3_ml,s1_train_test,yb,0,"puts something, but does not run (""Engine Size"" => where did that come from?)"
aafonja,hw0,t3_ml,s2_build_model,yb,0,
aafonja,hw0,t3_ml,s3_calc_performance,yb,0,
aafonja,hw0,t3_ml,s4_explain_feat_model,yb,0,
aafonja,hw0,t3_ml,s5_best_model,yb,0,
aafonja,hw0,t4_data_story,c1_goal,yb,0,
aafonja,hw0,t4_data_story,c2_stats_ml,yb,0,
aafonja,hw0,t4_data_story,c3_viz,yb,0,
aafonja,hw0,t4_data_story,c4_comm,yb,0,
aafonja,hw0,t4_data_story,c5_data_limit,yb,0,
suhailk,hw0,t1_data_clean,col1,jk,0,
suhailk,hw0,t1_data_clean,col2,jk,2,does not convert to float or check for outliers
suhailk,hw0,t1_data_clean,col3,jk,2,does not convert to float
suhailk,hw0,t1_data_clean,col4,jk,2,conversion from 'four' to '4'; no conversion to int
suhailk,hw0,t1_data_clean,col5,jk,1,replacing w/ cleaned data
suhailk,hw0,t2_eda,q1,jk,3,
suhailk,hw0,t2_eda,a1,jk,3,
suhailk,hw0,t2_eda,p1,jk,2,bar chart ineffective for mean price
suhailk,hw0,t2_eda,q2,jk,1,simple question
suhailk,hw0,t2_eda,a2,jk,3,
suhailk,hw0,t2_eda,p2,jk,3,
suhailk,hw0,t2_eda,q3,jk,1,simple categorical analysis
suhailk,hw0,t2_eda,a3,jk,3,
suhailk,hw0,t2_eda,p3,jk,2,bar chart not very effective for visualization
suhailk,hw0,t2_eda,q4,jk,2,intermediate
suhailk,hw0,t2_eda,a4,jk,2,"does not calculate percentages, only raw counts"
suhailk,hw0,t2_eda,p4,jk,2,does not answer question; just bar chart of how many sold per year
suhailk,hw0,t2_eda,q5,jk,0,
suhailk,hw0,t2_eda,a5,jk,0,
suhailk,hw0,t2_eda,p5,jk,0,
suhailk,hw0,t3_ml,s1_train_test,jk,2,"test=0.4 kinda high, x&y swapped"
suhailk,hw0,t3_ml,s2_build_model,jk,2,no documentation of what model is predicting
suhailk,hw0,t3_ml,s3_calc_performance,jk,2,plots residual errors
suhailk,hw0,t3_ml,s4_explain_feat_model,jk,0,
suhailk,hw0,t3_ml,s5_best_model,jk,0,only linear regression
suhailk,hw0,t4_data_story,c1_goal,jk,2,could use more insights/explanations
suhailk,hw0,t4_data_story,c2_stats_ml,jk,2,lacks statistics
suhailk,hw0,t4_data_story,c3_viz,jk,0,no visualization
suhailk,hw0,t4_data_story,c4_comm,jk,2,basic analysis
suhailk,hw0,t4_data_story,c5_data_limit,jk,0,
suhailk,hw0,t1_data_clean,col1,yb,0,
suhailk,hw0,t1_data_clean,col2,yb,2,does not change to int/float and keeps it as string
suhailk,hw0,t1_data_clean,col3,yb,1,does not change to int/float and keeps it as string; also does not take into consideration of other variations
suhailk,hw0,t1_data_clean,col4,yb,1,does not change to int/float and keeps it as string; also does not take into consideration of other variations
suhailk,hw0,t1_data_clean,col5,yb,1,using cleaned dataset to clean; also turns some cases into NaN
suhailk,hw0,t2_eda,q1,yb,2,intermediate question
suhailk,hw0,t2_eda,a1,yb,3,
suhailk,hw0,t2_eda,p1,yb,2,bar chart is not needed/ineffective
suhailk,hw0,t2_eda,q2,yb,1,simple question
suhailk,hw0,t2_eda,a2,yb,3,
suhailk,hw0,t2_eda,p2,yb,3,
suhailk,hw0,t2_eda,q3,yb,1,simple question
suhailk,hw0,t2_eda,a3,yb,0,no answer
suhailk,hw0,t2_eda,p3,yb,3,
suhailk,hw0,t2_eda,q4,yb,2,intermediate question
suhailk,hw0,t2_eda,a4,yb,0,no answer
suhailk,hw0,t2_eda,p4,yb,3,
suhailk,hw0,t2_eda,q5,yb,0,
suhailk,hw0,t2_eda,a5,yb,0,
suhailk,hw0,t2_eda,p5,yb,0,
suhailk,hw0,t3_ml,s1_train_test,yb,2,x/y swapped and test size seems a bit too much
suhailk,hw0,t3_ml,s2_build_model,yb,1,what is the specific question?
suhailk,hw0,t3_ml,s3_calc_performance,yb,2,"no rmse, but does residual error plot"
suhailk,hw0,t3_ml,s4_explain_feat_model,yb,0,
suhailk,hw0,t3_ml,s5_best_model,yb,0,
suhailk,hw0,t4_data_story,c1_goal,yb,1,what is the problem/objective that is being asked?
suhailk,hw0,t4_data_story,c2_stats_ml,yb,2,lacks depth
suhailk,hw0,t4_data_story,c3_viz,yb,0,does not include visualization
suhailk,hw0,t4_data_story,c4_comm,yb,1,unclear explanation
suhailk,hw0,t4_data_story,c5_data_limit,yb,0,does not talk about limitations
sohammon,hw0,t1_data_clean,col1,jk,2,does not convert to float or check for outliers
sohammon,hw0,t1_data_clean,col2,jk,2,"could check for missing values,  convert to float, check outliers"
sohammon,hw0,t1_data_clean,col3,jk,2,
sohammon,hw0,t1_data_clean,col4,jk,3,
sohammon,hw0,t1_data_clean,col5,jk,3,
sohammon,hw0,t2_eda,q1,jk,3,
sohammon,hw0,t2_eda,a1,jk,2,no statistics
sohammon,hw0,t2_eda,p1,jk,3,
sohammon,hw0,t2_eda,q2,jk,0,
sohammon,hw0,t2_eda,a2,jk,0,
sohammon,hw0,t2_eda,p2,jk,0,
sohammon,hw0,t2_eda,q3,jk,0,
sohammon,hw0,t2_eda,a3,jk,0,
sohammon,hw0,t2_eda,p3,jk,0,
sohammon,hw0,t2_eda,q4,jk,0,
sohammon,hw0,t2_eda,a4,jk,0,
sohammon,hw0,t2_eda,p4,jk,0,
sohammon,hw0,t2_eda,q5,jk,0,
sohammon,hw0,t2_eda,a5,jk,0,
sohammon,hw0,t2_eda,p5,jk,0,
sohammon,hw0,t3_ml,s1_train_test,jk,0,
sohammon,hw0,t3_ml,s2_build_model,jk,0,
sohammon,hw0,t3_ml,s3_calc_performance,jk,0,
sohammon,hw0,t3_ml,s4_explain_feat_model,jk,0,
sohammon,hw0,t3_ml,s5_best_model,jk,0,
sohammon,hw0,t4_data_story,c1_goal,jk,0,
sohammon,hw0,t4_data_story,c2_stats_ml,jk,0,
sohammon,hw0,t4_data_story,c3_viz,jk,0,
sohammon,hw0,t4_data_story,c4_comm,jk,0,
sohammon,hw0,t4_data_story,c5_data_limit,jk,0,
sohammon,hw0,t1_data_clean,col1,yb,0,only copied the first 5 rows from df_cars.head() and copied the entire thing as string...
sohammon,hw0,t1_data_clean,col2,yb,0,
sohammon,hw0,t1_data_clean,col3,yb,0,
sohammon,hw0,t1_data_clean,col4,yb,0,
sohammon,hw0,t1_data_clean,col5,yb,0,
sohammon,hw0,t2_eda,q1,yb,3,advanced question
sohammon,hw0,t2_eda,a1,yb,1,
sohammon,hw0,t2_eda,p1,yb,1,
sohammon,hw0,t2_eda,q2,yb,0,
sohammon,hw0,t2_eda,a2,yb,0,
sohammon,hw0,t2_eda,p2,yb,0,
sohammon,hw0,t2_eda,q3,yb,0,
sohammon,hw0,t2_eda,a3,yb,0,
sohammon,hw0,t2_eda,p3,yb,0,
sohammon,hw0,t2_eda,q4,yb,0,
sohammon,hw0,t2_eda,a4,yb,0,
sohammon,hw0,t2_eda,p4,yb,0,
sohammon,hw0,t2_eda,q5,yb,0,
sohammon,hw0,t2_eda,a5,yb,0,
sohammon,hw0,t2_eda,p5,yb,0,
sohammon,hw0,t3_ml,s1_train_test,yb,0,attempted something but not really
sohammon,hw0,t3_ml,s2_build_model,yb,0,
sohammon,hw0,t3_ml,s3_calc_performance,yb,0,
sohammon,hw0,t3_ml,s4_explain_feat_model,yb,0,
sohammon,hw0,t3_ml,s5_best_model,yb,0,
sohammon,hw0,t4_data_story,c1_goal,yb,0,nothing was written
sohammon,hw0,t4_data_story,c2_stats_ml,yb,0,
sohammon,hw0,t4_data_story,c3_viz,yb,0,
sohammon,hw0,t4_data_story,c4_comm,yb,0,
sohammon,hw0,t4_data_story,c5_data_limit,yb,0,
