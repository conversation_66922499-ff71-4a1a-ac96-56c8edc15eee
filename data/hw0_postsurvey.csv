<PERSON><PERSON><PERSON>,Email <PERSON>dress,Name,task_used_llm,task_no_llm,used_llm,perceived_difficulty_data_cleaning,perceived_difficulty_eda,perceived_difficulty_ml,perceived_difficulty_data_storytelling,perceived_effort_data_cleaning,perceived_effort_eda,perceived_effort_ml,perceived_effort_data_storytelling,perceived_stress_data_cleaning,perceived_stress_eda,perceived_stress_ml,perceived_stress_data_storytelling,perceived_engagement_data_cleaning,perceived_engagement_eda,perceived_engagement_ml,perceived_engagement_data_storytelling,perceived_success_data_cleaning,perceived_success_eda,perceived_success_ml,perceived_success_data_storytelling,confidence_data_cleaning,confidence_eda,confidence_ml,confidence_data_storytelling,helpseek_data_cleaning,helpseek_eda,helpseek_ml,helpseek_data_storytelling,challenge_no_llm,challenge_used_llm,llm_use,llm_helpful,help_needed,help_peer,Link to Zoom recording,andrewid,perceived_success_data_cleaning_score,perceived_success_eda_score,perceived_success_ml_score,perceived_success_data_storytelling_score,perceived_difficulty_data_cleaning_score,perceived_difficulty_eda_score,perceived_difficulty_ml_score,perceived_difficulty_data_storytelling_score,perceived_effort_data_cleaning_score,perceived_effort_eda_score,perceived_effort_ml_score,perceived_effort_data_storytelling_score,perceived_stress_data_cleaning_score,perceived_stress_eda_score,perceived_stress_ml_score,perceived_stress_data_storytelling_score,perceived_engagement_data_cleaning_score,perceived_engagement_eda_score,perceived_engagement_ml_score,perceived_engagement_data_storytelling_score,helpseek_data_cleaning_score,helpseek_eda_score,helpseek_ml_score,helpseek_data_storytelling_score,confidence_data_cleaning_score,confidence_eda_score,confidence_ml_score,confidence_data_storytelling_score
3/12/2025 13:52:32,<EMAIL>,Rupal Chauhan,"['Part 2. Exploratory data analysis', 'Part 3. Machine learning', 'Part 4. Data-driven storytelling']",['Part 1. Data cleaning'],Colab's innate Gemini,Slightly difficult,Moderately difficult,Extremely difficult,Very difficult,Very effortful,Very effortful,Very effortful,Very effortful,Very frustrating,Very frustrating,Very frustrating,Very frustrating,Moderately interesting,Moderately interesting,Moderately interesting,Moderately interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately confident,Moderately confident,Moderately confident,Moderately confident,A little bit,A little bit,A little bit,A little bit,writing the code,giving the correct prompt,for coding,yes because it helped me with the coding,help with prompt clarity,with the prompts,https://cmu.zoom.us/rec/share/sDFjSKelMa_p3RQp7Cgz_n1SgwCkiSXu4H5EZ36qRkVAWUPBs2ddNxL3-XPUDwl1.ABQv-4U6k0CWPPnt,rupalc,3,3,3,3,2,3,5,4,4,4,4,4,4,4,4,4,3,3,3,3,2,2,2,2,3,3,3,3
3/12/2025 13:52:50,<EMAIL>,Eason Chen,['Part 4. Data-driven storytelling'],"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning', 'Part 4. Data-driven storytelling']","No Any (wait, I thought I can't use GenAI for any tasks?)",Not difficult at all,Slightly difficult,Very difficult,Very difficult,I do not need to work hard at all,Very effortful,I need to work extremely hard,I need to work extremely hard,Slightly frustrating,Extremely frustrating,Extremely frustrating,Extremely frustrating,Very interesting,Very interesting,Moderately interesting,Very interesting,Extremely successful/ satisfied,Moderately successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Extremely confident,Extremely confident,Extremely confident,Extremely confident,Not at all,Not at all,Not at all,Not at all,Syntax error and how to call the right function,N/A,N/A,N/A,N/A,Tell peers where to find documents,https://cmu.zoom.us/rec/share/xzD_p7iA7wY9ikcfu4dbkmI7ccfd-Ki4HbKV803R6FlzgHCMdTg5kV4v907RUbWr.d-hlanCXrXF2DBKg?pwd=fmhAIGtAVIt6MTFbQO0u9GE8qpG8ucwj,ishengc,5,3,2,2,1,2,4,4,1,4,5,5,2,5,5,5,4,4,3,4,1,1,1,1,5,5,5,5
3/12/2025 13:53:09,<EMAIL>,Bhavya Arora,"['Part 2. Exploratory data analysis', 'Part 3. Machine learning']","['Part 1. Data cleaning', 'Part 4. Data-driven storytelling']","Colab's innate Gemini, ChatGPT",Extremely difficult,Extremely difficult,Extremely difficult,Extremely difficult,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Extremely frustrating,Extremely frustrating,Extremely frustrating,Extremely frustrating,Moderately interesting,Slightly interesting,Slightly interesting,Slightly interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Very much,Moderately,Very much,A little bit,Less knowledge of coding ,understanding errors ,to add codes ,yes it is helpful to generate code,na,how to use google collab and for the errors,https://drive.google.com/drive/folders/19QgWSz_nKACDWlWboHimeTZ_o4l-0CcE?usp=sharing,barora,2,2,2,2,5,5,5,5,5,5,5,5,5,5,5,5,3,2,2,2,4,3,4,2,1,1,1,1
3/12/2025 13:53:48,<EMAIL>,Grace Liao,['Part 3. Machine learning'],"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 4. Data-driven storytelling']",ChatGPT,Slightly difficult,Slightly difficult,Moderately difficult,Slightly difficult,Slightly effortful,Slightly effortful,Moderately effortful,Slightly effortful,Not frustrating at all,Not frustrating at all,Not frustrating at all,Not frustrating at all,Very interesting,Very interesting,Very interesting,Very interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Moderately successful/ satisfied,Slightly successful/ satisfied,Slightly confident,Slightly confident,Extremely confident,Slightly confident,Not at all,Not at all,Not at all,Not at all,kind of,no,i asked questions,kind of,ask better questions,na,https://drive.google.com/drive/folders/1f65067WlLMOerJSyOesTafybprOQWOKx?usp=sharing,gracelia,2,2,3,2,2,2,3,2,2,2,3,2,1,1,1,1,4,4,4,4,1,1,1,1,2,2,5,2
3/12/2025 13:56:18,<EMAIL>,Ankit Shukla,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","Colab's innate Gemini, Perplexity",Extremely difficult,Extremely difficult,Extremely difficult,Extremely difficult,Very effortful,Very effortful,Very effortful,Very effortful,Slightly frustrating,Slightly frustrating,Slightly frustrating,Slightly frustrating,Moderately interesting,Moderately interesting,Moderately interesting,Moderately interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately confident,Moderately confident,Moderately confident,Moderately confident,A little bit,A little bit,A little bit,A little bit,"Coding part, since I was out of practice.",Trying to get good answers from AI. I was getting generic answers.,Asked it questions related to the assignment.,"It was useful, got an idea about which models , codes etc.",More time - I think that will be sufficient.,"Just, what to do, explaining the question etc.",https://cmu.zoom.us/rec/share/Wm50YCrGEX-Gb93OO3WfsW9iWGJ-9bmFfW9H2N5FwTrSoXzpu8PcQptiNls3n0Sq.WbTEEECtqNgTC1r0,ankitshu,3,3,3,3,5,5,5,5,4,4,4,4,2,2,2,2,3,3,3,3,2,2,2,2,3,3,3,3
3/12/2025 13:56:58,<EMAIL>,Ansh Pandey,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning']",['Part 4. Data-driven storytelling'],"Colab's innate Gemini, perplexity",Slightly difficult,Moderately difficult,Extremely difficult,Slightly difficult,Very effortful,Very effortful,I need to work extremely hard,Moderately effortful,Very frustrating,Very frustrating,Very frustrating,Not frustrating at all,Extremely interesting,Extremely interesting,Extremely interesting,Moderately interesting,Moderately successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Moderately successful/ satisfied,Very confident,Moderately confident,Slightly confident,Moderately confident,A little bit,Not at all,Not at all,Not at all,constructing the final response as I used Ai for all first 3 questions,"Prompting the right information to get the desired result. In case of a bug, it was hard to understand what the bug actually was and how to resolve it",Perplexity on chatGPT4o and in jupyter notebook 'generate' option,Yes i did except that you need to know basics of coding and then prompt accordingly when using IA to write code,Understanding basics of coding,"What is the goal of the session, just that",https://cmu.zoom.us/rec/share/4TMDVD_gGmG4QHMyVGiC8dP5Y0QPLUdtgxko2J-xYiYzZ0mDe1yvMTMJKlIysFI5.sqgtVUlfKfBwJy4n,anshp,3,2,2,3,2,3,5,2,4,4,5,3,4,4,4,1,5,5,5,3,2,1,1,1,4,3,2,3
3/12/2025 14:00:25,<EMAIL>,venkata himakar yanamandra,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Moderately difficult,Very difficult,Slightly difficult,Moderately difficult,Moderately effortful,Moderately effortful,Slightly effortful,Moderately effortful,Moderately frustrating,Extremely frustrating,Slightly frustrating,Slightly frustrating,Moderately interesting,Moderately interesting,Slightly interesting,Slightly interesting,Slightly successful/ satisfied,Moderately successful/ satisfied,Very successful/ satisfied,Moderately successful/ satisfied,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately,Moderately,Moderately,Moderately,had to go through multiple google links to get to specific task,exploratory analysis thinking,to understand the errors and give direction to the code,yes,more clarity,i told them shortcuts,https://cmu.zoom.us/rec/share/6HA1n-x8h-P1KIjweaS4t9OmQAjdXcLJq59bkRtjKWCSQhaUMQP9sux5zLEgumdg.WRwsRxU65xqYOPQO,hyanaman,2,3,4,3,3,4,2,3,3,3,2,3,3,5,2,2,3,3,2,2,3,3,3,3,3,3,3,3
3/12/2025 14:07:10,<EMAIL>,alexding,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Very difficult,Very difficult,Moderately difficult,Moderately difficult,Moderately effortful,Moderately effortful,Very effortful,Very effortful,Very frustrating,Very frustrating,Very frustrating,Moderately frustrating,Moderately interesting,Moderately interesting,Very interesting,Very interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Very successful/ satisfied,Very successful/ satisfied,Slightly confident,Slightly confident,Moderately confident,Moderately confident,Very much,Very much,Moderately,Moderately,hard to find what we excatlly want ,the information is not specific enough,"asking question, especially for coding","really helpful, lays out most of useful coding that miss in memory",if we know more on basic structure for data science knowledge we can do better with genai,"where do I get data from, how can I down load or make a copy of that",https://cmu.zoom.us/rec/share/zQtQ5zWrKUETvdVW2JW67Bz-2KhcfHtUrJpTQV4mZd_utLooJaplIMB3PpvawkPJ.Mlq_pDy0nH6z3bYK Passcode: R4C7Xg1!,alexding,2,2,4,4,4,4,3,3,3,3,4,4,4,4,4,3,3,3,4,4,4,4,3,3,2,2,3,3
3/12/2025 14:09:20,<EMAIL>,Roni Kim,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Extremely difficult,Extremely difficult,Extremely difficult,Extremely difficult,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Extremely frustrating,Extremely frustrating,Extremely frustrating,Extremely frustrating,Very interesting,Extremely interesting,Moderately interesting,Moderately interesting,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not at all,Not at all,A little bit,Not at all,Did not know how to code. ,"Understood the basics and was able to decipher what the code was doing. However, I still wasn't able to extract the correct code in order to get the desired results. ",To write me the code itself. ,"Yes it was helpful, however, without a fundamental understanding of Python, things were still very difficult. ",Having a fundamental understanding of Python. ,I asked them certain things regarding what to do for task 3 and 4 when I was not able to use GenAI. ,https://drive.google.com/drive/folders/1S3is310dsYJ2RNCQ7_NlLlFeVd4_dAY2?usp=drive_link,rkkim,1,1,1,1,5,5,5,5,5,5,5,5,5,5,5,5,4,5,3,3,1,1,2,1,1,1,1,1
3/12/2025 14:11:03,<EMAIL>,Soham Mondal,['Part 3. Machine learning'],"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 4. Data-driven storytelling']",Claude,Moderately difficult,Moderately difficult,Extremely difficult,Very difficult,Very effortful,Very effortful,Very effortful,Very effortful,Moderately frustrating,Moderately frustrating,Very frustrating,Moderately frustrating,Moderately interesting,Moderately interesting,Very interesting,Moderately interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Very successful/ satisfied,Moderately successful/ satisfied,Moderately confident,Moderately confident,Very confident,Moderately confident,Moderately,Moderately,Very much,Moderately,Challenge was putting the right code and iteratively coding on it,Challenge was putting the right codes and questions for each section to Gen AI and iteratively prototyping on it,I used Claude AI specifically for the machine learning part,Yes I found it really helpful. But sometimes it threw error based on the machine learning models it is choosing,I understood the google collab framework a little late like running each cells and understanding prior outputs before putting the next inputs,I did not take any help from peers except for understanding what process they are following,https://cmu.zoom.us/rec/share/0NJqFl2ufUJthfSYpqM72SbpSEker8f7BpwbupKcgxNUPsfhQ9ugJdrPCB-9Bnzz.kePxCrik8TxaXgXh,sohammon,3,3,4,3,3,3,5,4,4,4,4,4,3,3,4,3,3,3,4,3,3,3,4,3,3,3,4,3
3/12/2025 14:23:03,<EMAIL>,Siddhant Sagar,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","Colab's innate Gemini, ChatGPT",Moderately difficult,Very difficult,Very difficult,Very difficult,Slightly effortful,Moderately effortful,Very effortful,Very effortful,Moderately frustrating,Very frustrating,Slightly frustrating,Slightly frustrating,Moderately interesting,Slightly interesting,Very interesting,Extremely interesting,Moderately successful/ satisfied,Slightly successful/ satisfied,Very successful/ satisfied,Not successful/ satisfied at all,Very confident,Slightly confident,Very confident,Moderately confident,Very much,Very much,Very much,A little bit,Asking the right questions,Choosing the right starting point,Get sample code for further refining,Helpful to a large extent,Better understanding of tasks and a starting point,More about the approach to take while solving these problems,https://cmu.zoom.us/rec/share/qFj5gpWq7CPD-jQNoVf-qCzt3TrKXGXSCm7n2auxzKbQUHoamGJNBXJ_GTTggEis.xlE7P-nzHJI8BxIJ?startTime=1741799024000,ssagar2,3,2,4,1,3,4,4,4,2,3,4,4,3,4,2,2,3,2,4,5,4,4,4,2,4,2,4,3
3/12/2025 14:26:00,<EMAIL>,khanishkaa Viknesh,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","Colab's innate Gemini, ChatGPT",Slightly difficult,Slightly difficult,Extremely difficult,Extremely difficult,Slightly effortful,Slightly effortful,I need to work extremely hard,I need to work extremely hard,Slightly frustrating,Slightly frustrating,Very frustrating,Very frustrating,Very interesting,Very interesting,Very interesting,Very interesting,Moderately successful/ satisfied,Slightly successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Slightly confident,Slightly confident,Not confident at all,Not confident at all,A little bit,A little bit,All the time,All the time,I did not have guidance,Just trying to make sense of everything,Inbuild one to explain the code,"yes ,It guided me with every step",Better visuals to help understand codes easier,When I could not use gen AI asked them to explain part3 and part4,https://cmu.zoom.us/rec/share/MhY7u_3bcbr7NZADm6RY8cfJi8dao7hKIdg8aHeBjIetwMkuk7XC-h8NCWOkYEkp.P2sn-1siuu-RCWjg,kviknesh,3,2,1,1,2,2,5,5,2,2,5,5,2,2,4,4,4,4,4,4,2,2,5,5,2,2,1,1
3/12/2025 14:27:32,<EMAIL>,Shriya Jashnani,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",ChatGPT,Moderately difficult,Slightly difficult,Very difficult,Slightly difficult,Moderately effortful,Slightly effortful,I do not need to work hard at all,I do not need to work hard at all,Slightly frustrating,Slightly frustrating,Not frustrating at all,Not frustrating at all,Slightly interesting,Moderately interesting,Slightly interesting,Slightly interesting,Not successful/ satisfied at all,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Not confident at all,Slightly confident,Slightly confident,Moderately confident,A little bit,Not at all,Not at all,Not at all,Figuring out the syntax,Feeding the exact context,uploaded excel to share all the context we were working on,"Yes, it was very helpful",Having more idea about which models work best when,Understanding what all things can be done to clean the data,https://drive.google.com/file/d/1ZipF1AAyVAAqmln0tnlb5BD_1-Vdz3nq/view?usp=sharing,sjashnan,1,2,2,2,3,2,4,2,3,2,1,1,2,2,1,1,2,3,2,2,2,1,1,1,1,2,2,3
3/12/2025 14:31:57,<EMAIL>,Wenyi Li,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Not difficult at all,Not difficult at all,Very difficult,Very difficult,Moderately effortful,Moderately effortful,Very effortful,Very effortful,Slightly frustrating,Slightly frustrating,Slightly frustrating,Slightly frustrating,Moderately interesting,Moderately interesting,Moderately interesting,Moderately interesting,Very successful/ satisfied,Very successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Moderately confident,Moderately confident,Not confident at all,Not confident at all,A little bit,A little bit,Very much,Very much,"I don't have much ML background to support me finish this assignment, still working on that.",Not really much,ask prompt,super helpful,know more about programming and grammar,No,https://cmu.zoom.us/rec/share/dBCYEgOGHNa99i4jDuXLDfm3sMmjTmQ_yitheqVISxSX0mtCVM7ZX53FgxqUF4lV.lt6Q7egTpsUOqqAb  (code： +2@ed=b7,wenyili,4,4,1,1,1,1,4,4,3,3,4,4,2,2,2,2,3,3,3,3,2,2,4,4,3,3,1,1
3/12/2025 14:50:38,<EMAIL>,Vrushal Sakharkar,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning']",['Part 4. Data-driven storytelling'],Perplexity,Very difficult,Extremely difficult,Extremely difficult,Very difficult,Very effortful,Very effortful,Very effortful,Very effortful,Extremely frustrating,Extremely frustrating,Very frustrating,Very frustrating,Moderately interesting,Moderately interesting,Moderately interesting,Moderately interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Not confident at all,Not confident at all,Slightly confident,Slightly confident,Very much,All the time,Very much,Moderately,Weren't able to find reliable information or source to understand given problem statement,I wasn't able to understand the structure and language of the results or query,Provided entire task details and ask GenAI to get the results,I found it very useful during data analysis activity as I was able to get the desired results and visualisation but wasn't able to get the same results during data cleaning activity ,I think my understanding of the topic or querying would help me better leverage GenAI for my tasks,Helped peers to setup the system for the activity and asked help to understand specific activities related to data cleaning and data modelling related queries,https://cmu.zoom.us/rec/share/xnEE8t_4JqY4eAAPdsMZDZ_uv89QB5wZxIFb70o_-NDZ9X8EBY7zUK81z316mP4i.g1xZAiS2gk_uS-Z3  Passcode: zMia?B33,vsakhark,2,2,2,2,4,5,5,4,4,4,4,4,5,5,4,4,3,3,3,3,4,5,4,3,1,1,2,2
3/12/2025 14:52:52,<EMAIL>,Hassaan Pasha,['Part 3. Machine learning'],"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Moderately difficult,Extremely difficult,Extremely difficult,Extremely difficult,Very effortful,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Very frustrating,Extremely frustrating,Extremely frustrating,Extremely frustrating,Extremely interesting,Extremely interesting,Extremely interesting,Extremely interesting,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Very much,Very much,Very much,Very much,To find the right code for the task. I wanted to find all the functions first but failed to especially under the 15 minute time constraint per task,"I did not know what model to utilize.. and how to utilize it.. the genAI gave me a list of models but I could not pick the most relevant one for myself. Moreover, I did not know if the syntax was even correct because I kept running into those errors",I asked for help on the Machine Learning task,"Not very much since I was not familiar with the environment, code, functions or libraries",Understanding the libraries and functions first that I can use for each task and then asking genAI to develop code on it.. that way I can better understand the output,"I asked them what is going on, and what are we supposed to do since it was not very clear ...

Note for next question: The password for Zoom recording is: Q6uh$.sP",https://cmu.zoom.us/rec/play/uPThgw2u-ZhiCPI4LKEKPH7E9tPXSnjjjVkAL6jCbxP1GDnU5QAwJ9tnv2IdZkfG4GfumcfuE6A1TQoO.UAMzuEMuhU0GE0SV?accessLevel=meeting&canPlayFromShare=true&from=share_recording_detail&continueMode=true&componentName=rec-play&originRequestUrl=https%3A%2F%2Fcmu.zoom.us%2Frec%2Fshare%2FiqbqvAifSrZIkfBQW1efJO9rlPU2X7qdijx9NpAU0YqlqKrmmHl9FaqNKVlGCwK3.oqV23kw8vi46Q-Ct,hpasha,1,1,1,1,3,5,5,5,4,5,5,5,4,5,5,5,5,5,5,5,4,4,4,4,1,1,1,1
3/12/2025 15:06:37,<EMAIL>,Tejas Sohani,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Very difficult,Very difficult,Slightly difficult,Extremely difficult,Moderately effortful,Moderately effortful,Moderately effortful,Moderately effortful,Moderately frustrating,Moderately frustrating,Moderately frustrating,Moderately frustrating,Very interesting,Very interesting,Very interesting,Very interesting,Not successful/ satisfied at all,Slightly successful/ satisfied,Moderately successful/ satisfied,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,A little bit,A little bit,Not at all,Not at all,Creating visualisation,Finding the right code,For finding out python code for the task,"not very helpful , but good to find initial code",More insights on what to find to get correct python code,Mostly to understand the task, https://cmu.zoom.us/rec/share/a-UB7hKLrhOD4m1Mw6_Pm4yxy_UrdaTcISRkbgPQd5wSSbv4lK4Uh31m7ykdBAUy.df2T8nG92halEYjQ,tsohani,1,2,3,1,4,4,2,5,3,3,3,3,3,3,3,3,4,4,4,4,2,2,1,1,1,1,1,1
3/12/2025 15:15:25,<EMAIL>,Devisha Tayal,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Very difficult,Extremely difficult,Extremely difficult,Very difficult,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Very frustrating,Extremely frustrating,Extremely frustrating,Moderately frustrating,Very interesting,Very interesting,Very interesting,Very interesting,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Slightly successful/ satisfied,Not confident at all,Not confident at all,Not confident at all,Not confident at all,A little bit,Not at all,Not at all,Not at all,"Having no experience, it was difficult to understand what was expected to be done. ","Even when GenAI was allowed, lack of experience made it difficult to write a powerful prompt.",I used Gen AI for Task 3 and Task 4 and wrote prompts to come up with the model and the report.,It was useful for Task 4 when generating a report. I didn't understand the model generated or how to evaluate the model.,"Prior understanding of the environment, data science concepts, doing tasks without GenAI to actually see how and where GenAI will be helpful.",We discussed about what every Task could potentially mean and their undertsanding.,https://cmu.zoom.us/rec/share/Nb83Apz3MZWR5Cwwy3TOqU1tX-0mqOsptMQDlvZSF4CSUczlawZk8VRSEpCjAB4f.7O6oZv2MNuUqPByd Passcode: Rnv@+c08,dtayal,1,1,1,2,4,5,5,4,5,5,5,5,4,5,5,3,4,4,4,4,2,1,1,1,1,1,1,1
3/12/2025 15:58:13,<EMAIL>,Caroline Fan,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","Colab's innate Gemini, Copilot",Moderately difficult,Not difficult at all,Moderately difficult,Moderately difficult,Very effortful,Slightly effortful,Moderately effortful,Moderately effortful,Very frustrating,Not frustrating at all,Moderately frustrating,Moderately frustrating,Moderately interesting,Moderately interesting,Very interesting,Very interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Very confident,Very confident,Moderately confident,Moderately confident,Not at all,Not at all,Not at all,Not at all,quickly locate the solution for my problem,describe the task to GenAI,copilot,using copilot to code is fast,give responses to genai and iteratively improve the code,nan,https://cmu.zoom.us/rec/share/Rhn-1sziIC9sjsKmyObwb4sptamEC9Dw7N3AgggWgfBbl3OE4hJ-Lkp39zhITDx-.lEeuhKRCgGe32DN-         Password: 015@jq%W,sijiaf,3,3,2,2,3,1,3,3,4,2,3,3,4,1,3,3,3,3,4,4,1,1,1,1,4,4,3,3
3/12/2025 15:59:29,<EMAIL>,Upasna Ahuja,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Slightly difficult,Slightly difficult,Slightly difficult,Slightly difficult,Slightly effortful,Slightly effortful,Slightly effortful,Slightly effortful,Slightly frustrating,Slightly frustrating,Slightly frustrating,Slightly frustrating,Moderately interesting,Moderately interesting,Moderately interesting,Moderately interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately,A little bit,Moderately,A little bit,I reversed the order in which i was supposed to do the tasks in terms of using AI. I couldn't follow a thing as I'm new to DS but once I knew how to use AI and what responses im getting what the code was returning i could work with the assignment but only with AI mostly. 1st 2 parts of the assignment to clean the data and write code was challenging but i ended up using AI for it as well. Sorry.,How to use AI in the 1st place. I am used to using it traditionally in a separate browser. I didn't know we had it embedded and how to use it. once I explored it it was very easy to do the assignment.,"I used AI for everything generating questions, statistics, visualization all the parts. All the tasks but i understood the responses which was great.","Yes, very helpful. I didn't have to code, but i could understand the output which is what matters.","Understanding how to better and smartly use it, it will make me efficient and smarter and i can concentrate on important things like reading the data better and presenting it impactfully.",I asked peers how to use AI? What is the assignment about? When the Prof explained the example I could follow to an extent but had 1 of the example been done in class to understand how to use AI it would've been quicker.,https://cmu.zoom.us/rec/share/7et8v-ThK61QKPd02zk5yxPtVp97rJXp-5eGcHeJtQ5bZpM8Q3UUkQhL3fj39g3c.1TCyvXa7YCU8kXvA Passcode: +0K@5j3A,usa,3,3,3,3,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,2,3,2,3,3,3,3
3/12/2025 16:31:00,<EMAIL>,Yi Wen Tan,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning']",['Part 4. Data-driven storytelling'],Colab's innate Gemini,Extremely difficult,Extremely difficult,Very difficult,Extremely difficult,I need to work extremely hard,I need to work extremely hard,Very effortful,I need to work extremely hard,Extremely frustrating,Extremely frustrating,Very frustrating,Extremely frustrating,Not interesting at all,Moderately interesting,Extremely interesting,Moderately interesting,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not at all,Not at all,Not at all,Not at all,Did not know where to start at all.,The noisy classroom environment was not conducive for me to write prompts properly and troubleshoot with the Gen AI.,Write the prompt to get the Gen AI generate the specific python code for me.,"Yes, it was useful as I had basic python knowledge but I had forgotten all the code commands like importing the libraries and getting the value to uppercase.",I found the inbuild code suggestions useful after I write the comments in the code block to job my memory. ,My peers did not understand the basics of programming so they did not understand why they couldn't run the specific code block as they did not run the front code first to load the dataframe. They also did not understand the concept of importing the libraires to perform some of the data analysis like clustering and regression.,https://cmu.zoom.us/rec/share/TVzB_0PC4u0BQbmLNtoUatbi9ngdzXj-CshbG8ktimr-8PcmUAN-Uey1FhSx9vfE.QH9w1X6hVJwXcaB1 Passcode: A9Q^U+c5,yiwentan,1,1,1,1,5,5,4,5,5,5,4,5,5,5,4,5,1,3,5,3,1,1,1,1,1,1,1,1
3/12/2025 16:50:16,<EMAIL>,Angelica Chaves,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Slightly difficult,Moderately difficult,Very difficult,Very difficult,Moderately effortful,Moderately effortful,Very effortful,Very effortful,Moderately frustrating,Moderately frustrating,Very frustrating,Very frustrating,Moderately interesting,Moderately interesting,Very interesting,Very interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Moderately confident,Moderately confident,Not confident at all,Not confident at all,Not at all,Not at all,A little bit,A little bit,Identifying where to go to find the right resources to start with the assigned task,How to structure/prompt my questions to get a valuable coding response,To debug my syntax errors and accelerate how to get the most effective code to do my assigned tasks,"Yes, because it accelerates your research time and you can focus on what really matter (interpret your results or thinking more about asked questions)",Understand more about prompting engineering to ask direct questions and get relevant answers to the problem you're trying to solve,Where to download the right tools and where to find the right information,https://drive.google.com/drive/folders/1DVbC6Qr7Z8xBsf82EnbNgcpXyCaQlYCt?usp=sharing,achavess,3,3,1,1,2,3,4,4,3,3,4,4,3,3,4,4,3,3,4,4,1,1,2,2,3,3,1,1
3/12/2025 17:02:52,<EMAIL>,Eliana Huang,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Extremely difficult,Extremely difficult,Extremely difficult,Extremely difficult,Moderately effortful,Moderately effortful,Very effortful,Very effortful,Very frustrating,Very frustrating,Extremely frustrating,Extremely frustrating,Slightly interesting,Slightly interesting,Slightly interesting,Slightly interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Not at all,Not at all,Not at all,Not at all,I didnt know any knowledge on ML. Not sure what kind of answer it was looking for ,Not sure if it was giving the right answers,"i asked genAI to read the questions and tell me what its asking for, then asking it to generate code and fix code",yes i found it helpful. Gave me a better idea of what kind of answer to give,if i had prior knowledge on this topic,i didnt ask,https://drive.google.com/file/d/1E-JL3vigPejQc7J9nYi3sv5Dpad1oHPh/view?usp=sharing,elianah,2,2,1,1,5,5,5,5,3,3,4,4,4,4,5,5,2,2,2,2,1,1,1,1,1,1,1,1
3/12/2025 17:08:47,<EMAIL>,Manasa Nandigama,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Very difficult,Very difficult,Extremely difficult,Very difficult,Very effortful,Very effortful,I need to work extremely hard,Very effortful,Slightly frustrating,Slightly frustrating,Moderately frustrating,Slightly frustrating,Very interesting,Very interesting,Very interesting,Very interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Moderately confident,Moderately confident,Not confident at all,Slightly confident,Not at all,Not at all,A little bit,Not at all,"I am not well versed with machine learning, so I was not sure what to do.",The prompts to give are a bit challenging.,I asked the AI to give me the commands.,definitely yes. As I'm new to this with AI it would be helpful for me to understand what to do and where I'm going wrong. I'll be able to learn easily.,With GenAI I think I'll be able to learn easily as it'll be helping me if I'm making a mistake.,I just asked a couple of questions like what to do or how can I prompt this.,https://drive.google.com/file/d/1drZqtF3WsyeUrolBwQrsO0aUBL4AAQh4/view?usp=share_link,mnandiga,3,3,2,2,4,4,5,4,4,4,5,4,2,2,3,2,4,4,4,4,1,1,2,1,3,3,1,2
3/12/2025 17:42:32,<EMAIL>,Aminat Afonja,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Extremely difficult,Extremely difficult,Extremely difficult,Extremely difficult,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Slightly frustrating,Slightly frustrating,Slightly frustrating,Slightly frustrating,Moderately interesting,Moderately interesting,Moderately interesting,Moderately interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Slightly confident,Slightly confident,Not confident at all,Not confident at all,Not at all,A little bit,A little bit,Not at all,"I had challenges understanding the task from the beginning, it became difficult for me to follow through at the same pace.",Understanding exactly what was required to be done.,I used it to complete my task,Yes. helpful,Better understanding of the problem and task.,Clarity on the required task,https://drive.google.com/file/d/1a5JZK-WnN7ZMeBxhuHxzv987C5EL8664/view?usp=drive_link,aafonja,2,2,1,1,5,5,5,5,5,5,5,5,2,2,2,2,3,3,3,3,1,2,2,1,2,2,1,1
3/12/2025 18:03:46,<EMAIL>,Charlene Lin,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Extremely difficult,Extremely difficult,Extremely difficult,Extremely difficult,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Extremely frustrating,Extremely frustrating,Extremely frustrating,Extremely frustrating,Not interesting at all,Not interesting at all,Not interesting at all,Not interesting at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,All the time,All the time,All the time,All the time,Didn't know where to start,"The GenAI didn’t generate exactly what I wanted, and you can’t fine-tune parts of the result. Instead, you have to regenerate the whole thing, and sometimes, parts that were correct before end up wrong.",Type the prompt in Colab and let it generate the entire code.,"Yes, it got something done at least. Without GenAI, I wouldn’t even know how to start writing a line of Python without additional instructions, and searching on Google took way too much time for this task.","GenAI can create the tasks, and I can improve the results by tweaking the prompt. However, to effectively use Colab and Python for analysis—even with GenAI’s help—I still need to become more familiar with these tools. I can see how GenAI could assist with analysis in Google Spreadsheets, but today’s experience with Colab and Python was just a disaster.","To learn how they interpret the questions, what exactly was expected in this scenario, and what has already been done so I have a clue on how to start.",https://cmu.zoom.us/rec/share/qwLbf0Zrt6bxDqduF3xo3FLPMq_CA2tGAgKWHrRe3sHqpElZFT1Evb1BV_V8UYsV.WyYzOMdCSS01RSU5?startTime=1741798168000 密碼: BQ^?Ym3f,charlenl,1,1,1,1,5,5,5,5,5,5,5,5,5,5,5,5,1,1,1,1,5,5,5,5,1,1,1,1
3/12/2025 18:05:30,<EMAIL>,Sumukh,['Part 1. Data cleaning'],"['Part 2. Exploratory data analysis', 'Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Extremely difficult,Extremely difficult,Extremely difficult,Extremely difficult,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Extremely frustrating,Extremely frustrating,Extremely frustrating,Extremely frustrating,Extremely interesting,Extremely interesting,Extremely interesting,Extremely interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Not confident at all,Not confident at all,Not confident at all,Not confident at all,A little bit,Moderately,A little bit,Not at all,Coding,Picking the right data sets,Create visualisations,"Yes, Good set of alternatives to visualisations",Understanding the AI offerings of Google colab,Qualitative and inference driving,https://drive.google.com/file/d/1Wi50aUIUPl5Zr0eZUHRdAw4xC7Ca6WaS/view?usp=sharing,gsumukh,3,3,3,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,2,3,2,1,1,1,1,1
3/12/2025 18:58:52,<EMAIL>,Mukul Lal,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Moderately difficult,Moderately difficult,Extremely difficult,Extremely difficult,Slightly effortful,Moderately effortful,I need to work extremely hard,I need to work extremely hard,Moderately frustrating,Very frustrating,Extremely frustrating,Extremely frustrating,Moderately interesting,Moderately interesting,Slightly interesting,Not interesting at all,Moderately successful/ satisfied,Moderately successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Moderately confident,Not confident at all,Not confident at all,Not confident at all,Not at all,A little bit,Very much,Very much,"I didn't know what was expected of me to complete the tasks. While I know the concepts of ML models at a high level, coming up with a model was completely out of syllabus for me and left me confused. I could not get to the data-driven storytelling part of the problem, but would have faced similar issues","As I was doing data cleaning and EDA for the first time on Colab, I didn't know what was needed to be done. I have done data cleaning on MS Excel multiple times so had some idea of what was needed. But both the tasks with no direction/guidance were super difficult to perform",I prompted in built GenAI to help with the tasks,"Yes. I think the 3 main ways in which GenAI cleaned the data was helpful and clear for me to follow. It certainly helped in standardizing the data in the dataset. However, I am not sure if the output was exhaustive (e.g., for data cleaning, GenAI removed leading & trailing spaces and just capitalized the entries - is this enough to clean the data?)",Understanding the asks better ,What is expected from each of the tasks,"www.cmu.edu/As mentioned on Slack, I missed recording the session unfortunately",mlal,3,3,1,1,3,3,5,5,2,3,5,5,3,4,5,5,3,3,2,1,1,2,4,4,3,1,1,1
3/12/2025 19:04:15,<EMAIL>,Ram Kaushik Ramalingan,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Slightly difficult,Moderately difficult,Moderately difficult,Moderately difficult,Moderately effortful,Moderately effortful,Slightly effortful,Slightly effortful,Moderately frustrating,Moderately frustrating,Moderately frustrating,Moderately frustrating,Very interesting,Very interesting,Very interesting,Very interesting,Very successful/ satisfied,Very successful/ satisfied,Very successful/ satisfied,Very successful/ satisfied,Very confident,Very confident,Very confident,Very confident,Not at all,Not at all,Not at all,Not at all,Remembering syntax and in-built functions,Validating outputs from GenAI,Used GenAI to generate code snippets,Yes. GenAI provides a great starting point,Better prompts with more specific details,I helped peers who never used Python understand fundamentals of Python,https://cmu.zoom.us/rec/share/3pSVghxDt0aMdb6qo7-jKdt6SokJsc9dCZlwH9vL0QNwBA1EnwNc8Do1rf2XIRsg.6JLOgwQpWxR79cPo,ramkausr,4,4,4,4,2,3,3,3,3,3,2,2,3,3,3,3,4,4,4,4,1,1,1,1,4,4,4,4
3/12/2025 19:47:18,<EMAIL>,Karan Qasba,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Slightly difficult,Slightly difficult,Extremely difficult,Extremely difficult,Slightly effortful,Slightly effortful,I need to work extremely hard,I need to work extremely hard,Slightly frustrating,Slightly frustrating,Extremely frustrating,Extremely frustrating,Not interesting at all,Not interesting at all,Not interesting at all,Not interesting at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Very much,Very much,Very much,Very much,No code or data science background so it was challenging for me,GenAI was giving errors which i couldn't understand,By giving the prompt as given in the task,"Yes, it was helpful because it wrote the code for me",Having a background of how to use GenAI in collab tool,I got help on where to write the prompt and I asked them about the errors i got,https://cmu.zoom.us/rec/share/jrljSIdZqAWBd5SZo7zZdnOVtFIlbPKe25I3Ir0hBg4fPHOCARYpkmt0oi3IDo9E.RFrTpFuIF2Fn9HLm,kqasba,1,1,1,1,2,2,5,5,2,2,5,5,2,2,5,5,1,1,1,1,4,4,4,4,1,1,1,1
3/12/2025 21:12:23,<EMAIL>,Cody Soska,"['Part 2. Exploratory data analysis', 'Part 3. Machine learning']",['Part 1. Data cleaning'],"Colab's innate Gemini, ChatGPT",Moderately difficult,Moderately difficult,Very difficult,Moderately difficult,Moderately effortful,Moderately effortful,Moderately effortful,Moderately effortful,Moderately frustrating,Moderately frustrating,Moderately frustrating,Moderately frustrating,Very interesting,Very interesting,Extremely interesting,Very interesting,Not successful/ satisfied at all,Slightly successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,Moderately,A little bit,Not at all,Not at all,Understanding what the task was asking,"Also understanding what question I was supposed to be answering. To be clear, this wasn't a conceptual understanding - I've done this type of data work before. I simply did not know what was being asked of me by the wording of the questions.","I gave a pre-training prompt explaining what I was doing broadly and requested no output. I then gave a prompt (with expected output) for the specific sub processes I needed the AI to do. For example: 
Training Prompt: ""I'm comparing data about cars and will need to be able to answer several questions, e.g. [list of questions]. Do not generate an output and simply review the [cleaned_data_file provided by instructor].

Few shot prompts: ""From this data, I need to create a tuple of ""Features"" that I can compare against each other. What is the most common method to do this and walk me through each line""

""I would like to see which city has the most used Honda Accords. How do I do this?""","Yes! It's essential, especially early in the learning process. There are a number of ways to get the same result and unless you have a solid understanding of all of the common methods, adapting or fitting even the most basic functions from, like, a stackoverflow is challenging. Using AI let's you craft the prompt and determine and evaluate an acceptable output that is task-specific","Knowing the top 3 methods for doing each common form of data cleaning and analysis. The same goes for ML, though the method is determined more by the type of data and desired behaviour. For data storytelling, having the data in a form that is easily parsed for specific insights is useful and gen AI, with this goal known from the beginning, can pre prepare the data for eventual narrative uses. This is why I found few shot prompting useful on this assignment.","""What is the question here? It just says, 'insights' (or something like that). What do they actually want us to do?""",https://cmu.zoom.us/rec/share/BRaOcqztxFSC345ubb2kYjAvA0qErDYdB8ytwawotjcAq7kQnoSW08lvWJNhFNI8.L6X0FePIXXIFo7-m,csoska,1,2,1,1,3,3,4,3,3,3,3,3,3,3,3,3,4,4,5,4,3,2,1,1,1,1,1,1
3/12/2025 21:28:53,<EMAIL>,Nikhil Medarametla,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Moderately difficult,Moderately difficult,Moderately difficult,Moderately difficult,Very effortful,Very effortful,Very effortful,Very effortful,Moderately frustrating,Moderately frustrating,Moderately frustrating,Moderately frustrating,Moderately interesting,Moderately interesting,Moderately interesting,Moderately interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately confident,Moderately confident,Moderately confident,Moderately confident,Moderately,Moderately,Moderately,Moderately,"Honestly, it was like trying to build a house with just a hammer and some nails. Without the AI, I was constantly wrestling with the sheer volume of data and the complexity of the machine learning process. Keeping track of all the variables, remembering the nuances of different algorithms, and writing the code from scratch? It felt incredibly slow and error-prone. I found myself second-guessing every step, and debugging was a nightmare. Basically, I felt like I was working blindfolded in a room full of puzzle pieces.","Even with the AI, it wasn't always smooth sailing. I had to be super careful about how I phrased my prompts. If I wasn't precise, the AI would sometimes go off on a tangent or give me results that were technically correct but not what I needed. Also, I had to double-check everything, because even the best AI can make mistakes. It's like having a brilliant but sometimes unreliable assistant – you still need to be the editor. And, sometimes, I'd get lost in the AI's output, and it would take a minute to reorient myself.","I used it for just about everything! I'd ask it to clean and preprocess the data, generate code for different machine learning models, evaluate their performance, and even explain the results in plain English. It was like having a super-powered coding partner and data analyst all rolled into one. I'd use it to explore, try out ideas quickly, and generally speed up the whole process. I would also ask it to generate code snippets, and then I would tweak them to fit the exact parameters of the task.","Absolutely, it was a game-changer. It saved me so much time and effort, and it allowed me to explore a wider range of possibilities. Without it, I'd probably still be stuck trying to figure out the basics. It let me focus on the bigger picture – understanding the data, interpreting the results, and making informed decisions. It was like having a cheat code for the boring parts of the job, so I could focus on the fun, interesting bits.","I think I need to get better at communicating with the AI. I need to learn how to ask the right questions and provide clear instructions. I also need to develop a more critical eye, so I can better evaluate the AI's output and identify potential errors or biases. More practice, basically. And more awareness of the underlying assumptions the AI is making. I also think more specific examples, and more feedback loops, would allow me to better refine the AI's output.","Well, ""peers"" in this context is a bit of a stretch, but if I were working with other humans, I'd definitely ask them about the business context, the domain expertise, and their gut feelings about the data. Things that an AI, even a powerful one, might miss. I'd ask questions like:

""What are the key business drivers we're trying to address?""
""What are the common pitfalls in this industry that we should be aware of?""
""What are your hunches about the most important features in this dataset?""
""How would a typical customer react to this particular model output?""
""What are the edge cases that are most likely to cause problems?""
""Can you explain the reasoning behind this data transformation in a way that someone without a technical background could understand?""",https://cmu.zoom.us/rec/share/0MTRvNQPkU0hUNQ8niyxSvn7N6nwc_0RgefOGEaM_Rpnxp84Uc7dFwENVE8ZNbHt.t0PDT6aTfS2hr4no  Passcode: ZGf^s53q,nmedaram,3,3,3,3,3,3,3,3,4,4,4,4,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3
3/12/2025 21:36:55,<EMAIL>,Nivedita Yadav,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Moderately difficult,Slightly difficult,Very difficult,Very difficult,Very effortful,Slightly effortful,Very effortful,Very effortful,Very frustrating,Slightly frustrating,Moderately frustrating,Moderately frustrating,Very interesting,Very interesting,Very interesting,Very interesting,Slightly successful/ satisfied,Moderately successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Not confident at all,Not confident at all,Not confident at all,Not confident at all,A little bit,A little bit,A little bit,A little bit,Didnt know what to do ,Kept getting errors ,Entered prompts to get the code ,Yes,Not sure right now ,Structuring things better. Explaining code ,https://drive.google.com/file/d/1nGLi_r528li_K4mjj2UoF3Hdnwevimfv/view?usp=sharing,nivedity,2,3,1,1,3,2,4,4,4,2,4,4,4,2,3,3,4,4,4,4,2,2,2,2,1,1,1,1
3/12/2025 22:35:13,<EMAIL>,Ashwin Swaminathan,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Very difficult,Extremely difficult,Very difficult,Very difficult,Very effortful,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Extremely frustrating,Extremely frustrating,Very frustrating,Very frustrating,Moderately interesting,Slightly interesting,Moderately interesting,Moderately interesting,Slightly successful/ satisfied,Not successful/ satisfied at all,Moderately successful/ satisfied,Not successful/ satisfied at all,Moderately confident,Not confident at all,Slightly confident,Not confident at all,Moderately,A little bit,A little bit,A little bit,I did not understand the requirement initially and I was struggling to understand the format of the deliverable until we were given a simple example in class. I did not realize until then that we were expected to write actual code to fix the issues (as opposed to giving ideas in plaintext).,"I was successful in doing one task using Colab's AI. I got some minor errors in compiling the code, but I was able to fix them eventually by reading the error descriptions. However, I was unsure what the output represented, and found it difficult to connect it to my prompt.",I relied on Colab's own Gemini AI and avoided asking ChatGPT or Gemini (via Google Search) to explain,GenAI was definitely useful in giving a headstart and a basic sense of what was expected. I might have been able to use it to interpret the results if I got a better understanding of the context of the assignment so I could frame my prompts accordingly.,"To write effective prompts for GenAI, I need clearer understanding of the context of the assignment. There were few errors in the code written by GenAI, so I think I will also need to understand the assignment so that I know where could I expect errors and how would I have to work around or fix them.","I mostly asked peers what did they understand about the tasks, what tools they were using and what's a good place to start.",https://cmu.zoom.us/rec/share/kEIl3ATsjgVsGgda8FP9vV0-MqdRmGgsC9XTfzgO6wOPyeULfvkYoMJmpxmmGnZc.QjQSdg0bVZO3es1h,ashwin2,2,1,3,1,4,5,4,4,4,5,5,5,5,5,4,4,3,2,3,3,3,2,2,2,3,1,2,1
3/12/2025 22:35:22,<EMAIL>,Esha,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",['Part 3. Machine learning'],Colab's innate Gemini,Moderately difficult,Moderately difficult,Extremely difficult,Extremely difficult,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Extremely frustrating,Extremely frustrating,Extremely frustrating,Extremely frustrating,Very interesting,Very interesting,Moderately interesting,Moderately interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Not successful/ satisfied at all,Not successful/ satisfied at all,Moderately confident,Moderately confident,Not confident at all,Not confident at all,Moderately,Moderately,Moderately,Moderately,Not understanding what to do,Was okay to figure next steps,Gemini collab,"Yes, for coding",Seems fine for now,To catchup on the asks of the instructor mostly,https://cmu.zoom.us/rec/share/-Ml9LEVyMzwTDZVl_Hr3nG_ecUvOaFlZ2XDmMBa3uknmx27hwaxiBg4jN7v9HUU.3ZSE_-JGoFe27sfS   | Password: 3+Ad8KdP,elakra,3,3,1,1,3,3,5,5,5,5,5,5,5,5,5,5,4,4,3,3,3,3,3,3,3,3,1,1
3/12/2025 23:25:47,<EMAIL>,Somya Mehta,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","Colab's innate Gemini, ChatGPT",Slightly difficult,Moderately difficult,Moderately difficult,Moderately difficult,Moderately effortful,Moderately effortful,Moderately effortful,Moderately effortful,Moderately frustrating,Slightly frustrating,Not frustrating at all,Not frustrating at all,Slightly interesting,Slightly interesting,Slightly interesting,Slightly interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly confident,Slightly confident,Slightly confident,Slightly confident,A little bit,A little bit,A little bit,A little bit,"Time-consuming research, manual coding",Ensuring correctness,"coding assistance, debugging","Yes! It sped up work, provided clear explanations, and helped refine ideas efficiently.","Learning better prompt engineering, cross-verifying outputs","Discussed edge cases, validated model results, and shared debugging strategies instead of relying solely on GenAI.",https://drive.google.com/file/d/1G09VGsg7qOZqCBdarRqVmcsGNDTrirEN/view?usp=sharing,somyameh,2,2,2,2,2,3,3,3,3,3,3,3,3,2,1,1,2,2,2,2,2,2,2,2,2,2,2,2
3/12/2025 23:29:00,<EMAIL>,Aditya Teja,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",['Part 3. Machine learning'],Colab's innate Gemini,Moderately difficult,Slightly difficult,Very difficult,Very difficult,Very effortful,Moderately effortful,Moderately effortful,Moderately effortful,Slightly frustrating,Slightly frustrating,Slightly frustrating,Slightly frustrating,Very interesting,Very interesting,Very interesting,Very interesting,Moderately successful/ satisfied,Slightly successful/ satisfied,Very successful/ satisfied,Very successful/ satisfied,Very confident,Slightly confident,Very confident,Very confident,Not at all,Not at all,Not at all,Not at all,figuring out the approach,"debugging errors, although only trivial errors like names of data frames or indenting.","Yes, to interact with the code and take relevant help","Yes, it understood the context well",increase the information in the prompt,I didnt interact with my peers during this assigment,https://drive.google.com/drive/folders/18MQyxMTA6xWdYZRlUUC_bFMmFtIOOjfD?usp=sharing,abhimava,3,2,4,4,3,2,4,4,4,3,3,3,2,2,2,2,4,4,4,4,1,1,1,1,4,2,4,4
3/13/2025 0:04:29,<EMAIL>,radhika ,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",['Part 1. Data cleaning'],Colab's innate Gemini,Very difficult,Very difficult,Extremely difficult,Extremely difficult,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,I need to work extremely hard,Moderately frustrating,Moderately frustrating,Moderately frustrating,Moderately frustrating,Extremely interesting,Extremely interesting,Extremely interesting,Extremely interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly successful/ satisfied,Slightly confident,Slightly confident,Not confident at all,Not confident at all,Very much,Moderately,A little bit,A little bit,I do not have a lot of experience coding so it was very tough for me and without genAI I couldn't have completed the task,with genAI it became very easy no challenges after that ,I just types my query in the genAI text box and it gave me the exact answers I wanted ,It was extremely helpful without that I could not have completed any task,if i knew how to code I could used it better also I have never used google colab so if I had a better understanding of how that works then that could also have helped better ,both felt similar,https://drive.google.com/drive/folders/1OYNwOMpEXIYzWzC4T59ZV4nYwDCHKc9_?usp=share_link,radhikaj,2,2,2,2,4,4,5,5,5,5,5,5,3,3,3,3,5,5,5,5,4,3,2,2,2,2,1,1
3/13/2025 2:00:58,<EMAIL>,Eishita Tripathi,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']","['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']",Colab's innate Gemini,Moderately difficult,Very difficult,Very difficult,Very difficult,Moderately effortful,Very effortful,I do not need to work hard at all,I do not need to work hard at all,Moderately frustrating,Moderately frustrating,Not frustrating at all,Not frustrating at all,Extremely interesting,Extremely interesting,Extremely interesting,Extremely interesting,Slightly successful/ satisfied,Slightly successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Not confident at all,Not confident at all,Slightly confident,Slightly confident,Not at all,Not at all,Not at all,Not at all,The direction in which to go for EDA was the most challenging. Figuring out code for data cleaning was tedious. ,Figuring out whether the output from GenAI was meaningful,Enter the task prompts into gemini in collab,"Yes, because it completed the task for me",Clear direction on what we wanted out of the dataset and why ,"My peers asked me how to record the zoom meeting, run the collab notebook, and how to enter the prompts into gemini in the notebook, and how to use the prompts to fix errors in the AI generated code. ",https://cmu.zoom.us/rec/share/4sxwglKaRzp8lcuboXlIeTMy6xx-FLQS0n3Cm30dFoGGKLHkE5Sv5RL8il91ywym.hQTxznWpeu472gP6 Passcode: t9nf@a*4,etripath,2,2,3,3,3,4,4,4,3,4,1,1,3,3,1,1,5,5,5,5,1,1,1,1,1,1,2,2
3/13/2025 3:14:55,<EMAIL>,Suhail Khan,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']","['Part 3. Machine learning', 'Part 4. Data-driven storytelling']",Colab's innate Gemini,Slightly difficult,Slightly difficult,Moderately difficult,Moderately difficult,Slightly effortful,Slightly effortful,Moderately effortful,Moderately effortful,Slightly frustrating,Not frustrating at all,Moderately frustrating,Moderately frustrating,Slightly interesting,Slightly interesting,Moderately interesting,Slightly interesting,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately successful/ satisfied,Moderately confident,Very confident,Very confident,Moderately confident,Not at all,Not at all,Not at all,Not at all,I have to google a lot even for minute or simple tasks.,Somtime it couldn't pick column name correctly.,for coding format majorly,"Yes, with the help of it's easier to find syntax for code, fix errors and auto-complete trivial tasks quickly.","Suggeting potential ways to solve problem, kind of brainstorm together. So, that it pushes more to think.",I didn't asked to any peer for the task.,https://drive.google.com/file/d/19HnH2oWJNQYe-b3kwYY40vwIe4TPZd0L/view?usp=sharing,suhailk,3,3,3,3,2,2,3,3,2,2,3,3,2,1,3,3,2,2,3,2,1,1,1,1,3,4,4,3
