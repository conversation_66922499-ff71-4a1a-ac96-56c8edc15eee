andrewid,hw,task,item,grader,score,notes
aafonja,hw1,t1_data_clean,col1,jk,2,Should drop missing values
aafonja,hw1,t1_data_clean,col2,jk,1,"Drop NaN instead of filling with ""Unknown"", did not parse/transform comma-separated strings into lists of standardized developer names"
aafonja,hw1,t1_data_clean,col3,jk,1,"Drop NaN instead of filling with ""N/A"""
aafonja,hw1,t1_data_clean,col4,jk,2,"Output has both strings and lists, filled missing values with ""Unknown"""
aafonja,hw1,t1_data_clean,col5,jk,2,Not removing 'tbd' leads to NaT values
barora,hw1,t1_data_clean,col1,jk,3,
barora,hw1,t1_data_clean,col2,jk,3,
barora,hw1,t1_data_clean,col3,jk,3,
barora,hw1,t1_data_clean,col4,jk,3,
barora,hw1,t1_data_clean,col5,jk,2,"Doesn't handle European comma decimal (2,3K), removing all commas can affect decimal values (2,3K -> 23K -> 23000 instead of 2300)"
achavess,hw1,t1_data_clean,col1,jk,3,
achavess,hw1,t1_data_clean,col2,jk,1,"Filter out missing values instead of filling with ""Not available"""
achavess,hw1,t1_data_clean,col3,jk,1,"Only extracts first developer (drops all co-develoeprs), no filtering/type-checking (some values are Python lists), no formatting/standardization (""nintendo""/""Nintendo"")"
achavess,hw1,t1_data_clean,col4,jk,1,"We should be dropping missing values, not filling with the mean"
achavess,hw1,t1_data_clean,col5,jk,3,
alexding,hw1,t1_data_clean,col1,jk,3,
alexding,hw1,t1_data_clean,col2,jk,1,"Filter out 'tbd' strings, convert to datetime"
alexding,hw1,t1_data_clean,col3,jk,2,"Doesn't account for 'k' vs. 'K', doesn't handle European comma decimal (2,3K), removing all commas can affect decimal values (2,3K -> 23K -> 23000 instead of 2300)"
alexding,hw1,t1_data_clean,col4,jk,3,
alexding,hw1,t1_data_clean,col5,jk,3,
sijiaf,hw1,t1_data_clean,col1,jk,3,
sijiaf,hw1,t1_data_clean,col2,jk,3,
sijiaf,hw1,t1_data_clean,col3,jk,3,
sijiaf,hw1,t1_data_clean,col4,jk,3,
sijiaf,hw1,t1_data_clean,col5,jk,3,
jgu2,hw1,t1_data_clean,col1,jk,1,"Multiplies every entry by 1000, not just entries with 'k'/'K', doesn't handle European comma decimal (2,3K) (removing all commas can affect decimal values (2,3K -> 23K -> 23000 instead of 2300))"
jgu2,hw1,t1_data_clean,col2,jk,1,"Multiplies every entry by 1000, not just entries with 'K'"
jgu2,hw1,t1_data_clean,col3,jk,2,Remove 'tbd' so you don't get NaT values
jgu2,hw1,t1_data_clean,col4,jk,2,Should drop invalid entries
jgu2,hw1,t1_data_clean,col5,jk,2,Should drop invalid entries
radhikaj,hw1,t1_data_clean,col1,jk,2,Doesn't check for duplicates
radhikaj,hw1,t1_data_clean,col2,jk,2,Should drop missing entries
radhikaj,hw1,t1_data_clean,col3,jk,2,"Filter out missing values, not filling with []; filter out incorrectly formatted entries that look like lists but are strings"
radhikaj,hw1,t1_data_clean,col4,jk,1,Filter out missing values instead of filling with median
radhikaj,hw1,t1_data_clean,col5,jk,1,"Wrapping in brackets does not make it a string, need to convert to list, filter out missing values isntead of filling with """""
mlal,hw1,t1_data_clean,col1,jk,3,
mlal,hw1,t1_data_clean,col2,jk,3,
mlal,hw1,t1_data_clean,col3,jk,2,Filter out entries with missing values
mlal,hw1,t1_data_clean,col4,jk,2,"Doesn't account for malformed stringified lists (""Nintendo, Retro Studios"" without brackets); we need to make sure the content inside is actually list-parsable"
mlal,hw1,t1_data_clean,col5,jk,2,"We expect filtering out release dates that are 'tbd' although manually filling them in works. Think about if we are working with a large dataset with multiple 'tbd' entries, and whether dropping them or manually filling them is more efficient."
somyameh,hw1,t1_data_clean,col1,jk,2,"Normalize capitalization, keeping as a list is optimal, filter out missing values"
somyameh,hw1,t1_data_clean,col2,jk,2,"Capital normalization, keeping as a list is optimal"
somyameh,hw1,t1_data_clean,col3,jk,2,Not removing 'tbd' leads to NaT values
somyameh,hw1,t1_data_clean,col4,jk,2,"Total players column has both 'k' and 'K' as well as European commas (2,3K) (removing all commas can affect decimal values (2,3K -> 23K -> 23000 instead of 2300))"
somyameh,hw1,t1_data_clean,col5,jk,3,
mnandiga,hw1,t1_data_clean,col1,jk,1,"Check/remove duplicates, filter out missing values instead of filling with ""Unknown"""
mnandiga,hw1,t1_data_clean,col2,jk,1,Filter out entries with missing values instead of replacing
mnandiga,hw1,t1_data_clean,col3,jk,1,Filter out entries with missing values instead of replacing. Can be # or lowercase/capitalized strings not list; need to account for malformed stringified lists
mnandiga,hw1,t1_data_clean,col4,jk,2,Filter out entries with missing values instead of replacing
mnandiga,hw1,t1_data_clean,col5,jk,1,"Need to manually deal with special cases, doesn't handle malformed stringified lists, filter out entries with missing values"
anshp,hw1,t1_data_clean,col1,jk,1,Check/filter out for duplicate. You don't call your function anywhere to actually clean your data
anshp,hw1,t1_data_clean,col2,jk,1,"Not removing 'tbd' leads to NaT values, filter out missing values, you don't call your function (no cleaning actually done)"
anshp,hw1,t1_data_clean,col3,jk,1,"Handle malformed stringified lists, capitalization standardization, handle both raw strings AND Python lists, you don't call your function (no cleaning actually done)"
anshp,hw1,t1_data_clean,col4,jk,2,Filter out missing values instead of replacing with mean
anshp,hw1,t1_data_clean,col5,jk,2,Don't replace missing values with median (remove them)
hpasha,hw1,t1_data_clean,col1,jk,3,
hpasha,hw1,t1_data_clean,col2,jk,1,"Convert to list, standardize capitalization"
hpasha,hw1,t1_data_clean,col3,jk,3,
hpasha,hw1,t1_data_clean,col4,jk,2,"Handle malformed stringified lists, capitalization standardization, filtering with rows starting with '[' can drop valid rows (convert ""RPG, Shooter"" to '[""RPG"", ""Shooter""]')"
hpasha,hw1,t1_data_clean,col5,jk,3,
kqasba,hw1,t1_data_clean,col1,jk,2,Did not actually drop duplicates
kqasba,hw1,t1_data_clean,col2,jk,2,"Filter out missing values instead of replacing with ""No description available"""
kqasba,hw1,t1_data_clean,col3,jk,2,Filter out missing values instead of replacing with median
kqasba,hw1,t1_data_clean,col4,jk,1,"Only extracts first developer (drops all co-developers), no filtering/type-checking"
kqasba,hw1,t1_data_clean,col5,jk,1,"Only extracts first genre, no handling of malformed strings"
krastogi,hw1,t1_data_clean,col1,jk,3,
krastogi,hw1,t1_data_clean,col2,jk,3,
krastogi,hw1,t1_data_clean,col3,jk,3,
krastogi,hw1,t1_data_clean,col4,jk,3,
krastogi,hw1,t1_data_clean,col5,jk,3,
ssagar2,hw1,t1_data_clean,col1,jk,2,"Drop duplicates for name, drop missing values for released on"
ssagar2,hw1,t1_data_clean,col2,jk,3,
ssagar2,hw1,t1_data_clean,col3,jk,3,"For total players, need to account for European commas (2,3K) (removing all commas can affect decimal values (2,3K -> 23K -> 23000 instead of 2300))"
ssagar2,hw1,t1_data_clean,col4,jk,0,What cleaning was actually done here?
ssagar2,hw1,t1_data_clean,col5,jk,3,
vsakhark,hw1,t1_data_clean,col1,jk,3,
vsakhark,hw1,t1_data_clean,col2,jk,3,
vsakhark,hw1,t1_data_clean,col3,jk,3,
vsakhark,hw1,t1_data_clean,col4,jk,3,
vsakhark,hw1,t1_data_clean,col5,jk,3,
naxs,hw1,t1_data_clean,col1,jk,3,"Total players has both 'k' and 'K', also account for European comma (only in total players)"
naxs,hw1,t1_data_clean,col2,jk,3,
naxs,hw1,t1_data_clean,col3,jk,2,
naxs,hw1,t1_data_clean,col4,jk,2,"Drop missing values, convert stringified lists to actual lists"
naxs,hw1,t1_data_clean,col5,jk,2,Drop missing values
ankitshu,hw1,t1_data_clean,col1,jk,3,
ankitshu,hw1,t1_data_clean,col2,jk,3,
ankitshu,hw1,t1_data_clean,col3,jk,2,Drop missing values instead of replacing with mean
ankitshu,hw1,t1_data_clean,col4,jk,3,"For the total players column, you also need to account for European commas (2,3K) (removing all commas can affect decimal values (2,3K -> 23K -> 23000 instead of 2300))"
ankitshu,hw1,t1_data_clean,col5,jk,3,
tsohani,hw1,t1_data_clean,col1,jk,3,
tsohani,hw1,t1_data_clean,col2,jk,2,"Drop missing values instead of replacing, convert stringified lists to actual lists (it is easier to work with lists than strings!)"
tsohani,hw1,t1_data_clean,col3,jk,2,"Drop missing values instead of replacing, convert stringified lists to actual lists (it is easier to work with lists than strings!)"
tsohani,hw1,t1_data_clean,col4,jk,2,Drop missing values instead of replacing
tsohani,hw1,t1_data_clean,col5,jk,2,Drop missing values instead of replacing
csoska,hw1,t1_data_clean,col1,jk,2,Not removing 'tbd' leads to NaT values in released_on column
csoska,hw1,t1_data_clean,col2,jk,3,
csoska,hw1,t1_data_clean,col3,jk,3,
csoska,hw1,t1_data_clean,col4,jk,3,
csoska,hw1,t1_data_clean,col5,jk,3,
nivedity,hw1,t1_data_clean,col1,jk,0,wrong homework submitted
nivedity,hw1,t1_data_clean,col2,jk,0,
nivedity,hw1,t1_data_clean,col3,jk,0,
nivedity,hw1,t1_data_clean,col4,jk,0,
nivedity,hw1,t1_data_clean,col5,jk,0,
hengyuez,hw1,t1_data_clean,col1,jk,2,Not removing 'tbd' leads to NaT values in released_on column
hengyuez,hw1,t1_data_clean,col2,jk,2,"total_players also has European commas. Removing all commas can affect decimal values (2,3K -> 23K -> 23000 instead of 2300)"
hengyuez,hw1,t1_data_clean,col3,jk,3,
hengyuez,hw1,t1_data_clean,col4,jk,1,"Handle stringified lists, capitalization standardiziation, list type for all values"
hengyuez,hw1,t1_data_clean,col5,jk,2,Filter out missing values
barora,hw2,t2_eda,q1,jk,3,
barora,hw2,t2_eda,a1,jk,3,
barora,hw2,t2_eda,p1,jk,3,
barora,hw2,t2_eda,q2,jk,2,Intermediate question
barora,hw2,t2_eda,a2,jk,2,Provide speicifc statistics
barora,hw2,t2_eda,p2,jk,3,
barora,hw2,t2_eda,q3,jk,3,
barora,hw2,t2_eda,a3,jk,3,
barora,hw2,t2_eda,p3,jk,3,
barora,hw2,t2_eda,q4,jk,3,
barora,hw2,t2_eda,a4,jk,3,
barora,hw2,t2_eda,p4,jk,3,
barora,hw2,t2_eda,q5,jk,3,
barora,hw2,t2_eda,a5,jk,3,
barora,hw2,t2_eda,p5,jk,3,
achavess,hw2,t2_eda,q1,jk,1,Simple question
achavess,hw2,t2_eda,a1,jk,3,
achavess,hw2,t2_eda,p1,jk,3,
achavess,hw2,t2_eda,q2,jk,3,
achavess,hw2,t2_eda,a2,jk,3,
achavess,hw2,t2_eda,p2,jk,3,
achavess,hw2,t2_eda,q3,jk,3,
achavess,hw2,t2_eda,a3,jk,3,
achavess,hw2,t2_eda,p3,jk,3,
achavess,hw2,t2_eda,q4,jk,1,Simple question
achavess,hw2,t2_eda,a4,jk,3,
achavess,hw2,t2_eda,p4,jk,3,
achavess,hw2,t2_eda,q5,jk,1,Simple question
achavess,hw2,t2_eda,a5,jk,3,
achavess,hw2,t2_eda,p5,jk,3,
alexding,hw2,t2_eda,q1,jk,2,Intermediate question
alexding,hw2,t2_eda,a1,jk,3,
alexding,hw2,t2_eda,p1,jk,3,
alexding,hw2,t2_eda,q2,jk,2,Intermediate question
alexding,hw2,t2_eda,a2,jk,3,
alexding,hw2,t2_eda,p2,jk,3,
alexding,hw2,t2_eda,q3,jk,2,Intermediate question
alexding,hw2,t2_eda,a3,jk,3,
alexding,hw2,t2_eda,p3,jk,3,
alexding,hw2,t2_eda,q4,jk,3,
alexding,hw2,t2_eda,a4,jk,3,
alexding,hw2,t2_eda,p4,jk,3,
alexding,hw2,t2_eda,q5,jk,3,
alexding,hw2,t2_eda,a5,jk,3,
alexding,hw2,t2_eda,p5,jk,3,
sijiaf,hw2,t2_eda,q1,jk,3,
sijiaf,hw2,t2_eda,a1,jk,3,
sijiaf,hw2,t2_eda,p1,jk,2,Coefficient not graphed
sijiaf,hw2,t2_eda,q2,jk,3,
sijiaf,hw2,t2_eda,a2,jk,3,
sijiaf,hw2,t2_eda,p2,jk,2,Coefficient not graphed
sijiaf,hw2,t2_eda,q3,jk,3,
sijiaf,hw2,t2_eda,a3,jk,3,
sijiaf,hw2,t2_eda,p3,jk,3,
sijiaf,hw2,t2_eda,q4,jk,3,
sijiaf,hw2,t2_eda,a4,jk,3,
sijiaf,hw2,t2_eda,p4,jk,3,
sijiaf,hw2,t2_eda,q5,jk,3,
sijiaf,hw2,t2_eda,a5,jk,3,
sijiaf,hw2,t2_eda,p5,jk,3,
radhikaj,hw2,t2_eda,q1,jk,1,Simple question
radhikaj,hw2,t2_eda,a1,jk,0,No answer
radhikaj,hw2,t2_eda,p1,jk,1,Does not visualize question
radhikaj,hw2,t2_eda,q2,jk,2,Intermediate question
radhikaj,hw2,t2_eda,a2,jk,1,Vague answer
radhikaj,hw2,t2_eda,p2,jk,1,Visualization shows only current players and labels total players
radhikaj,hw2,t2_eda,q3,jk,2,
radhikaj,hw2,t2_eda,a3,jk,1,Vague answer
radhikaj,hw2,t2_eda,p3,jk,3,
radhikaj,hw2,t2_eda,q4,jk,1,Simple question
radhikaj,hw2,t2_eda,a4,jk,0,No answer
radhikaj,hw2,t2_eda,p4,jk,1,Incorrect visualization
radhikaj,hw2,t2_eda,q5,jk,3,
radhikaj,hw2,t2_eda,a5,jk,0,No answer
radhikaj,hw2,t2_eda,p5,jk,2,Coefficient not graphed
mlal,hw2,t2_eda,q1,jk,2,Intermediate question
mlal,hw2,t2_eda,a1,jk,3,
mlal,hw2,t2_eda,p1,jk,3,
mlal,hw2,t2_eda,q2,jk,3,
mlal,hw2,t2_eda,a2,jk,3,
mlal,hw2,t2_eda,p2,jk,3,
mlal,hw2,t2_eda,q3,jk,3,
mlal,hw2,t2_eda,a3,jk,3,
mlal,hw2,t2_eda,p3,jk,3,
mlal,hw2,t2_eda,q4,jk,3,
mlal,hw2,t2_eda,a4,jk,3,
mlal,hw2,t2_eda,p4,jk,3,
mlal,hw2,t2_eda,q5,jk,3,
mlal,hw2,t2_eda,a5,jk,3,
mlal,hw2,t2_eda,p5,jk,3,
somyameh,hw2,t2_eda,q1,jk,3,
somyameh,hw2,t2_eda,a1,jk,3,
somyameh,hw2,t2_eda,p1,jk,3,
somyameh,hw2,t2_eda,q2,jk,2,Question lacks clear actionability
somyameh,hw2,t2_eda,a2,jk,3,
somyameh,hw2,t2_eda,p2,jk,2,Coefficient not graphed
somyameh,hw2,t2_eda,q3,jk,3,
somyameh,hw2,t2_eda,a3,jk,2,Correlation coefficient not included in answer
somyameh,hw2,t2_eda,p3,jk,2,Coefficient not graphed
somyameh,hw2,t2_eda,q4,jk,3,
somyameh,hw2,t2_eda,a4,jk,2,Statistics not included
somyameh,hw2,t2_eda,p4,jk,3,
somyameh,hw2,t2_eda,q5,jk,3,
somyameh,hw2,t2_eda,a5,jk,2,Correlation coefficient not included in answer
somyameh,hw2,t2_eda,p5,jk,2,Coefficient not graphed
anshp,hw2,t2_eda,q1,jk,2,Intermediate question
anshp,hw2,t2_eda,a1,jk,1,"Answer says ""top 5 genres by average review score"" but the code calculates average number of reviews, not review score."
anshp,hw2,t2_eda,p1,jk,3,
anshp,hw2,t2_eda,q2,jk,2,
anshp,hw2,t2_eda,a2,jk,1,"Answer implies that these developers make ""highly reviewed"" games, but doesn’t clarify that it’s based on volume of reviews, not rating or review quality"
anshp,hw2,t2_eda,p2,jk,3,
anshp,hw2,t2_eda,q3,jk,1,How is this question valuable for a Steam PM?
anshp,hw2,t2_eda,a3,jk,2,Correlation coefficient not included in answer
anshp,hw2,t2_eda,p3,jk,2,Coefficient not graphed
anshp,hw2,t2_eda,q4,jk,2,Intermediate question
anshp,hw2,t2_eda,a4,jk,1,Vague answer
anshp,hw2,t2_eda,p4,jk,3,
anshp,hw2,t2_eda,q5,jk,1,Simple question
anshp,hw2,t2_eda,a5,jk,2,No specific statistics in answer
anshp,hw2,t2_eda,p5,jk,3,
hpasha,hw2,t2_eda,q1,jk,3,
hpasha,hw2,t2_eda,a1,jk,2,No specific statistics
hpasha,hw2,t2_eda,p1,jk,3,
hpasha,hw2,t2_eda,q2,jk,3,
hpasha,hw2,t2_eda,a2,jk,3,
hpasha,hw2,t2_eda,p2,jk,3,
hpasha,hw2,t2_eda,q3,jk,3,
hpasha,hw2,t2_eda,a3,jk,3,
hpasha,hw2,t2_eda,p3,jk,3,
hpasha,hw2,t2_eda,q4,jk,3,
hpasha,hw2,t2_eda,a4,jk,3,
hpasha,hw2,t2_eda,p4,jk,3,
hpasha,hw2,t2_eda,q5,jk,3,
hpasha,hw2,t2_eda,a5,jk,3,
hpasha,hw2,t2_eda,p5,jk,3,
kqasba,hw2,t2_eda,q1,jk,2,
kqasba,hw2,t2_eda,a1,jk,3,
kqasba,hw2,t2_eda,p1,jk,3,
kqasba,hw2,t2_eda,q2,jk,3,
kqasba,hw2,t2_eda,a2,jk,3,
kqasba,hw2,t2_eda,p2,jk,3,
kqasba,hw2,t2_eda,q3,jk,3,
kqasba,hw2,t2_eda,a3,jk,3,
kqasba,hw2,t2_eda,p3,jk,3,
kqasba,hw2,t2_eda,q4,jk,3,
kqasba,hw2,t2_eda,a4,jk,3,
kqasba,hw2,t2_eda,p4,jk,3,
kqasba,hw2,t2_eda,q5,jk,3,
kqasba,hw2,t2_eda,a5,jk,3,
kqasba,hw2,t2_eda,p5,jk,3,
krastogi,hw2,t2_eda,q1,jk,1,Simple question
krastogi,hw2,t2_eda,a1,jk,2,Correlation coefficient not included in answer
krastogi,hw2,t2_eda,p1,jk,2,Coefficient not graphed
krastogi,hw2,t2_eda,q2,jk,2,Intermediate question
krastogi,hw2,t2_eda,a2,jk,2,No specific statistics in answer
krastogi,hw2,t2_eda,p2,jk,3,
krastogi,hw2,t2_eda,q3,jk,2,Intermediate question
krastogi,hw2,t2_eda,a3,jk,2,No specific statistics in answer
krastogi,hw2,t2_eda,p3,jk,3,
krastogi,hw2,t2_eda,q4,jk,1,Simple question
krastogi,hw2,t2_eda,a4,jk,2,Correlation coefficient not included in answer
krastogi,hw2,t2_eda,p4,jk,3,Coefficient not graphed
krastogi,hw2,t2_eda,q5,jk,3,
krastogi,hw2,t2_eda,a5,jk,2,No specific statistics
krastogi,hw2,t2_eda,p5,jk,3,
ssagar2,hw2,t2_eda,q1,jk,2,
ssagar2,hw2,t2_eda,a1,jk,3,
ssagar2,hw2,t2_eda,p1,jk,3,
ssagar2,hw2,t2_eda,q2,jk,2,
ssagar2,hw2,t2_eda,a2,jk,3,
ssagar2,hw2,t2_eda,p2,jk,3,
ssagar2,hw2,t2_eda,q3,jk,2,
ssagar2,hw2,t2_eda,a3,jk,3,
ssagar2,hw2,t2_eda,p3,jk,3,
ssagar2,hw2,t2_eda,q4,jk,2,
ssagar2,hw2,t2_eda,a4,jk,3,
ssagar2,hw2,t2_eda,p4,jk,3,
ssagar2,hw2,t2_eda,q5,jk,3,
ssagar2,hw2,t2_eda,a5,jk,2,Question talks about high ratings but answer talks about # of games released
ssagar2,hw2,t2_eda,p5,jk,2,Same comment as above forvisualization
vsakhark,hw2,t2_eda,q1,jk,2,
vsakhark,hw2,t2_eda,a1,jk,3,
vsakhark,hw2,t2_eda,p1,jk,3,
vsakhark,hw2,t2_eda,q2,jk,1,Simple question
vsakhark,hw2,t2_eda,a2,jk,2,No specific statistics in answer
vsakhark,hw2,t2_eda,p2,jk,3,
vsakhark,hw2,t2_eda,q3,jk,2,Intermediate question
vsakhark,hw2,t2_eda,a3,jk,2,No specific statistics in answer
vsakhark,hw2,t2_eda,p3,jk,3,
vsakhark,hw2,t2_eda,q4,jk,2,Intermediate question
vsakhark,hw2,t2_eda,a4,jk,2,No specific statistics in answer
vsakhark,hw2,t2_eda,p4,jk,3,
vsakhark,hw2,t2_eda,q5,jk,3,
vsakhark,hw2,t2_eda,a5,jk,2,
vsakhark,hw2,t2_eda,p5,jk,3,
naxs,hw2,t2_eda,q1,jk,2,
naxs,hw2,t2_eda,a1,jk,0,No answer
naxs,hw2,t2_eda,p1,jk,3,
naxs,hw2,t2_eda,q2,jk,2,
naxs,hw2,t2_eda,a2,jk,0,No answer
naxs,hw2,t2_eda,p2,jk,3,
naxs,hw2,t2_eda,q3,jk,2,
naxs,hw2,t2_eda,a3,jk,0,No answer
naxs,hw2,t2_eda,p3,jk,3,
naxs,hw2,t2_eda,q4,jk,2,
naxs,hw2,t2_eda,a4,jk,0,No answer
naxs,hw2,t2_eda,p4,jk,3,
naxs,hw2,t2_eda,q5,jk,3,
naxs,hw2,t2_eda,a5,jk,0,No answer
naxs,hw2,t2_eda,p5,jk,3,
ankitshu,hw2,t2_eda,q1,jk,2,Intermediate question
ankitshu,hw2,t2_eda,a1,jk,3,
ankitshu,hw2,t2_eda,p1,jk,3,
ankitshu,hw2,t2_eda,q2,jk,2,Intermediate question
ankitshu,hw2,t2_eda,a2,jk,2,No specific statistics in answer
ankitshu,hw2,t2_eda,p2,jk,2,Correlation coefficient not graphed
ankitshu,hw2,t2_eda,q3,jk,2,
ankitshu,hw2,t2_eda,a3,jk,2,"Answer could benefit from more statistics, discuss relative frequency more explicitly"
ankitshu,hw2,t2_eda,p3,jk,3,
ankitshu,hw2,t2_eda,q4,jk,2,Intermediate question
ankitshu,hw2,t2_eda,a4,jk,2,No specific statistics in answer
ankitshu,hw2,t2_eda,p4,jk,3,
ankitshu,hw2,t2_eda,q5,jk,2,Intermediate question
ankitshu,hw2,t2_eda,a5,jk,2,No specific statistics
ankitshu,hw2,t2_eda,p5,jk,1,Visualizes average rating rather than ratings over 4.5
tsohani,hw2,t2_eda,q1,jk,3,
tsohani,hw2,t2_eda,a1,jk,1,No specific statistics in answer
tsohani,hw2,t2_eda,p1,jk,3,
tsohani,hw2,t2_eda,q2,jk,1,Simple question
tsohani,hw2,t2_eda,a2,jk,1,Vague answer
tsohani,hw2,t2_eda,p2,jk,3,
tsohani,hw2,t2_eda,q3,jk,2,
tsohani,hw2,t2_eda,a3,jk,1,Vague answer
tsohani,hw2,t2_eda,p3,jk,3,
tsohani,hw2,t2_eda,q4,jk,2,
tsohani,hw2,t2_eda,a4,jk,1,Vague answer
tsohani,hw2,t2_eda,p4,jk,3,
tsohani,hw2,t2_eda,q5,jk,3,
tsohani,hw2,t2_eda,a5,jk,0,No answer
tsohani,hw2,t2_eda,p5,jk,3,
csoka,hw2,t2_eda,q1,jk,2,Intermediate question
csoka,hw2,t2_eda,a1,jk,1,No explicit insight or interpretation
csoka,hw2,t2_eda,p1,jk,3,
csoka,hw2,t2_eda,q2,jk,2,
csoka,hw2,t2_eda,a2,jk,0,No answer
csoka,hw2,t2_eda,p2,jk,3,
csoka,hw2,t2_eda,q3,jk,3,
csoka,hw2,t2_eda,a3,jk,0,No answer
csoka,hw2,t2_eda,p3,jk,1,"You attempted boxplots to show distribution but applied them to aggregated data — resulting in points, not boxes."
csoka,hw2,t2_eda,q4,jk,3,
csoka,hw2,t2_eda,a4,jk,1,Vague answer
csoka,hw2,t2_eda,p4,jk,2,
csoka,hw2,t2_eda,q5,jk,3,
csoka,hw2,t2_eda,a5,jk,0,No answer
csoka,hw2,t2_eda,p5,jk,3,
nivedity,hw2,t2_eda,q1,jk,1,Simple question
nivedity,hw2,t2_eda,a1,jk,3,
nivedity,hw2,t2_eda,p1,jk,3,
nivedity,hw2,t2_eda,q2,jk,1,Simple question
nivedity,hw2,t2_eda,a2,jk,3,
nivedity,hw2,t2_eda,p2,jk,3,
nivedity,hw2,t2_eda,q3,jk,1,Simple question
nivedity,hw2,t2_eda,a3,jk,3,
nivedity,hw2,t2_eda,p3,jk,3,
nivedity,hw2,t2_eda,q4,jk,1,Simple question
nivedity,hw2,t2_eda,a4,jk,1,"Question asks for ratings above 4.5, but answer and visualization is about ratings above 4.0"
nivedity,hw2,t2_eda,p4,jk,1,
nivedity,hw2,t2_eda,q5,jk,1,Simple question
nivedity,hw2,t2_eda,a5,jk,3,
nivedity,hw2,t2_eda,p5,jk,3,
barora,hw3,t3_ml,s1_train_test,jk,3,
barora,hw3,t3_ml,s2_build_model,jk,3,
barora,hw3,t3_ml,s3_calc_performance,jk,3,
barora,hw3,t3_ml,s4_explain_feat_model,jk,3,
barora,hw3,t3_ml,s5_best_model,jk,3,
achavess,hw3,t3_ml,s1_train_test,jk,3,
achavess,hw3,t3_ml,s2_build_model,jk,3,
achavess,hw3,t3_ml,s3_calc_performance,jk,3,
achavess,hw3,t3_ml,s4_explain_feat_model,jk,3,
achavess,hw3,t3_ml,s5_best_model,jk,3,
alexding,hw3,t3_ml,s1_train_test,jk,3,
alexding,hw3,t3_ml,s2_build_model,jk,2,Unclear what specific question is being asked
alexding,hw3,t3_ml,s3_calc_performance,jk,2,No interpretation or written comparison of metrics across models
alexding,hw3,t3_ml,s4_explain_feat_model,jk,1,No explanation of how features relate or discussion of their effects
alexding,hw3,t3_ml,s5_best_model,jk,1,No model comparison or justification of why it’s “best.”
sijiaf,hw3,t3_ml,s1_train_test,jk,3,
sijiaf,hw3,t3_ml,s2_build_model,jk,3,
sijiaf,hw3,t3_ml,s3_calc_performance,jk,3,
sijiaf,hw3,t3_ml,s4_explain_feat_model,jk,3,
sijiaf,hw3,t3_ml,s5_best_model,jk,3,
radhikaj,hw3,t3_ml,s1_train_test,jk,3,
radhikaj,hw3,t3_ml,s2_build_model,jk,1,Unclear what specific question is being asked
radhikaj,hw3,t3_ml,s3_calc_performance,jk,2,No interpretation or written comparison of metrics across models
radhikaj,hw3,t3_ml,s4_explain_feat_model,jk,1,No explanation of how features relate or discussion of their effects
radhikaj,hw3,t3_ml,s5_best_model,jk,0,No model comparison or justification of why it’s “best.”
mlal,hw3,t3_ml,s1_train_test,jk,3,
mlal,hw3,t3_ml,s2_build_model,jk,2,Unclear what specific question is being asked
mlal,hw3,t3_ml,s3_calc_performance,jk,3,
mlal,hw3,t3_ml,s4_explain_feat_model,jk,3,
mlal,hw3,t3_ml,s5_best_model,jk,3,
somyameh,hw3,t3_ml,s1_train_test,jk,3,
somyameh,hw3,t3_ml,s2_build_model,jk,3,
somyameh,hw3,t3_ml,s3_calc_performance,jk,2,Vague interpretation or written comparison of metrics across models
somyameh,hw3,t3_ml,s4_explain_feat_model,jk,1,No explanation of how features relate or discussion of their effects
somyameh,hw3,t3_ml,s5_best_model,jk,2,Vague justification of why it’s “best.”
anshp,hw3,t3_ml,s1_train_test,jk,3,
anshp,hw3,t3_ml,s2_build_model,jk,3,
anshp,hw3,t3_ml,s3_calc_performance,jk,2,Vague interpretation or written comparison of metrics across models
anshp,hw3,t3_ml,s4_explain_feat_model,jk,3,
anshp,hw3,t3_ml,s5_best_model,jk,3,
hpasha,hw3,t3_ml,s1_train_test,jk,3,
hpasha,hw3,t3_ml,s2_build_model,jk,1,Unclear what specific question is being asked
hpasha,hw3,t3_ml,s3_calc_performance,jk,2,No interpretation or written comparison of metrics across models
hpasha,hw3,t3_ml,s4_explain_feat_model,jk,1,No explanation of how features relate or discussion of their effects
hpasha,hw3,t3_ml,s5_best_model,jk,1,Vague justification of why it’s “best.”
kqasba,hw3,t3_ml,s1_train_test,jk,3,
kqasba,hw3,t3_ml,s2_build_model,jk,3,
kqasba,hw3,t3_ml,s3_calc_performance,jk,2,No interpretation or written comparison of metrics across models
kqasba,hw3,t3_ml,s4_explain_feat_model,jk,2,Vague explanation of how features relate or discussion of their effects
kqasba,hw3,t3_ml,s5_best_model,jk,3,
krastogi,hw3,t3_ml,s1_train_test,jk,3,
krastogi,hw3,t3_ml,s2_build_model,jk,3,
krastogi,hw3,t3_ml,s3_calc_performance,jk,2,No interpretation or written comparison of metrics across models
krastogi,hw3,t3_ml,s4_explain_feat_model,jk,3,
krastogi,hw3,t3_ml,s5_best_model,jk,3,
ssagar2,hw3,t3_ml,s1_train_test,jk,3,
ssagar2,hw3,t3_ml,s2_build_model,jk,3,
ssagar2,hw3,t3_ml,s3_calc_performance,jk,2,No interpretation or written comparison of metrics across models
ssagar2,hw3,t3_ml,s4_explain_feat_model,jk,3,
ssagar2,hw3,t3_ml,s5_best_model,jk,3,
vsakhark,hw3,t3_ml,s1_train_test,jk,3,
vsakhark,hw3,t3_ml,s2_build_model,jk,3,
vsakhark,hw3,t3_ml,s3_calc_performance,jk,3,
vsakhark,hw3,t3_ml,s4_explain_feat_model,jk,3,
vsakhark,hw3,t3_ml,s5_best_model,jk,3,
naxs,hw3,t3_ml,s1_train_test,jk,3,
naxs,hw3,t3_ml,s2_build_model,jk,2,"The task says ""train models to predict #saves"", but no problem framing or context is given (e.g., why we're predicting saves, or how it could be useful for developers or business goals)."
naxs,hw3,t3_ml,s3_calc_performance,jk,2,Metrics are mostly printed without clear interpretation or comparison.
naxs,hw3,t3_ml,s4_explain_feat_model,jk,0,"No analysis of which features were most important, no interpretation of why any model did better or worse"
naxs,hw3,t3_ml,s5_best_model,jk,3,
ankitshu,hw3,t3_ml,s1_train_test,jk,3,
ankitshu,hw3,t3_ml,s2_build_model,jk,3,
ankitshu,hw3,t3_ml,s3_calc_performance,jk,3,
ankitshu,hw3,t3_ml,s4_explain_feat_model,jk,3,
ankitshu,hw3,t3_ml,s5_best_model,jk,3,
tsohani,hw3,t3_ml,s1_train_test,jk,3,
tsohani,hw3,t3_ml,s2_build_model,jk,3,
tsohani,hw3,t3_ml,s3_calc_performance,jk,2,No interpretation or written comparison of metrics across models
tsohani,hw3,t3_ml,s4_explain_feat_model,jk,2,"Feature importance was included, but more interpretation of why certain features matter was needed"
tsohani,hw3,t3_ml,s5_best_model,jk,3,
csoska,hw3,t3_ml,s1_train_test,jk,3,
csoska,hw3,t3_ml,s2_build_model,jk,2,Unclear what specific question is being asked
csoska,hw3,t3_ml,s3_calc_performance,jk,2,Metrics are mostly printed without clear interpretation or comparison.
csoska,hw3,t3_ml,s4_explain_feat_model,jk,0,"No analysis of which features were most important, no interpretation of why any model did better or worse"
csoska,hw3,t3_ml,s5_best_model,jk,3,
nivedity,hw3,t3_ml,s1_train_test,jk,3,
nivedity,hw3,t3_ml,s2_build_model,jk,3,
nivedity,hw3,t3_ml,s3_calc_performance,jk,3,
nivedity,hw3,t3_ml,s4_explain_feat_model,jk,3,
nivedity,hw3,t3_ml,s5_best_model,jk,3,
barora,hw4,t4_data_story,c1_goal,jk,3,
barora,hw4,t4_data_story,c2_stats_ml,jk,3,
barora,hw4,t4_data_story,c3_viz,jk,3,
barora,hw4,t4_data_story,c4_comm,jk,3,
barora,hw4,t4_data_story,c5_data_limit,jk,3,
achavess,hw4,t4_data_story,c1_goal,jk,3,
achavess,hw4,t4_data_story,c2_stats_ml,jk,3,
achavess,hw4,t4_data_story,c3_viz,jk,3,
achavess,hw4,t4_data_story,c4_comm,jk,3,
achavess,hw4,t4_data_story,c5_data_limit,jk,3,
alexding,hw4,t4_data_story,c1_goal,jk,3,
alexding,hw4,t4_data_story,c2_stats_ml,jk,3,
alexding,hw4,t4_data_story,c3_viz,jk,3,
alexding,hw4,t4_data_story,c4_comm,jk,3,
alexding,hw4,t4_data_story,c5_data_limit,jk,3,
sijiaf,hw4,t4_data_story,c1_goal,jk,3,
sijiaf,hw4,t4_data_story,c2_stats_ml,jk,3,
sijiaf,hw4,t4_data_story,c3_viz,jk,3,
sijiaf,hw4,t4_data_story,c4_comm,jk,3,
sijiaf,hw4,t4_data_story,c5_data_limit,jk,3,
radhikaj,hw4,t4_data_story,c1_goal,jk,3,
radhikaj,hw4,t4_data_story,c2_stats_ml,jk,2,"Does not present any formal modeling (e.g., regression, ML)"
radhikaj,hw4,t4_data_story,c3_viz,jk,3,
radhikaj,hw4,t4_data_story,c4_comm,jk,3,
radhikaj,hw4,t4_data_story,c5_data_limit,jk,3,
mlal,hw4,t4_data_story,c1_goal,jk,3,
mlal,hw4,t4_data_story,c2_stats_ml,jk,3,
mlal,hw4,t4_data_story,c3_viz,jk,3,
mlal,hw4,t4_data_story,c4_comm,jk,3,
mlal,hw4,t4_data_story,c5_data_limit,jk,3,
somyameh,hw4,t4_data_story,c1_goal,jk,3,
somyameh,hw4,t4_data_story,c2_stats_ml,jk,3,
somyameh,hw4,t4_data_story,c3_viz,jk,3,
somyameh,hw4,t4_data_story,c4_comm,jk,3,
somyameh,hw4,t4_data_story,c5_data_limit,jk,3,
anshp,hw4,t4_data_story,c1_goal,jk,3,
anshp,hw4,t4_data_story,c2_stats_ml,jk,3,
anshp,hw4,t4_data_story,c3_viz,jk,3,
anshp,hw4,t4_data_story,c4_comm,jk,3,
anshp,hw4,t4_data_story,c5_data_limit,jk,3,
hpasha,hw4,t4_data_story,c1_goal,jk,3,
hpasha,hw4,t4_data_story,c2_stats_ml,jk,3,
hpasha,hw4,t4_data_story,c3_viz,jk,3,
hpasha,hw4,t4_data_story,c4_comm,jk,3,
hpasha,hw4,t4_data_story,c5_data_limit,jk,3,
kqasba,hw4,t4_data_story,c1_goal,jk,3,
kqasba,hw4,t4_data_story,c2_stats_ml,jk,2,
kqasba,hw4,t4_data_story,c3_viz,jk,3,
kqasba,hw4,t4_data_story,c4_comm,jk,3,
kqasba,hw4,t4_data_story,c5_data_limit,jk,3,
krastogi,hw4,t4_data_story,c1_goal,jk,3,
krastogi,hw4,t4_data_story,c2_stats_ml,jk,3,
krastogi,hw4,t4_data_story,c3_viz,jk,3,
krastogi,hw4,t4_data_story,c4_comm,jk,3,
krastogi,hw4,t4_data_story,c5_data_limit,jk,3,
ssagar2,hw4,t4_data_story,c1_goal,jk,3,
ssagar2,hw4,t4_data_story,c2_stats_ml,jk,2,Does not use modeling to answer a clear analytical question or uncover insight—only reports metrics without interpreting their significance or purpose.
ssagar2,hw4,t4_data_story,c3_viz,jk,3,
ssagar2,hw4,t4_data_story,c4_comm,jk,3,
ssagar2,hw4,t4_data_story,c5_data_limit,jk,3,
vsakhark,hw4,t4_data_story,c1_goal,jk,3,
vsakhark,hw4,t4_data_story,c2_stats_ml,jk,2,No data-driven insights from modeling
vsakhark,hw4,t4_data_story,c3_viz,jk,3,
vsakhark,hw4,t4_data_story,c4_comm,jk,3,
vsakhark,hw4,t4_data_story,c5_data_limit,jk,2,Does not provide thoughtful or specific discussion of data limitations
naxs,hw4,t4_data_story,c1_goal,jk,3,
naxs,hw4,t4_data_story,c2_stats_ml,jk,3,
naxs,hw4,t4_data_story,c3_viz,jk,3,
naxs,hw4,t4_data_story,c4_comm,jk,3,
naxs,hw4,t4_data_story,c5_data_limit,jk,3,
ankitshu,hw4,t4_data_story,c1_goal,jk,3,
ankitshu,hw4,t4_data_story,c2_stats_ml,jk,3,
ankitshu,hw4,t4_data_story,c3_viz,jk,3,
ankitshu,hw4,t4_data_story,c4_comm,jk,3,
ankitshu,hw4,t4_data_story,c5_data_limit,jk,3,
tsohani,hw4,t4_data_story,c1_goal,jk,3,
tsohani,hw4,t4_data_story,c2_stats_ml,jk,2,No data-driven insights from modeling
tsohani,hw4,t4_data_story,c3_viz,jk,3,
tsohani,hw4,t4_data_story,c4_comm,jk,3,
tsohani,hw4,t4_data_story,c5_data_limit,jk,3,
csoska,hw4,t4_data_story,c1_goal,jk,1,Never clearly states the problem or formal objectives
csoska,hw4,t4_data_story,c2_stats_ml,jk,1,Little reflection on what the results mean
csoska,hw4,t4_data_story,c3_viz,jk,2,Explanations are scattered and at times speculative
csoska,hw4,t4_data_story,c4_comm,jk,1,"Difficult to follow, missing explanations"
csoska,hw4,t4_data_story,c5_data_limit,jk,0,No discussion of limitations or suggestions
nivedity,hw4,t4_data_story,c1_goal,jk,3,
nivedity,hw4,t4_data_story,c2_stats_ml,jk,2,"Minimal detail about model goals, feature selection, or interpretation beyond surface-level performance metrics"
nivedity,hw4,t4_data_story,c3_viz,jk,3,
nivedity,hw4,t4_data_story,c4_comm,jk,3,
nivedity,hw4,t4_data_story,c5_data_limit,jk,3,
usa,hw1,t1_data_clean,col1,yb,1,"The code did not remove any duplicates, and it only identified them."
usa,hw1,t1_data_clean,col2,yb,2,Did not drop missing values.
usa,hw1,t1_data_clean,col3,yb,2,Did not drop missing values.
usa,hw1,t1_data_clean,col4,yb,1,The code turns the developer column into a string and ONLY keeps the first developer (removing the other ones in the list)
usa,hw1,t1_data_clean,col5,yb,1,Same thing for genre column. Only keeps the first genre.
abhimava,hw1,t1_data_clean,col1,yb,2,"Did not drop the invalid date values. ""errors='coerce'"" are replaced with NaT"
abhimava,hw1,t1_data_clean,col2,yb,3,
abhimava,hw1,t1_data_clean,col3,yb,1,"Code puts genre in nested list. Converts the strings to lists, but does not transform elements into a list of capitalized string ([""adventure""] instead of [""Adventure""])"
abhimava,hw1,t1_data_clean,col4,yb,2,Does not transform elements into a list of capitalized string
abhimava,hw1,t1_data_clean,col5,yb,3,
rupalc,hw1,t1_data_clean,col1,yb,2,Does not drop duplicates
rupalc,hw1,t1_data_clean,col2,yb,1,"Is there a reason why you are removing specific phrases related to DLC, menus, and technical details?"
rupalc,hw1,t1_data_clean,col3,yb,1,"Turns the entire list into string (needs to be list of strings), and no dropping NA."
rupalc,hw1,t1_data_clean,col4,yb,2,Does not drop NA and replaces
rupalc,hw1,t1_data_clean,col5,yb,1,"Turns the entire list into string (needs to be list of strings), and no dropping NA."
gsumukh,hw1,t1_data_clean,col1,yb,3,"[Regraded] Did not drop the invalid date values. ""errors='coerce'"" are replaced with NaT"
gsumukh,hw1,t1_data_clean,col2,yb,2,"Make sure to keep date type as datetime type! Does not account for regular string values without ""K"" so numbers like ""393"" are still strings"
gsumukh,hw1,t1_data_clean,col3,yb,3,This does not clean the column at all. It just modifies the column completely differently ('679' -> 679000)
gsumukh,hw1,t1_data_clean,col4,yb,3,Same reason as Cleaning column 3
gsumukh,hw1,t1_data_clean,col5,yb,2,No formatting for capitalization. Same reason as Cleaning column 3
sjashnan,hw1,t1_data_clean,col1,yb,3,
sjashnan,hw1,t1_data_clean,col2,yb,3,
sjashnan,hw1,t1_data_clean,col3,yb,3,
sjashnan,hw1,t1_data_clean,col4,yb,2,"Did not drop the invalid date values, replaced with """""
sjashnan,hw1,t1_data_clean,col5,yb,3,
suhailk,hw1,t1_data_clean,col1,yb,2,"Did not drop the invalid date values. ""errors='coerce'"" are replaced with NaT"
suhailk,hw1,t1_data_clean,col2,yb,3,
suhailk,hw1,t1_data_clean,col3,yb,2,"When returning original value (if they don't have ""k""), they are not changed into a float/int"
suhailk,hw1,t1_data_clean,col4,yb,3,
suhailk,hw1,t1_data_clean,col5,yb,3,
rkkim,hw1,t1_data_clean,col1,yb,2,Did not drop NaT
rkkim,hw1,t1_data_clean,col2,yb,3,
rkkim,hw1,t1_data_clean,col3,yb,3,
rkkim,hw1,t1_data_clean,col4,yb,3,
rkkim,hw1,t1_data_clean,col5,yb,3,
elakra,hw1,t1_data_clean,col1,yb,3,[Regraded] wrong homework submitted
elakra,hw1,t1_data_clean,col2,yb,1,can you explain why the you would have to truncate descriptions and append '...' after 15 characters?
elakra,hw1,t1_data_clean,col3,yb,1,"Turns the entire list into string (needs to be list of strings), and no formatting for capitalization"
elakra,hw1,t1_data_clean,col4,yb,2,Did not drop the invalid date values.
elakra,hw1,t1_data_clean,col5,yb,2,Does not account for ‘k’ instead of ‘K’ and does not drop NA values
gracelia,hw1,t1_data_clean,col1,yb,1,"Turns the entire list into string (needs to be list of strings), and no formatting for capitalization."
gracelia,hw1,t1_data_clean,col2,yb,1,"Turns the entire list into string (needs to be list of strings), and no formatting for capitalization."
gracelia,hw1,t1_data_clean,col3,yb,2,Does not account for ‘k’ instead of ‘K’ and does not drop NA values
gracelia,hw1,t1_data_clean,col4,yb,2,Does not drop NA values
gracelia,hw1,t1_data_clean,col5,yb,2,Does not drop NA values
charlenl,hw1,t1_data_clean,col1,yb,2,Does not account for ‘k’ instead of ‘K’ and does not drop NA values
charlenl,hw1,t1_data_clean,col2,yb,2,"Did not drop the invalid date values. ""errors='coerce'"" are replaced with NaT"
charlenl,hw1,t1_data_clean,col3,yb,2,Does not drop NA values
charlenl,hw1,t1_data_clean,col4,yb,2,
charlenl,hw1,t1_data_clean,col5,yb,2,
nmedaram,hw1,t1_data_clean,col1,yb,1,"Turns the entire list into string (needs to be list of strings), and no formatting for capitalization."
nmedaram,hw1,t1_data_clean,col2,yb,1,"Turns the entire list into string (needs to be list of strings), and no formatting for capitalization."
nmedaram,hw1,t1_data_clean,col3,yb,2,"Did not drop the invalid date values. ""errors='coerce'"" are replaced with NaT"
nmedaram,hw1,t1_data_clean,col4,yb,2,Does not drop NA values
nmedaram,hw1,t1_data_clean,col5,yb,3,
sohammon,hw1,t1_data_clean,col1,yb,1,"Turns the entire list into string (needs to be list of strings), and no dropping NA."
sohammon,hw1,t1_data_clean,col2,yb,2,Does not drop NA values
sohammon,hw1,t1_data_clean,col3,yb,2,Drop NA values and not replace
sohammon,hw1,t1_data_clean,col4,yb,3,
sohammon,hw1,t1_data_clean,col5,yb,3,[Regraded] Does not run. Please make sure you follow the template provided.
ramkausr,hw1,t1_data_clean,col1,yb,3,"[Regraded] Does not drop NA values, maybe better if you modified the original column?"
ramkausr,hw1,t1_data_clean,col2,yb,3,
ramkausr,hw1,t1_data_clean,col3,yb,3,
ramkausr,hw1,t1_data_clean,col4,yb,3,
ramkausr,hw1,t1_data_clean,col5,yb,3,
agshaik,hw1,t1_data_clean,col1,yb,2,[Regraded] Does not remove duplicates
agshaik,hw1,t1_data_clean,col2,yb,3,
agshaik,hw1,t1_data_clean,col3,yb,1,"Turns the entire list into string (needs to be list of strings), and no dropping NA."
agshaik,hw1,t1_data_clean,col4,yb,3,"Graded the column 7-11 instead of Column 4 Turns the entire list into string (needs to be list of strings), and no dropping NA."
agshaik,hw1,t1_data_clean,col5,yb,2,Does not remove NA
ashwin2,hw1,t1_data_clean,col1,yb,3,"[Regraded] Does not drop NA values, maybe better if you modified the original column?"
ashwin2,hw1,t1_data_clean,col2,yb,3,
ashwin2,hw1,t1_data_clean,col3,yb,3,
ashwin2,hw1,t1_data_clean,col4,yb,3,
ashwin2,hw1,t1_data_clean,col5,yb,3,
yiwentan,hw1,t1_data_clean,col1,yb,3,
yiwentan,hw1,t1_data_clean,col2,yb,3,
yiwentan,hw1,t1_data_clean,col3,yb,1,"Turns the entire list into string (needs to be list of strings), and no dropping NA."
yiwentan,hw1,t1_data_clean,col4,yb,2,"Did not drop the invalid date values. ""errors='coerce'"" are replaced with NaT"
yiwentan,hw1,t1_data_clean,col5,yb,1,"Turns the entire list into string (needs to be list of strings), and no dropping NA."
dtayal,hw1,t1_data_clean,col1,yb,1,"Does not remove duplicates, does not drop NA"
dtayal,hw1,t1_data_clean,col2,yb,1,Does not drop NA and fills the NA with '1900-01-01'
dtayal,hw1,t1_data_clean,col3,yb,3,
dtayal,hw1,t1_data_clean,col4,yb,2,Does not account for ‘k’ instead of ‘K’ and does not drop NA values
dtayal,hw1,t1_data_clean,col5,yb,2,Does not drop NA
hyanaman,hw1,t1_data_clean,col1,yb,1,Does not drop duplicates
hyanaman,hw1,t1_data_clean,col2,yb,3,
hyanaman,hw1,t1_data_clean,col3,yb,2,Drop missing values instead of replacing
hyanaman,hw1,t1_data_clean,col4,yb,2,Drop missing values instead of replacing
hyanaman,hw1,t1_data_clean,col5,yb,1,Does not drop missing values
aafonja,hw2,t2_eda,q1,yb,2,intermediate question
aafonja,hw2,t2_eda,a1,yb,2,
aafonja,hw2,t2_eda,p1,yb,1,the code/visualization technically does not answer your question since it is check genre with the highest number of game count rather than the players.
aafonja,hw2,t2_eda,q2,yb,2,
aafonja,hw2,t2_eda,a2,yb,1,"might be good to clarify the insight about how there is no ""drastic decline in 2022"" but after. the insight could be more quantitative by specifying the approx counts + also could mention the overall upward trend over the years."
aafonja,hw2,t2_eda,p2,yb,2,
aafonja,hw2,t2_eda,q3,yb,2,
aafonja,hw2,t2_eda,a3,yb,1,"maybe noting that most genres have fairly similar average ratings, with only a few genres (Quiz/Trivia and especially MOBA) falling significantly below the rest + maybe briefly hypothesizing why Visual Novel might be rated higher compared to MOBA?"
aafonja,hw2,t2_eda,p3,yb,2,
aafonja,hw2,t2_eda,q4,yb,3,
aafonja,hw2,t2_eda,a4,yb,1,insight could clarify that the chart only includes developers with more than four highly rated games + discussing what this dominance suggests would add analytical depth
aafonja,hw2,t2_eda,p4,yb,3,
aafonja,hw2,t2_eda,q5,yb,2,
aafonja,hw2,t2_eda,a5,yb,1,"insight is incorrect and incomplete: states that highly rated games were last released in 2019 and 2020, but the data and the plot show several highly rated games released in other years"
aafonja,hw2,t2_eda,p5,yb,1,x-axis label is not fully clear.
usa,hw2,t2_eda,q1,yb,3,
usa,hw2,t2_eda,a1,yb,2,"while reviews influenced the clustering, it's not directly visible in the 2D plot, which could lead to misinterpretation of some data points. also, consider including info about which specific games are representative examples of each cluster"
usa,hw2,t2_eda,p1,yb,2,"the legend could be enhanced with the descriptive cluster names rather than just 0,1,2,3."
usa,hw2,t2_eda,q2,yb,3,
usa,hw2,t2_eda,a2,yb,1,"does not address the sharp decline in both average number of review + total player, could have had quantitative support"
usa,hw2,t2_eda,p2,yb,2,the large difference in scale between two metrics can make it hard to interpret the blue line's trend
usa,hw2,t2_eda,q3,yb,3,
usa,hw2,t2_eda,a3,yb,1,"it would be helpful to annotate or highlight specific outliers—games that are very popular but have low ratings, or games with few reviews but very high ratings. could clarify how the color gradient (representing total players) is distributed across the plot?"
usa,hw2,t2_eda,p3,yb,2,
usa,hw2,t2_eda,q4,yb,3,
usa,hw2,t2_eda,a4,yb,2,
usa,hw2,t2_eda,p4,yb,3,
usa,hw2,t2_eda,q5,yb,3,
usa,hw2,t2_eda,a5,yb,2,
usa,hw2,t2_eda,p5,yb,2,Would have been nice to see the second graph slightly zoomed up for better analysis. Also would have been nice if there were other visualization to back up the reasonings for certain insights such as average number of players.
abhimava,hw2,t2_eda,q1,yb,2,
abhimava,hw2,t2_eda,a1,yb,2,potentially deeper insight
abhimava,hw2,t2_eda,p1,yb,3,
abhimava,hw2,t2_eda,q2,yb,3,
abhimava,hw2,t2_eda,a2,yb,2,some quantitative evidence in analysis might be good 
abhimava,hw2,t2_eda,p2,yb,3,
abhimava,hw2,t2_eda,q3,yb,3,
abhimava,hw2,t2_eda,a3,yb,3,
abhimava,hw2,t2_eda,p3,yb,3,
abhimava,hw2,t2_eda,q4,yb,3,
abhimava,hw2,t2_eda,a4,yb,3,
abhimava,hw2,t2_eda,p4,yb,3,
abhimava,hw2,t2_eda,q5,yb,3,
abhimava,hw2,t2_eda,a5,yb,2,potentially deeper insight
abhimava,hw2,t2_eda,p5,yb,3,
rupalc,hw2,t2_eda,q1,yb,3,
rupalc,hw2,t2_eda,a1,yb,2,
rupalc,hw2,t2_eda,p1,yb,3,
rupalc,hw2,t2_eda,q2,yb,3,
rupalc,hw2,t2_eda,a2,yb,1,can you give more analysis/quantitative evidence in your analysis?
rupalc,hw2,t2_eda,p2,yb,3,
rupalc,hw2,t2_eda,q3,yb,3,
rupalc,hw2,t2_eda,a3,yb,1,"""Brawler"" is at the top with the highest average saves but was not mentioned + insight lacks quantitative details"
rupalc,hw2,t2_eda,p3,yb,1,"address the ""combinations of genres"" aspect of the question"
rupalc,hw2,t2_eda,q4,yb,2,
rupalc,hw2,t2_eda,a4,yb,1,"insight doesn't directly interpret what the scatter plot actually shows, lacks specific examples from the data"
rupalc,hw2,t2_eda,p4,yb,1,"genre labels are missing from the data points, making it difficult to identify which specific genres correspond to which points"
rupalc,hw2,t2_eda,q5,yb,2,
rupalc,hw2,t2_eda,a5,yb,1,insight doesn't directly connect to what the visualization actually shows
rupalc,hw2,t2_eda,p5,yb,1,"isn't just the same graph as Q2? also the code doesn't actually filter the saves data to only include those from the first month after release (i dont think you can do this with this dataset?). It appears to be using total lifetime saves instead + no analysis showing whether games with high early save counts maintain success over time, which is the core of the original question"
gsumukh,hw2,t2_eda,q1,yb,2,
gsumukh,hw2,t2_eda,a1,yb,1,very vague. can you provide more insight? is point-and-click the ultimately higest rating out of all ratings? what about the number of ratings that each genre has?
gsumukh,hw2,t2_eda,p1,yb,1,
gsumukh,hw2,t2_eda,q2,yb,2,
gsumukh,hw2,t2_eda,a2,yb,1,vague insight.
gsumukh,hw2,t2_eda,p2,yb,2,Would have been nice to see the second graph slightly zoomed up for better analysis. Also would have been nice if there were other visualization to back up the reasonings for more insights/details.
gsumukh,hw2,t2_eda,q3,yb,1,not a question
gsumukh,hw2,t2_eda,a3,yb,1,"insight could mention the dramatic increase over time, not just the current peak, would be helpful to provide context for this increase."
gsumukh,hw2,t2_eda,p3,yb,2,
gsumukh,hw2,t2_eda,q4,yb,1,
gsumukh,hw2,t2_eda,a4,yb,0,no answer
gsumukh,hw2,t2_eda,p4,yb,1,
gsumukh,hw2,t2_eda,q5,yb,2,
gsumukh,hw2,t2_eda,a5,yb,0,no answer
gsumukh,hw2,t2_eda,p5,yb,2,
jgu2,hw2,t2_eda,q1,yb,2,
jgu2,hw2,t2_eda,a1,yb,3,
jgu2,hw2,t2_eda,p1,yb,3,
jgu2,hw2,t2_eda,q2,yb,3,
jgu2,hw2,t2_eda,a2,yb,0,no answer
jgu2,hw2,t2_eda,p2,yb,3,
jgu2,hw2,t2_eda,q3,yb,2,
jgu2,hw2,t2_eda,a3,yb,1,can you provide reason why that might be the case for your insight?
jgu2,hw2,t2_eda,p3,yb,3,
jgu2,hw2,t2_eda,q4,yb,3,
jgu2,hw2,t2_eda,a4,yb,2,
jgu2,hw2,t2_eda,p4,yb,2,
jgu2,hw2,t2_eda,q5,yb,3,
jgu2,hw2,t2_eda,a5,yb,2,"if you could have the quantitative analysis in your insight, that would be nice!"
jgu2,hw2,t2_eda,p5,yb,3,
sjashnan,hw2,t2_eda,q1,yb,3,
sjashnan,hw2,t2_eda,a1,yb,3,
sjashnan,hw2,t2_eda,p1,yb,3,
sjashnan,hw2,t2_eda,q2,yb,3,
sjashnan,hw2,t2_eda,a2,yb,3,
sjashnan,hw2,t2_eda,p2,yb,3,
sjashnan,hw2,t2_eda,q3,yb,3,
sjashnan,hw2,t2_eda,a3,yb,3,
sjashnan,hw2,t2_eda,p3,yb,3,
sjashnan,hw2,t2_eda,q4,yb,3,
sjashnan,hw2,t2_eda,a4,yb,3,
sjashnan,hw2,t2_eda,p4,yb,3,
sjashnan,hw2,t2_eda,q5,yb,3,
sjashnan,hw2,t2_eda,a5,yb,3,
sjashnan,hw2,t2_eda,p5,yb,3,
suhailk,hw2,t2_eda,q1,yb,3,
suhailk,hw2,t2_eda,a1,yb,3,
suhailk,hw2,t2_eda,p1,yb,3,
suhailk,hw2,t2_eda,q2,yb,3,
suhailk,hw2,t2_eda,a2,yb,3,
suhailk,hw2,t2_eda,p2,yb,3,
suhailk,hw2,t2_eda,q3,yb,3,
suhailk,hw2,t2_eda,a3,yb,3,
suhailk,hw2,t2_eda,p3,yb,3,
suhailk,hw2,t2_eda,q4,yb,3,
suhailk,hw2,t2_eda,a4,yb,3,
suhailk,hw2,t2_eda,p4,yb,3,
suhailk,hw2,t2_eda,q5,yb,3,
suhailk,hw2,t2_eda,a5,yb,3,
suhailk,hw2,t2_eda,p5,yb,3,
rkkim,hw2,t2_eda,q1,yb,2,
rkkim,hw2,t2_eda,a1,yb,1,vague insight
rkkim,hw2,t2_eda,p1,yb,2,
rkkim,hw2,t2_eda,q2,yb,3,
rkkim,hw2,t2_eda,a2,yb,1,vague insight
rkkim,hw2,t2_eda,p2,yb,3,
rkkim,hw2,t2_eda,q3,yb,2,
rkkim,hw2,t2_eda,a3,yb,1,vague insight
rkkim,hw2,t2_eda,p3,yb,3,
rkkim,hw2,t2_eda,q4,yb,2,
rkkim,hw2,t2_eda,a4,yb,1,can you explain why?
rkkim,hw2,t2_eda,p4,yb,1,the difference between the two lines make it hard to interpret current players
rkkim,hw2,t2_eda,q5,yb,3,
rkkim,hw2,t2_eda,a5,yb,1,vague insight
rkkim,hw2,t2_eda,p5,yb,3,
elakra,hw2,t2_eda,q1,yb,1,simple question
elakra,hw2,t2_eda,a1,yb,1,could provide more analysis
elakra,hw2,t2_eda,p1,yb,2,
elakra,hw2,t2_eda,q2,yb,2,
elakra,hw2,t2_eda,a2,yb,1,"could provide the actual correlation coefficient, can you give insights on the graphs you created?"
elakra,hw2,t2_eda,p2,yb,2,
elakra,hw2,t2_eda,q3,yb,3,
elakra,hw2,t2_eda,a3,yb,1,"could clarify that the ranking is based on game count, not necessarily on rating or popularity by player numbers"
elakra,hw2,t2_eda,p3,yb,1,"If the goal is to find the ""most popular"" by rating, not just by quantity, an additional step to consider developer average ratings within each genre would be needed + does not show how close the second or third most ""popular"" developers are in each genre, which could be useful for context?"
elakra,hw2,t2_eda,q4,yb,2,
elakra,hw2,t2_eda,a4,yb,1,"insight could note that a ratio above 1 means the current player count exceeds the total player count recorded, which might indicate data anomalies, recent surges, or differences in how ""total players"" is measured. + strengthened by quantifying Tokyo Necro's ratio (1.57) and comparing it to the next highest"
elakra,hw2,t2_eda,p4,yb,2,
elakra,hw2,t2_eda,q5,yb,2,
elakra,hw2,t2_eda,a5,yb,2,"term ""weak positive correlation"" might slightly understate the relationship since a correlation of 0.67 is typically considered moderate to moderately strong!"
elakra,hw2,t2_eda,p5,yb,3,
gracelia,hw2,t2_eda,q1,yb,2,intermediate question
gracelia,hw2,t2_eda,a1,yb,2,"analysis could briefly mention genres with notably low player ratios to provide a fuller picture of the distribution + ""more players in general"" in the hypothesis is slightly misleading—the metric is about the proportion of current to total players, not the absolute number of players"
gracelia,hw2,t2_eda,p1,yb,2,would look nice if it was sorted by ascending/descending! easier to see the visualization all at once
gracelia,hw2,t2_eda,q2,yb,2,
gracelia,hw2,t2_eda,a2,yb,2,insight could be strengthened by quantifying how many developers have a mean rating above a certain threshold + address the overall distribution (but the graph is a bit hard for that i guess)
gracelia,hw2,t2_eda,p2,yb,1,consider displaying the number of games each developer has released + a high average rating for a developer with only one game may not be as meaningful as for one with a larger portfolio.
gracelia,hw2,t2_eda,q3,yb,2,
gracelia,hw2,t2_eda,a3,yb,2,"the analysis only addresses 2013-18, but could have been more detailed with the sharp decline after 2018"
gracelia,hw2,t2_eda,p3,yb,2,does this normalize the number of games released per year? if more games were released in peak years that could explain the higher total player count
gracelia,hw2,t2_eda,q4,yb,3,
gracelia,hw2,t2_eda,a4,yb,1,"correlation measures linear association, and a value of 0.70 suggests a strong positive linear relationship + could be helpful to disucss the possible reasons for the outliers"
gracelia,hw2,t2_eda,p4,yb,3,
gracelia,hw2,t2_eda,q5,yb,3,
gracelia,hw2,t2_eda,a5,yb,1,"highlighting other genres with significant changes (like MOBA's rise and fall, or the stability of Platform and Indie) would provide a more comprehensive view! also providing specific quantitative detail might strengthen the insight"
gracelia,hw2,t2_eda,p5,yb,3,
charlenl,hw2,t2_eda,q1,yb,1,simple question
charlenl,hw2,t2_eda,a1,yb,3,
charlenl,hw2,t2_eda,p1,yb,3,
charlenl,hw2,t2_eda,q2,yb,2,the second part of the question is a bit difficult to answer because...
charlenl,hw2,t2_eda,a2,yb,1,the analysis does not account for the number of reviews behind each rating. high ratings with few reviews can be more volatile or subject to selection bias!
charlenl,hw2,t2_eda,p2,yb,2,"to better understand what makes a ""great"" game, consider both the rating and the number of reviews/players potentially"
charlenl,hw2,t2_eda,q3,yb,3,
charlenl,hw2,t2_eda,a3,yb,2,
charlenl,hw2,t2_eda,p3,yb,2,brief analysis on ths shape of the graph might further tie the visual to the analysis
charlenl,hw2,t2_eda,q4,yb,3,
charlenl,hw2,t2_eda,a4,yb,1,"the answer could be more explicit about which genres are currently declining/fading or have peaked in the past, and which are growing or maintaining activity. + also would be nice to have quantitative details in analysis"
charlenl,hw2,t2_eda,p4,yb,2,"while MOBA and Quiz/Trivia are newer, they have very low total counts and no recent releases, so calling them ""emerging"" may be misleading"
charlenl,hw2,t2_eda,q5,yb,3,
charlenl,hw2,t2_eda,a5,yb,3,
charlenl,hw2,t2_eda,p5,yb,3,
nmedaram,hw2,t2_eda,q1,yb,3,
nmedaram,hw2,t2_eda,a1,yb,2,
nmedaram,hw2,t2_eda,p1,yb,2,Would have been nice to see the second graph slightly zoomed up for better analysis. Also would have been nice if there were other visualization to back up the reasonings for certain insights such as average number of players.
nmedaram,hw2,t2_eda,q2,yb,3,
nmedaram,hw2,t2_eda,a2,yb,2,
nmedaram,hw2,t2_eda,p2,yb,3,
nmedaram,hw2,t2_eda,q3,yb,3,
nmedaram,hw2,t2_eda,a3,yb,1,"it would be helpful to annotate or highlight specific outliers—games that are very popular but have low ratings, or games with few reviews but very high ratings. could clarify how the color gradient (representing total players) is distributed across the plot?"
nmedaram,hw2,t2_eda,p3,yb,2,
nmedaram,hw2,t2_eda,q4,yb,3,
nmedaram,hw2,t2_eda,a4,yb,1,"does not address the sharp decline in both average number of review + total player, could have had quantitative support"
nmedaram,hw2,t2_eda,p4,yb,2,the large difference in scale between two metrics can make it hard to interpret the blue line's trend
nmedaram,hw2,t2_eda,q5,yb,3,
nmedaram,hw2,t2_eda,a5,yb,2,"while reviews influenced the clustering, it's not directly visible in the 2D plot, which could lead to misinterpretation of some data points. also, consider including info about which specific games are representative examples of each cluster"
nmedaram,hw2,t2_eda,p5,yb,2,"the legend could be enhanced with the descriptive cluster names (mainstream hits, Hhidden gems, etc.) rather than just 0,1,2,3."
sohammon,hw2,t2_eda,q1,yb,3,
sohammon,hw2,t2_eda,a1,yb,2,how about april and may?
sohammon,hw2,t2_eda,p1,yb,3,
sohammon,hw2,t2_eda,q2,yb,2,
sohammon,hw2,t2_eda,a2,yb,3,
sohammon,hw2,t2_eda,p2,yb,3,
sohammon,hw2,t2_eda,q3,yb,3,
sohammon,hw2,t2_eda,a3,yb,3,
sohammon,hw2,t2_eda,p3,yb,3,
sohammon,hw2,t2_eda,q4,yb,3,
sohammon,hw2,t2_eda,a4,yb,3,
sohammon,hw2,t2_eda,p4,yb,3,
sohammon,hw2,t2_eda,q5,yb,3,
sohammon,hw2,t2_eda,a5,yb,2,
sohammon,hw2,t2_eda,p5,yb,3,
mnandiga,hw2,t2_eda,q1,yb,3,advanced
mnandiga,hw2,t2_eda,a1,yb,1,"vague answer, could explain a little more on the highs/lows on the graph, comparison between games, etc"
mnandiga,hw2,t2_eda,p1,yb,3,
mnandiga,hw2,t2_eda,q2,yb,1,simple question
mnandiga,hw2,t2_eda,a2,yb,2,
mnandiga,hw2,t2_eda,p2,yb,3,
mnandiga,hw2,t2_eda,q3,yb,2,intermediate question
mnandiga,hw2,t2_eda,a3,yb,1,is there corelation coefficient to back up as evidence?
mnandiga,hw2,t2_eda,p3,yb,2,visualization did not really help clarify as much
mnandiga,hw2,t2_eda,q4,yb,3,
mnandiga,hw2,t2_eda,a4,yb,2,"could explain more by either including the specific rates, quantify the difference, group the top genres, etc"
mnandiga,hw2,t2_eda,p4,yb,3,
mnandiga,hw2,t2_eda,q5,yb,2,
mnandiga,hw2,t2_eda,a5,yb,2,did you choose ace attorney because it had the lowest number of total players?
mnandiga,hw2,t2_eda,p5,yb,2,"potentially provided a chart of player counts, the absence of player counts in the visualization makes it difficult the understand the analysis"
ramkausr,hw2,t2_eda,q1,yb,2,intermediate
ramkausr,hw2,t2_eda,a1,yb,1,vague answer
ramkausr,hw2,t2_eda,p1,yb,3,
ramkausr,hw2,t2_eda,q2,yb,2,intermediate question
ramkausr,hw2,t2_eda,a2,yb,1,does not include any specific numbers/data. does not explain about the scatter plot
ramkausr,hw2,t2_eda,p2,yb,2,why did you include the scatterplot if you are not using it in analysis?
ramkausr,hw2,t2_eda,q3,yb,2,intermediate question
ramkausr,hw2,t2_eda,a3,yb,1,very vague answer
ramkausr,hw2,t2_eda,p3,yb,3,
ramkausr,hw2,t2_eda,q4,yb,3,
ramkausr,hw2,t2_eda,a4,yb,1,vague answer
ramkausr,hw2,t2_eda,p4,yb,1,would like to see more depth for the visualization/analysis. do they actually have higher rating or do they just have low number of reviews? depths/details like that to support your analysis would have been nice.
ramkausr,hw2,t2_eda,q5,yb,2,intermediate question
ramkausr,hw2,t2_eda,a5,yb,1,vague and specific statistic in answer
ramkausr,hw2,t2_eda,p5,yb,3,
agshaik,hw2,t2_eda,q1,yb,3,advanced question
agshaik,hw2,t2_eda,a1,yb,3,
agshaik,hw2,t2_eda,p1,yb,2,Would have been nice to see the second graph slightly zoomed up for better analysis. Also would have been nice if there were other visualization to back up the reasonings for certain insights such as average number of players.
agshaik,hw2,t2_eda,q2,yb,3,advanced question
agshaik,hw2,t2_eda,a2,yb,2,Lacks specific data/numbers in the insight
agshaik,hw2,t2_eda,p2,yb,3,
agshaik,hw2,t2_eda,q3,yb,3,advanced question
agshaik,hw2,t2_eda,a3,yb,3,
agshaik,hw2,t2_eda,p3,yb,3,
agshaik,hw2,t2_eda,q4,yb,3,advanced question
agshaik,hw2,t2_eda,a4,yb,2,
agshaik,hw2,t2_eda,p4,yb,2,
agshaik,hw2,t2_eda,q5,yb,3,advanced question
agshaik,hw2,t2_eda,a5,yb,3,
agshaik,hw2,t2_eda,p5,yb,3,
ashwin2,hw2,t2_eda,q1,yb,3,
ashwin2,hw2,t2_eda,a1,yb,3,
ashwin2,hw2,t2_eda,p1,yb,2,also potentially looking into rating for popularity aspect
ashwin2,hw2,t2_eda,q2,yb,3,
ashwin2,hw2,t2_eda,a2,yb,3,
ashwin2,hw2,t2_eda,p2,yb,2,do you count for the number of ratings?
ashwin2,hw2,t2_eda,q3,yb,3,
ashwin2,hw2,t2_eda,a3,yb,3,
ashwin2,hw2,t2_eda,p3,yb,3,
ashwin2,hw2,t2_eda,q4,yb,3,
ashwin2,hw2,t2_eda,a4,yb,3,
ashwin2,hw2,t2_eda,p4,yb,3,
ashwin2,hw2,t2_eda,q5,yb,3,
ashwin2,hw2,t2_eda,a5,yb,3,
ashwin2,hw2,t2_eda,p5,yb,3,
yiwentan,hw2,t2_eda,q1,yb,2,
yiwentan,hw2,t2_eda,a1,yb,2,can you provide some hypothesis why that might be the case for the insight?
yiwentan,hw2,t2_eda,p1,yb,3,
yiwentan,hw2,t2_eda,q2,yb,3,
yiwentan,hw2,t2_eda,a2,yb,2,
yiwentan,hw2,t2_eda,p2,yb,2,what does the scatterplot exactly showing? what type of information is it providing for the analysis?
yiwentan,hw2,t2_eda,q3,yb,3,
yiwentan,hw2,t2_eda,a3,yb,1,more quantitative reasoning in analysis might be better for the insight. 
yiwentan,hw2,t2_eda,p3,yb,3,
yiwentan,hw2,t2_eda,q4,yb,2,
yiwentan,hw2,t2_eda,a4,yb,1,
yiwentan,hw2,t2_eda,p4,yb,1,not sure what this graph is showing
yiwentan,hw2,t2_eda,q5,yb,3,
yiwentan,hw2,t2_eda,a5,yb,2,
yiwentan,hw2,t2_eda,p5,yb,1,
dtayal,hw2,t2_eda,q1,yb,3,
dtayal,hw2,t2_eda,a1,yb,3,
dtayal,hw2,t2_eda,p1,yb,3,
dtayal,hw2,t2_eda,q2,yb,2,
dtayal,hw2,t2_eda,a2,yb,3,
dtayal,hw2,t2_eda,p2,yb,3,
dtayal,hw2,t2_eda,q3,yb,3,
dtayal,hw2,t2_eda,a3,yb,3,
dtayal,hw2,t2_eda,p3,yb,3,
dtayal,hw2,t2_eda,q4,yb,3,
dtayal,hw2,t2_eda,a4,yb,3,
dtayal,hw2,t2_eda,p4,yb,3,
dtayal,hw2,t2_eda,q5,yb,3,
dtayal,hw2,t2_eda,a5,yb,3,
dtayal,hw2,t2_eda,p5,yb,3,
hyanaman,hw2,t2_eda,q1,yb,3,
hyanaman,hw2,t2_eda,a1,yb,2,more quantitative reasoning could strengthen the insight
hyanaman,hw2,t2_eda,p1,yb,3,
hyanaman,hw2,t2_eda,q2,yb,3,
hyanaman,hw2,t2_eda,a2,yb,2,more quantitative reasoning could strengthen the insight
hyanaman,hw2,t2_eda,p2,yb,2,
hyanaman,hw2,t2_eda,q3,yb,3,
hyanaman,hw2,t2_eda,a3,yb,2,
hyanaman,hw2,t2_eda,p3,yb,2,
hyanaman,hw2,t2_eda,q4,yb,3,
hyanaman,hw2,t2_eda,a4,yb,2,
hyanaman,hw2,t2_eda,p4,yb,2,
hyanaman,hw2,t2_eda,q5,yb,3,
hyanaman,hw2,t2_eda,a5,yb,3,
hyanaman,hw2,t2_eda,p5,yb,3,
aafonja,hw3,t3_ml,s1_train_test,yb,3,
aafonja,hw3,t3_ml,s2_build_model,yb,2,Unclear what specific question is being asked
aafonja,hw3,t3_ml,s3_calc_performance,yb,3,
aafonja,hw3,t3_ml,s4_explain_feat_model,yb,1,No explanation of how features relate or discussion of their effects
aafonja,hw3,t3_ml,s5_best_model,yb,3,
usa,hw3,t3_ml,s1_train_test,yb,3,
usa,hw3,t3_ml,s2_build_model,yb,2,Unclear what specific question is being asked
usa,hw3,t3_ml,s3_calc_performance,yb,2,No interpretation of metrics across models
usa,hw3,t3_ml,s4_explain_feat_model,yb,1,No explanation of how features relate or discussion of their effects
usa,hw3,t3_ml,s5_best_model,yb,1,Very vague justification of why it’s “best.”
abhimava,hw3,t3_ml,s1_train_test,yb,3,
abhimava,hw3,t3_ml,s2_build_model,yb,3,
abhimava,hw3,t3_ml,s3_calc_performance,yb,3,
abhimava,hw3,t3_ml,s4_explain_feat_model,yb,3,
abhimava,hw3,t3_ml,s5_best_model,yb,3,
rupalc,hw3,t3_ml,s1_train_test,yb,3,
rupalc,hw3,t3_ml,s2_build_model,yb,3,
rupalc,hw3,t3_ml,s3_calc_performance,yb,1,No interpretation of metrics across models
rupalc,hw3,t3_ml,s4_explain_feat_model,yb,3,
rupalc,hw3,t3_ml,s5_best_model,yb,1,
gsumukh,hw3,t3_ml,s1_train_test,yb,3,
gsumukh,hw3,t3_ml,s2_build_model,yb,2,Unclear what specific question is being asked
gsumukh,hw3,t3_ml,s3_calc_performance,yb,2,No interpretation of metrics across models
gsumukh,hw3,t3_ml,s4_explain_feat_model,yb,3,
gsumukh,hw3,t3_ml,s5_best_model,yb,1,Very vague justification of why it’s “best.”
jgu2,hw3,t3_ml,s1_train_test,yb,3,
jgu2,hw3,t3_ml,s2_build_model,yb,2,Unclear what specific question is being asked
jgu2,hw3,t3_ml,s3_calc_performance,yb,3,
jgu2,hw3,t3_ml,s4_explain_feat_model,yb,3,
jgu2,hw3,t3_ml,s5_best_model,yb,3,
sjashnan,hw3,t3_ml,s1_train_test,yb,3,
sjashnan,hw3,t3_ml,s2_build_model,yb,3,
sjashnan,hw3,t3_ml,s3_calc_performance,yb,3,
sjashnan,hw3,t3_ml,s4_explain_feat_model,yb,3,
sjashnan,hw3,t3_ml,s5_best_model,yb,3,
suhailk,hw3,t3_ml,s1_train_test,yb,3,
suhailk,hw3,t3_ml,s2_build_model,yb,3,
suhailk,hw3,t3_ml,s3_calc_performance,yb,3,
suhailk,hw3,t3_ml,s4_explain_feat_model,yb,3,
suhailk,hw3,t3_ml,s5_best_model,yb,3,
rkkim,hw3,t3_ml,s1_train_test,yb,3,
rkkim,hw3,t3_ml,s2_build_model,yb,2,Unclear what specific question is being asked
rkkim,hw3,t3_ml,s3_calc_performance,yb,2,No interpretation of metrics across models
rkkim,hw3,t3_ml,s4_explain_feat_model,yb,1,No explanation of how features relate or discussion of their effects
rkkim,hw3,t3_ml,s5_best_model,yb,0,No model comparison or justification of why it’s “best.”
elakra,hw3,t3_ml,s1_train_test,yb,3,
elakra,hw3,t3_ml,s2_build_model,yb,3,
elakra,hw3,t3_ml,s3_calc_performance,yb,3,
elakra,hw3,t3_ml,s4_explain_feat_model,yb,1,No explanation of how features relate or discussion of their effects
elakra,hw3,t3_ml,s5_best_model,yb,1,Very vague justification of why it’s “best.”
gracelia,hw3,t3_ml,s1_train_test,yb,3,
gracelia,hw3,t3_ml,s2_build_model,yb,3,
gracelia,hw3,t3_ml,s3_calc_performance,yb,3,
gracelia,hw3,t3_ml,s4_explain_feat_model,yb,3,
gracelia,hw3,t3_ml,s5_best_model,yb,3,
charlenl,hw3,t3_ml,s1_train_test,yb,3,
charlenl,hw3,t3_ml,s2_build_model,yb,3,
charlenl,hw3,t3_ml,s3_calc_performance,yb,3,
charlenl,hw3,t3_ml,s4_explain_feat_model,yb,3,
charlenl,hw3,t3_ml,s5_best_model,yb,3,
nmedaram,hw3,t3_ml,s1_train_test,yb,3,
nmedaram,hw3,t3_ml,s2_build_model,yb,3,
nmedaram,hw3,t3_ml,s3_calc_performance,yb,3,
nmedaram,hw3,t3_ml,s4_explain_feat_model,yb,3,
nmedaram,hw3,t3_ml,s5_best_model,yb,3,
sohammon,hw3,t3_ml,s1_train_test,yb,3,
sohammon,hw3,t3_ml,s2_build_model,yb,3,
sohammon,hw3,t3_ml,s3_calc_performance,yb,3,
sohammon,hw3,t3_ml,s4_explain_feat_model,yb,3,
sohammon,hw3,t3_ml,s5_best_model,yb,3,
mnandiga,hw3,t3_ml,s1_train_test,yb,3,
mnandiga,hw3,t3_ml,s2_build_model,yb,3,
mnandiga,hw3,t3_ml,s3_calc_performance,yb,3,
mnandiga,hw3,t3_ml,s4_explain_feat_model,yb,3,
mnandiga,hw3,t3_ml,s5_best_model,yb,3,
ramkausr,hw3,t3_ml,s1_train_test,yb,3,
ramkausr,hw3,t3_ml,s2_build_model,yb,3,
ramkausr,hw3,t3_ml,s3_calc_performance,yb,3,
ramkausr,hw3,t3_ml,s4_explain_feat_model,yb,1,No explanation of how features relate or discussion of their effects
ramkausr,hw3,t3_ml,s5_best_model,yb,3,
agshaik,hw3,t3_ml,s1_train_test,yb,3,
agshaik,hw3,t3_ml,s2_build_model,yb,3,
agshaik,hw3,t3_ml,s3_calc_performance,yb,3,
agshaik,hw3,t3_ml,s4_explain_feat_model,yb,3,
agshaik,hw3,t3_ml,s5_best_model,yb,3,
ashwin2,hw3,t3_ml,s1_train_test,yb,3,
ashwin2,hw3,t3_ml,s2_build_model,yb,3,
ashwin2,hw3,t3_ml,s3_calc_performance,yb,3,
ashwin2,hw3,t3_ml,s4_explain_feat_model,yb,3,
ashwin2,hw3,t3_ml,s5_best_model,yb,3,
yiwentan,hw3,t3_ml,s1_train_test,yb,3,
yiwentan,hw3,t3_ml,s2_build_model,yb,3,
yiwentan,hw3,t3_ml,s3_calc_performance,yb,3,
yiwentan,hw3,t3_ml,s4_explain_feat_model,yb,3,
yiwentan,hw3,t3_ml,s5_best_model,yb,3,
dtayal,hw3,t3_ml,s1_train_test,yb,3,
dtayal,hw3,t3_ml,s2_build_model,yb,3,
dtayal,hw3,t3_ml,s3_calc_performance,yb,3,
dtayal,hw3,t3_ml,s4_explain_feat_model,yb,3,
dtayal,hw3,t3_ml,s5_best_model,yb,3,
hyanaman,hw3,t3_ml,s1_train_test,yb,3,
hyanaman,hw3,t3_ml,s2_build_model,yb,3,
hyanaman,hw3,t3_ml,s3_calc_performance,yb,3,
hyanaman,hw3,t3_ml,s4_explain_feat_model,yb,3,
hyanaman,hw3,t3_ml,s5_best_model,yb,3,
aafonja,hw4,t4_data_story,c1_goal,yb,1,Problem is unclear or objectives are weakly defined / Does not define the problem gap to showcase why the it matters
aafonja,hw4,t4_data_story,c2_stats_ml,yb,1,The insights are weak for EDA portion
aafonja,hw4,t4_data_story,c3_viz,yb,3,
aafonja,hw4,t4_data_story,c4_comm,yb,3,
aafonja,hw4,t4_data_story,c5_data_limit,yb,2,Lacks strong improvement suggestions.
usa,hw4,t4_data_story,c1_goal,yb,3,
usa,hw4,t4_data_story,c2_stats_ml,yb,3,
usa,hw4,t4_data_story,c3_viz,yb,2,Relevant viz but unclear or lack explanation.
usa,hw4,t4_data_story,c4_comm,yb,2,Missing explanation between the ML and EDA insights and how it relates/is relevant to the problem
usa,hw4,t4_data_story,c5_data_limit,yb,3,
abhimava,hw4,t4_data_story,c1_goal,yb,3,
abhimava,hw4,t4_data_story,c2_stats_ml,yb,3,
abhimava,hw4,t4_data_story,c3_viz,yb,3,
abhimava,hw4,t4_data_story,c4_comm,yb,3,
abhimava,hw4,t4_data_story,c5_data_limit,yb,3,
rupalc,hw4,t4_data_story,c1_goal,yb,3,
rupalc,hw4,t4_data_story,c2_stats_ml,yb,3,
rupalc,hw4,t4_data_story,c3_viz,yb,3,
rupalc,hw4,t4_data_story,c4_comm,yb,3,
rupalc,hw4,t4_data_story,c5_data_limit,yb,3,
gsumukh,hw4,t4_data_story,c1_goal,yb,1,Problem is unclear / Does not define the problem gap to showcase why the it matters
gsumukh,hw4,t4_data_story,c2_stats_ml,yb,2,Lack depth or clarity.
gsumukh,hw4,t4_data_story,c3_viz,yb,1,Would have been nice to show some EDA visualizations to present as evidence for some findings/features
gsumukh,hw4,t4_data_story,c4_comm,yb,2,Lack some more detailed explanations
gsumukh,hw4,t4_data_story,c5_data_limit,yb,3,
jgu2,hw4,t4_data_story,c1_goal,yb,1,Problem is unclear or objectives are weakly defined / Does not define the problem gap to showcase why the it matters
jgu2,hw4,t4_data_story,c2_stats_ml,yb,2,Lack depth or clarity.
jgu2,hw4,t4_data_story,c3_viz,yb,3,
jgu2,hw4,t4_data_story,c4_comm,yb,3,
jgu2,hw4,t4_data_story,c5_data_limit,yb,3,
sjashnan,hw4,t4_data_story,c1_goal,yb,3,
sjashnan,hw4,t4_data_story,c2_stats_ml,yb,3,
sjashnan,hw4,t4_data_story,c3_viz,yb,3,
sjashnan,hw4,t4_data_story,c4_comm,yb,3,
sjashnan,hw4,t4_data_story,c5_data_limit,yb,3,
suhailk,hw4,t4_data_story,c1_goal,yb,3,
suhailk,hw4,t4_data_story,c2_stats_ml,yb,3,
suhailk,hw4,t4_data_story,c3_viz,yb,3,
suhailk,hw4,t4_data_story,c4_comm,yb,3,
suhailk,hw4,t4_data_story,c5_data_limit,yb,3,
rkkim,hw4,t4_data_story,c1_goal,yb,1,Problem is unclear or objectives are weakly defined / Does not define the problem gap to showcase why the it matters
rkkim,hw4,t4_data_story,c2_stats_ml,yb,2,Lack depth or clarity.
rkkim,hw4,t4_data_story,c3_viz,yb,2,Relevant viz but unclear or lack explanation.
rkkim,hw4,t4_data_story,c4_comm,yb,2,
rkkim,hw4,t4_data_story,c5_data_limit,yb,3,
elakra,hw4,t4_data_story,c1_goal,yb,3,
elakra,hw4,t4_data_story,c2_stats_ml,yb,3,
elakra,hw4,t4_data_story,c3_viz,yb,3,
elakra,hw4,t4_data_story,c4_comm,yb,3,
elakra,hw4,t4_data_story,c5_data_limit,yb,3,
gracelia,hw4,t4_data_story,c1_goal,yb,3,
gracelia,hw4,t4_data_story,c2_stats_ml,yb,3,
gracelia,hw4,t4_data_story,c3_viz,yb,3,
gracelia,hw4,t4_data_story,c4_comm,yb,3,
gracelia,hw4,t4_data_story,c5_data_limit,yb,3,
charlenl,hw4,t4_data_story,c1_goal,yb,3,
charlenl,hw4,t4_data_story,c2_stats_ml,yb,3,
charlenl,hw4,t4_data_story,c3_viz,yb,3,
charlenl,hw4,t4_data_story,c4_comm,yb,3,
charlenl,hw4,t4_data_story,c5_data_limit,yb,3,
nmedaram,hw4,t4_data_story,c1_goal,yb,3,
nmedaram,hw4,t4_data_story,c2_stats_ml,yb,3,
nmedaram,hw4,t4_data_story,c3_viz,yb,3,
nmedaram,hw4,t4_data_story,c4_comm,yb,3,
nmedaram,hw4,t4_data_story,c5_data_limit,yb,3,
sohammon,hw4,t4_data_story,c1_goal,yb,3,
sohammon,hw4,t4_data_story,c2_stats_ml,yb,3,
sohammon,hw4,t4_data_story,c3_viz,yb,3,
sohammon,hw4,t4_data_story,c4_comm,yb,3,
sohammon,hw4,t4_data_story,c5_data_limit,yb,3,
mnandiga,hw4,t4_data_story,c1_goal,yb,3,
mnandiga,hw4,t4_data_story,c2_stats_ml,yb,3,
mnandiga,hw4,t4_data_story,c3_viz,yb,2,would have liked to see the EDA visualizations
mnandiga,hw4,t4_data_story,c4_comm,yb,3,
mnandiga,hw4,t4_data_story,c5_data_limit,yb,3,
ramkausr,hw4,t4_data_story,c1_goal,yb,1,Problem is unclear or objectives are weakly defined / Does not define the problem gap to showcase why the it matters
ramkausr,hw4,t4_data_story,c2_stats_ml,yb,3,
ramkausr,hw4,t4_data_story,c3_viz,yb,3,
ramkausr,hw4,t4_data_story,c4_comm,yb,3,
ramkausr,hw4,t4_data_story,c5_data_limit,yb,3,
agshaik,hw4,t4_data_story,c1_goal,yb,3,
agshaik,hw4,t4_data_story,c2_stats_ml,yb,3,
agshaik,hw4,t4_data_story,c3_viz,yb,3,
agshaik,hw4,t4_data_story,c4_comm,yb,3,
agshaik,hw4,t4_data_story,c5_data_limit,yb,3,
ashwin2,hw4,t4_data_story,c1_goal,yb,2,Does not define the problem gap to showcase why the features matter
ashwin2,hw4,t4_data_story,c2_stats_ml,yb,2,Does not use modeling to answer a clear analytical question or uncover insight—only reports metrics without interpreting their significance or purpose.
ashwin2,hw4,t4_data_story,c3_viz,yb,3,
ashwin2,hw4,t4_data_story,c4_comm,yb,3,
ashwin2,hw4,t4_data_story,c5_data_limit,yb,3,
yiwentan,hw4,t4_data_story,c1_goal,yb,3,
yiwentan,hw4,t4_data_story,c2_stats_ml,yb,3,
yiwentan,hw4,t4_data_story,c3_viz,yb,3,
yiwentan,hw4,t4_data_story,c4_comm,yb,3,
yiwentan,hw4,t4_data_story,c5_data_limit,yb,3,
dtayal,hw4,t4_data_story,c1_goal,yb,3,
dtayal,hw4,t4_data_story,c2_stats_ml,yb,3,
dtayal,hw4,t4_data_story,c3_viz,yb,3,
dtayal,hw4,t4_data_story,c4_comm,yb,3,
dtayal,hw4,t4_data_story,c5_data_limit,yb,3,
hyanaman,hw4,t4_data_story,c1_goal,yb,3,
hyanaman,hw4,t4_data_story,c2_stats_ml,yb,3,
hyanaman,hw4,t4_data_story,c3_viz,yb,3,
hyanaman,hw4,t4_data_story,c4_comm,yb,3,
hyanaman,hw4,t4_data_story,c5_data_limit,yb,3,
