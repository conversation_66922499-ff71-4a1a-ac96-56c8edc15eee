andrewid,hw,task,item,grader,score,notes
aafonja,hw0,t1_data_clean,col1,yb,0.0,"does not actually clean, just checks for unique values"
aafonja,hw0,t1_data_clean,col2,yb,2.0,does not drop NaN
aafonja,hw0,t1_data_clean,col3,yb,0.0,
aafonja,hw0,t1_data_clean,col4,yb,0.0,
aafonja,hw0,t1_data_clean,col5,yb,0.0,
aafonja,hw0,t2_eda,q1,yb,3.0,advanced question
aafonja,hw0,t2_eda,a1,yb,0.0,
aafonja,hw0,t2_eda,p1,yb,2.0,bar chart is not needed and does not explain anything
aafonja,hw0,t2_eda,q2,yb,0.0,
aafonja,hw0,t2_eda,a2,yb,0.0,
aafonja,hw0,t2_eda,p2,yb,0.0,
aafonja,hw0,t2_eda,q3,yb,0.0,
aafonja,hw0,t2_eda,a3,yb,0.0,
aafonja,hw0,t2_eda,p3,yb,0.0,
aafonja,hw0,t2_eda,q4,yb,0.0,
aafonja,hw0,t2_eda,a4,yb,0.0,
aafonja,hw0,t2_eda,p4,yb,0.0,
aafonja,hw0,t2_eda,q5,yb,0.0,
aafonja,hw0,t2_eda,a5,yb,0.0,
aafonja,hw0,t2_eda,p5,yb,0.0,
aafonja,hw0,t3_ml,s1_train_test,yb,0.0,"puts something, but does not run (""Engine Size"" => where did that come from?)"
aafonja,hw0,t3_ml,s2_build_model,yb,0.0,
aafonja,hw0,t3_ml,s3_calc_performance,yb,0.0,
aafonja,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
aafonja,hw0,t3_ml,s5_best_model,yb,0.0,
aafonja,hw0,t4_data_story,c1_goal,yb,0.0,
aafonja,hw0,t4_data_story,c2_stats_ml,yb,0.0,
aafonja,hw0,t4_data_story,c3_viz,yb,0.0,
aafonja,hw0,t4_data_story,c4_comm,yb,0.0,
aafonja,hw0,t4_data_story,c5_data_limit,yb,0.0,
suhailk,hw0,t1_data_clean,col1,jk,0.0,
suhailk,hw0,t1_data_clean,col2,jk,2.0,does not convert to float or check for outliers
suhailk,hw0,t1_data_clean,col3,jk,2.0,does not convert to float
suhailk,hw0,t1_data_clean,col4,jk,2.0,conversion from 'four' to '4'; no conversion to int
suhailk,hw0,t1_data_clean,col5,jk,1.0,replacing w/ cleaned data
suhailk,hw0,t2_eda,q1,jk,3.0,
suhailk,hw0,t2_eda,a1,jk,3.0,
suhailk,hw0,t2_eda,p1,jk,2.0,bar chart ineffective for mean price
suhailk,hw0,t2_eda,q2,jk,1.0,simple question
suhailk,hw0,t2_eda,a2,jk,3.0,
suhailk,hw0,t2_eda,p2,jk,3.0,
suhailk,hw0,t2_eda,q3,jk,1.0,simple categorical analysis
suhailk,hw0,t2_eda,a3,jk,3.0,
suhailk,hw0,t2_eda,p3,jk,2.0,bar chart not very effective for visualization
suhailk,hw0,t2_eda,q4,jk,2.0,intermediate
suhailk,hw0,t2_eda,a4,jk,2.0,"does not calculate percentages, only raw counts"
suhailk,hw0,t2_eda,p4,jk,2.0,does not answer question; just bar chart of how many sold per year
suhailk,hw0,t2_eda,q5,jk,0.0,
suhailk,hw0,t2_eda,a5,jk,0.0,
suhailk,hw0,t2_eda,p5,jk,0.0,
suhailk,hw0,t3_ml,s1_train_test,jk,2.0,"test=0.4 kinda high, x&y swapped"
suhailk,hw0,t3_ml,s2_build_model,jk,2.0,no documentation of what model is predicting
suhailk,hw0,t3_ml,s3_calc_performance,jk,2.0,plots residual errors
suhailk,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
suhailk,hw0,t3_ml,s5_best_model,jk,0.0,only linear regression
suhailk,hw0,t4_data_story,c1_goal,jk,2.0,could use more insights/explanations
suhailk,hw0,t4_data_story,c2_stats_ml,jk,2.0,lacks statistics
suhailk,hw0,t4_data_story,c3_viz,jk,0.0,no visualization
suhailk,hw0,t4_data_story,c4_comm,jk,2.0,basic analysis
suhailk,hw0,t4_data_story,c5_data_limit,jk,0.0,
usa,hw0,t1_data_clean,col1,jk,3.0,
usa,hw0,t1_data_clean,col2,jk,3.0,
usa,hw0,t1_data_clean,col3,jk,3.0,
usa,hw0,t1_data_clean,col4,jk,3.0,
usa,hw0,t1_data_clean,col5,jk,3.0,
usa,hw0,t2_eda,q1,jk,3.0,
usa,hw0,t2_eda,a1,jk,3.0,
usa,hw0,t2_eda,p1,jk,3.0,
usa,hw0,t2_eda,q2,jk,3.0,
usa,hw0,t2_eda,a2,jk,3.0,
usa,hw0,t2_eda,p2,jk,3.0,
usa,hw0,t2_eda,q3,jk,3.0,
usa,hw0,t2_eda,a3,jk,3.0,
usa,hw0,t2_eda,p3,jk,3.0,
usa,hw0,t2_eda,q4,jk,3.0,
usa,hw0,t2_eda,a4,jk,3.0,
usa,hw0,t2_eda,p4,jk,3.0,
usa,hw0,t2_eda,q5,jk,3.0,
usa,hw0,t2_eda,a5,jk,3.0,
usa,hw0,t2_eda,p5,jk,3.0,
usa,hw0,t3_ml,s1_train_test,jk,3.0,
usa,hw0,t3_ml,s2_build_model,jk,3.0,
usa,hw0,t3_ml,s3_calc_performance,jk,3.0,
usa,hw0,t3_ml,s4_explain_feat_model,jk,3.0,
usa,hw0,t3_ml,s5_best_model,jk,3.0,
usa,hw0,t4_data_story,c1_goal,jk,3.0,
usa,hw0,t4_data_story,c2_stats_ml,jk,2.0,No numerical performance details
usa,hw0,t4_data_story,c3_viz,jk,0.0,No visualizations
usa,hw0,t4_data_story,c4_comm,jk,3.0,
usa,hw0,t4_data_story,c5_data_limit,jk,3.0,
barora,hw0,t1_data_clean,col1,jk,0.0,
barora,hw0,t1_data_clean,col2,jk,0.0,
barora,hw0,t1_data_clean,col3,jk,0.0,
barora,hw0,t1_data_clean,col4,jk,0.0,
barora,hw0,t1_data_clean,col5,jk,0.0,
barora,hw0,t2_eda,q1,jk,3.0,
barora,hw0,t2_eda,a1,jk,3.0,
barora,hw0,t2_eda,p1,jk,3.0,
barora,hw0,t2_eda,q2,jk,3.0,
barora,hw0,t2_eda,a2,jk,2.0,Quantify the effect
barora,hw0,t2_eda,p2,jk,2.0,Display actual correlation value
barora,hw0,t2_eda,q3,jk,3.0,
barora,hw0,t2_eda,a3,jk,2.0,Partially vague answer
barora,hw0,t2_eda,p3,jk,3.0,
barora,hw0,t2_eda,q4,jk,0.0,Incomplete
barora,hw0,t2_eda,a4,jk,0.0,Incomplete
barora,hw0,t2_eda,p4,jk,0.0,Incomplete
barora,hw0,t2_eda,q5,jk,3.0,
barora,hw0,t2_eda,a5,jk,2.0,Partially vague answer
barora,hw0,t2_eda,p5,jk,3.0,
barora,hw0,t3_ml,s1_train_test,jk,3.0,
barora,hw0,t3_ml,s2_build_model,jk,3.0,
barora,hw0,t3_ml,s3_calc_performance,jk,3.0,
barora,hw0,t3_ml,s4_explain_feat_model,jk,3.0,
barora,hw0,t3_ml,s5_best_model,jk,3.0,
barora,hw0,t4_data_story,c1_goal,jk,3.0,
barora,hw0,t4_data_story,c2_stats_ml,jk,2.0,No numerical performance details
barora,hw0,t4_data_story,c3_viz,jk,0.0,No visualizations
barora,hw0,t4_data_story,c4_comm,jk,3.0,
barora,hw0,t4_data_story,c5_data_limit,jk,3.0,
abhimava,hw0,t1_data_clean,col1,jk,3.0,
abhimava,hw0,t1_data_clean,col2,jk,3.0,
abhimava,hw0,t1_data_clean,col3,jk,3.0,
abhimava,hw0,t1_data_clean,col4,jk,0.0,Incomplete
abhimava,hw0,t1_data_clean,col5,jk,0.0,Incomplete
abhimava,hw0,t2_eda,q1,jk,0.0,No question
abhimava,hw0,t2_eda,a1,jk,0.0,No answer
abhimava,hw0,t2_eda,p1,jk,1.0,Vague process
abhimava,hw0,t2_eda,q2,jk,0.0,No question
abhimava,hw0,t2_eda,a2,jk,0.0,No answer
abhimava,hw0,t2_eda,p2,jk,3.0,
abhimava,hw0,t2_eda,q3,jk,0.0,Incomplete
abhimava,hw0,t2_eda,a3,jk,0.0,Incomplete
abhimava,hw0,t2_eda,p3,jk,0.0,Incomplete
abhimava,hw0,t2_eda,q4,jk,0.0,Incomplete
abhimava,hw0,t2_eda,a4,jk,0.0,Incomplete
abhimava,hw0,t2_eda,p4,jk,0.0,Incomplete
abhimava,hw0,t2_eda,q5,jk,0.0,Incomplete
abhimava,hw0,t2_eda,a5,jk,0.0,Incomplete
abhimava,hw0,t2_eda,p5,jk,0.0,Incomplete
abhimava,hw0,t3_ml,s1_train_test,jk,0.0,
abhimava,hw0,t3_ml,s2_build_model,jk,2.0,
abhimava,hw0,t3_ml,s3_calc_performance,jk,2.0,
abhimava,hw0,t3_ml,s4_explain_feat_model,jk,3.0,
abhimava,hw0,t3_ml,s5_best_model,jk,0.0,
abhimava,hw0,t4_data_story,c1_goal,jk,0.0,
abhimava,hw0,t4_data_story,c2_stats_ml,jk,0.0,
abhimava,hw0,t4_data_story,c3_viz,jk,0.0,
abhimava,hw0,t4_data_story,c4_comm,jk,0.0,
abhimava,hw0,t4_data_story,c5_data_limit,jk,0.0,
rupalc,hw0,t1_data_clean,col1,jk,1.0,"Standardize capitalization, drop missing values"
rupalc,hw0,t1_data_clean,col2,jk,0.0,Incomplete
rupalc,hw0,t1_data_clean,col3,jk,0.0,Incomplete
rupalc,hw0,t1_data_clean,col4,jk,0.0,Incomplete
rupalc,hw0,t1_data_clean,col5,jk,0.0,Incomplete
rupalc,hw0,t2_eda,q1,jk,3.0,
rupalc,hw0,t2_eda,a1,jk,0.0,
rupalc,hw0,t2_eda,p1,jk,1.0,
rupalc,hw0,t2_eda,q2,jk,3.0,
rupalc,hw0,t2_eda,a2,jk,0.0,
rupalc,hw0,t2_eda,p2,jk,2.0,Display actual correlation value
rupalc,hw0,t2_eda,q3,jk,2.0,
rupalc,hw0,t2_eda,a3,jk,0.0,
rupalc,hw0,t2_eda,p3,jk,0.0,
rupalc,hw0,t2_eda,q4,jk,0.0,
rupalc,hw0,t2_eda,a4,jk,0.0,
rupalc,hw0,t2_eda,p4,jk,0.0,
rupalc,hw0,t2_eda,q5,jk,0.0,
rupalc,hw0,t2_eda,a5,jk,0.0,
rupalc,hw0,t2_eda,p5,jk,0.0,
rupalc,hw0,t3_ml,s1_train_test,jk,3.0,
rupalc,hw0,t3_ml,s2_build_model,jk,1.0,
rupalc,hw0,t3_ml,s3_calc_performance,jk,3.0,
rupalc,hw0,t3_ml,s4_explain_feat_model,jk,3.0,
rupalc,hw0,t3_ml,s5_best_model,jk,3.0,
rupalc,hw0,t4_data_story,c1_goal,jk,2.0,
rupalc,hw0,t4_data_story,c2_stats_ml,jk,0.0,
rupalc,hw0,t4_data_story,c3_viz,jk,0.0,
rupalc,hw0,t4_data_story,c4_comm,jk,2.0,
rupalc,hw0,t4_data_story,c5_data_limit,jk,2.0,
achavess,hw0,t1_data_clean,col1,jk,1.0,
achavess,hw0,t1_data_clean,col2,jk,0.0,
achavess,hw0,t1_data_clean,col3,jk,0.0,
achavess,hw0,t1_data_clean,col4,jk,0.0,
achavess,hw0,t1_data_clean,col5,jk,0.0,
achavess,hw0,t2_eda,q1,jk,3.0,
achavess,hw0,t2_eda,a1,jk,3.0,
achavess,hw0,t2_eda,p1,jk,3.0,
achavess,hw0,t2_eda,q2,jk,0.0,
achavess,hw0,t2_eda,a2,jk,0.0,
achavess,hw0,t2_eda,p2,jk,0.0,
achavess,hw0,t2_eda,q3,jk,0.0,
achavess,hw0,t2_eda,a3,jk,0.0,
achavess,hw0,t2_eda,p3,jk,0.0,
achavess,hw0,t2_eda,q4,jk,0.0,
achavess,hw0,t2_eda,a4,jk,0.0,
achavess,hw0,t2_eda,p4,jk,0.0,
achavess,hw0,t2_eda,q5,jk,0.0,
achavess,hw0,t2_eda,a5,jk,0.0,
achavess,hw0,t2_eda,p5,jk,0.0,
achavess,hw0,t3_ml,s1_train_test,jk,0.0,
achavess,hw0,t3_ml,s2_build_model,jk,0.0,
achavess,hw0,t3_ml,s3_calc_performance,jk,0.0,
achavess,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
achavess,hw0,t3_ml,s5_best_model,jk,0.0,
achavess,hw0,t4_data_story,c1_goal,jk,0.0,
achavess,hw0,t4_data_story,c2_stats_ml,jk,0.0,
achavess,hw0,t4_data_story,c3_viz,jk,0.0,
achavess,hw0,t4_data_story,c4_comm,jk,0.0,
achavess,hw0,t4_data_story,c5_data_limit,jk,0.0,
alexding,hw0,t1_data_clean,col1,jk,1.0,
alexding,hw0,t1_data_clean,col2,jk,0.0,
alexding,hw0,t1_data_clean,col3,jk,0.0,
alexding,hw0,t1_data_clean,col4,jk,0.0,
alexding,hw0,t1_data_clean,col5,jk,0.0,
alexding,hw0,t2_eda,q1,jk,1.0,
alexding,hw0,t2_eda,a1,jk,0.0,
alexding,hw0,t2_eda,p1,jk,0.0,
alexding,hw0,t2_eda,q2,jk,3.0,
alexding,hw0,t2_eda,a2,jk,0.0,
alexding,hw0,t2_eda,p2,jk,0.0,
alexding,hw0,t2_eda,q3,jk,0.0,
alexding,hw0,t2_eda,a3,jk,0.0,
alexding,hw0,t2_eda,p3,jk,0.0,
alexding,hw0,t2_eda,q4,jk,0.0,
alexding,hw0,t2_eda,a4,jk,0.0,
alexding,hw0,t2_eda,p4,jk,0.0,
alexding,hw0,t2_eda,q5,jk,0.0,
alexding,hw0,t2_eda,a5,jk,0.0,
alexding,hw0,t2_eda,p5,jk,0.0,
alexding,hw0,t3_ml,s1_train_test,jk,3.0,
alexding,hw0,t3_ml,s2_build_model,jk,3.0,
alexding,hw0,t3_ml,s3_calc_performance,jk,2.0,
alexding,hw0,t3_ml,s4_explain_feat_model,jk,1.0,
alexding,hw0,t3_ml,s5_best_model,jk,2.0,
alexding,hw0,t4_data_story,c1_goal,jk,1.0,
alexding,hw0,t4_data_story,c2_stats_ml,jk,2.0,
alexding,hw0,t4_data_story,c3_viz,jk,0.0,
alexding,hw0,t4_data_story,c4_comm,jk,2.0,
alexding,hw0,t4_data_story,c5_data_limit,jk,2.0,
sijiaf,hw0,t1_data_clean,col1,jk,3.0,
sijiaf,hw0,t1_data_clean,col2,jk,3.0,
sijiaf,hw0,t1_data_clean,col3,jk,3.0,
sijiaf,hw0,t1_data_clean,col4,jk,1.0,
sijiaf,hw0,t1_data_clean,col5,jk,0.0,
sijiaf,hw0,t2_eda,q1,jk,3.0,
sijiaf,hw0,t2_eda,a1,jk,3.0,
sijiaf,hw0,t2_eda,p1,jk,3.0,
sijiaf,hw0,t2_eda,q2,jk,3.0,
sijiaf,hw0,t2_eda,a2,jk,3.0,
sijiaf,hw0,t2_eda,p2,jk,3.0,
sijiaf,hw0,t2_eda,q3,jk,3.0,
sijiaf,hw0,t2_eda,a3,jk,3.0,
sijiaf,hw0,t2_eda,p3,jk,3.0,
sijiaf,hw0,t2_eda,q4,jk,3.0,
sijiaf,hw0,t2_eda,a4,jk,3.0,
sijiaf,hw0,t2_eda,p4,jk,3.0,
sijiaf,hw0,t2_eda,q5,jk,0.0,
sijiaf,hw0,t2_eda,a5,jk,0.0,
sijiaf,hw0,t2_eda,p5,jk,0.0,
sijiaf,hw0,t3_ml,s1_train_test,jk,3.0,
sijiaf,hw0,t3_ml,s2_build_model,jk,3.0,
sijiaf,hw0,t3_ml,s3_calc_performance,jk,2.0,
sijiaf,hw0,t3_ml,s4_explain_feat_model,jk,2.0,
sijiaf,hw0,t3_ml,s5_best_model,jk,1.0,
sijiaf,hw0,t4_data_story,c1_goal,jk,0.0,
sijiaf,hw0,t4_data_story,c2_stats_ml,jk,0.0,
sijiaf,hw0,t4_data_story,c3_viz,jk,0.0,
sijiaf,hw0,t4_data_story,c4_comm,jk,0.0,
sijiaf,hw0,t4_data_story,c5_data_limit,jk,0.0,
elianah,hw0,t1_data_clean,col1,jk,2.0,doesn’t account for ‘Honda’
elianah,hw0,t1_data_clean,col2,jk,2.0,"did not handle missing values, did not check for duplicates"
elianah,hw0,t1_data_clean,col3,jk,2.0,misinterpreted as ‘trim’ rather than standardizing ‘Dealer or Individual’
elianah,hw0,t1_data_clean,col4,jk,0.0,no actual cleaning done
elianah,hw0,t1_data_clean,col5,jk,0.0,misinterpretation (similar to Column 3) 
elianah,hw0,t2_eda,q1,jk,3.0,
elianah,hw0,t2_eda,a1,jk,2.0,No specific statistics
elianah,hw0,t2_eda,p1,jk,3.0,
elianah,hw0,t2_eda,q2,jk,3.0,
elianah,hw0,t2_eda,a2,jk,2.0, did not quantify correlation/slope
elianah,hw0,t2_eda,p2,jk,2.0,"only showed scatterplot, did not visualize coefficient"
elianah,hw0,t2_eda,q3,jk,0.0,
elianah,hw0,t2_eda,a3,jk,0.0,
elianah,hw0,t2_eda,p3,jk,0.0,
elianah,hw0,t2_eda,q4,jk,3.0,
elianah,hw0,t2_eda,a4,jk,2.0,no correlation coefficient
elianah,hw0,t2_eda,p4,jk,2.0,no regression line 
elianah,hw0,t2_eda,q5,jk,3.0,
elianah,hw0,t2_eda,a5,jk,2.0,no specific statistics
elianah,hw0,t2_eda,p5,jk,2.0,no visualization
elianah,hw0,t3_ml,s1_train_test,jk,0.0,
elianah,hw0,t3_ml,s2_build_model,jk,0.0,
elianah,hw0,t3_ml,s3_calc_performance,jk,0.0,
elianah,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
elianah,hw0,t3_ml,s5_best_model,jk,0.0,
elianah,hw0,t4_data_story,c1_goal,jk,0.0,
elianah,hw0,t4_data_story,c2_stats_ml,jk,0.0,
elianah,hw0,t4_data_story,c3_viz,jk,0.0,
elianah,hw0,t4_data_story,c4_comm,jk,0.0,
elianah,hw0,t4_data_story,c5_data_limit,jk,0.0,
radhikaj,hw0,t1_data_clean,col1,jk,3.0,
radhikaj,hw0,t1_data_clean,col2,jk,2.0,"did not handle missing values, did not check for duplicates"
radhikaj,hw0,t1_data_clean,col3,jk,2.0,"did not handle missing values, did not check for duplicates"
radhikaj,hw0,t1_data_clean,col4,jk,1.0,"only removed ‘$’ and ‘,’"
radhikaj,hw0,t1_data_clean,col5,jk,0.0,no cleaning done
radhikaj,hw0,t2_eda,q1,jk,1.0,simple counting question
radhikaj,hw0,t2_eda,a1,jk,1.0,incorrect calculation
radhikaj,hw0,t2_eda,p1,jk,1.0,incorrect visualization
radhikaj,hw0,t2_eda,q2,jk,1.0,simple counting question
radhikaj,hw0,t2_eda,a2,jk,3.0,
radhikaj,hw0,t2_eda,p2,jk,1.0,"incorrect visualization; line chart of year vs price, should be bar chart showing numbers of cars sold by year"
radhikaj,hw0,t2_eda,q3,jk,2.0,
radhikaj,hw0,t2_eda,a3,jk,3.0,
radhikaj,hw0,t2_eda,p3,jk,1.0,incorrect visualization; year vs price is irrelevant to question
radhikaj,hw0,t2_eda,q4,jk,3.0,
radhikaj,hw0,t2_eda,a4,jk,3.0,
radhikaj,hw0,t2_eda,p4,jk,3.0,
radhikaj,hw0,t2_eda,q5,jk,3.0,
radhikaj,hw0,t2_eda,a5,jk,3.0,
radhikaj,hw0,t2_eda,p5,jk,3.0,
radhikaj,hw0,t3_ml,s1_train_test,jk,0.0,
radhikaj,hw0,t3_ml,s2_build_model,jk,0.0,
radhikaj,hw0,t3_ml,s3_calc_performance,jk,0.0,
radhikaj,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
radhikaj,hw0,t3_ml,s5_best_model,jk,0.0,
radhikaj,hw0,t4_data_story,c1_goal,jk,0.0,
radhikaj,hw0,t4_data_story,c2_stats_ml,jk,0.0,
radhikaj,hw0,t4_data_story,c3_viz,jk,0.0,
radhikaj,hw0,t4_data_story,c4_comm,jk,0.0,
radhikaj,hw0,t4_data_story,c5_data_limit,jk,0.0,
sjashnan,hw0,t1_data_clean,col1,jk,0.0,wrong language
sjashnan,hw0,t1_data_clean,col2,jk,0.0,
sjashnan,hw0,t1_data_clean,col3,jk,0.0,
sjashnan,hw0,t1_data_clean,col4,jk,0.0,
sjashnan,hw0,t1_data_clean,col5,jk,0.0,
sjashnan,hw0,t2_eda,q1,jk,3.0,
sjashnan,hw0,t2_eda,a1,jk,3.0,
sjashnan,hw0,t2_eda,p1,jk,2.0,no visualization
sjashnan,hw0,t2_eda,q2,jk,0.0,
sjashnan,hw0,t2_eda,a2,jk,0.0,
sjashnan,hw0,t2_eda,p2,jk,0.0,
sjashnan,hw0,t2_eda,q3,jk,0.0,
sjashnan,hw0,t2_eda,a3,jk,0.0,
sjashnan,hw0,t2_eda,p3,jk,0.0,
sjashnan,hw0,t2_eda,q4,jk,0.0,
sjashnan,hw0,t2_eda,a4,jk,0.0,
sjashnan,hw0,t2_eda,p4,jk,0.0,
sjashnan,hw0,t2_eda,q5,jk,0.0,
sjashnan,hw0,t2_eda,a5,jk,0.0,
sjashnan,hw0,t2_eda,p5,jk,0.0,
sjashnan,hw0,t3_ml,s1_train_test,jk,0.0,
sjashnan,hw0,t3_ml,s2_build_model,jk,2.0,
sjashnan,hw0,t3_ml,s3_calc_performance,jk,3.0,
sjashnan,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
sjashnan,hw0,t3_ml,s5_best_model,jk,1.0,
sjashnan,hw0,t4_data_story,c1_goal,jk,3.0,
sjashnan,hw0,t4_data_story,c2_stats_ml,jk,2.0,
sjashnan,hw0,t4_data_story,c3_viz,jk,0.0,
sjashnan,hw0,t4_data_story,c4_comm,jk,3.0,
sjashnan,hw0,t4_data_story,c5_data_limit,jk,0.0,
rkkim,hw0,t1_data_clean,col1,jk,3.0,
rkkim,hw0,t1_data_clean,col2,jk,0.0,incorrect
rkkim,hw0,t1_data_clean,col3,jk,0.0,no cleaning done
rkkim,hw0,t1_data_clean,col4,jk,0.0,
rkkim,hw0,t1_data_clean,col5,jk,0.0,
rkkim,hw0,t2_eda,q1,jk,2.0,
rkkim,hw0,t2_eda,a1,jk,1.0,
rkkim,hw0,t2_eda,p1,jk,1.0,incorrect implementation
rkkim,hw0,t2_eda,q2,jk,0.0,
rkkim,hw0,t2_eda,a2,jk,0.0,
rkkim,hw0,t2_eda,p2,jk,0.0,
rkkim,hw0,t2_eda,q3,jk,0.0,
rkkim,hw0,t2_eda,a3,jk,0.0,
rkkim,hw0,t2_eda,p3,jk,0.0,
rkkim,hw0,t2_eda,q4,jk,0.0,
rkkim,hw0,t2_eda,a4,jk,0.0,
rkkim,hw0,t2_eda,p4,jk,0.0,
rkkim,hw0,t2_eda,q5,jk,0.0,
rkkim,hw0,t2_eda,a5,jk,0.0,
rkkim,hw0,t2_eda,p5,jk,0.0,
rkkim,hw0,t3_ml,s1_train_test,jk,0.0,
rkkim,hw0,t3_ml,s2_build_model,jk,0.0,
rkkim,hw0,t3_ml,s3_calc_performance,jk,0.0,
rkkim,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
rkkim,hw0,t3_ml,s5_best_model,jk,0.0,
rkkim,hw0,t4_data_story,c1_goal,jk,0.0,
rkkim,hw0,t4_data_story,c2_stats_ml,jk,0.0,
rkkim,hw0,t4_data_story,c3_viz,jk,0.0,
rkkim,hw0,t4_data_story,c4_comm,jk,0.0,
rkkim,hw0,t4_data_story,c5_data_limit,jk,0.0,
elakra,hw0,t1_data_clean,col1,jk,3.0,
elakra,hw0,t1_data_clean,col2,jk,3.0,
elakra,hw0,t1_data_clean,col3,jk,2.0,interior color' doesn't exist
elakra,hw0,t1_data_clean,col4,jk,0.0,
elakra,hw0,t1_data_clean,col5,jk,0.0,
elakra,hw0,t2_eda,q1,jk,2.0,"does not analyze trends, relationships, or correlations"
elakra,hw0,t2_eda,a1,jk,3.0,
elakra,hw0,t2_eda,p1,jk,3.0,
elakra,hw0,t2_eda,q2,jk,3.0,
elakra,hw0,t2_eda,a2,jk,3.0,
elakra,hw0,t2_eda,p2,jk,3.0,
elakra,hw0,t2_eda,q3,jk,0.0,
elakra,hw0,t2_eda,a3,jk,0.0,
elakra,hw0,t2_eda,p3,jk,0.0,
elakra,hw0,t2_eda,q4,jk,0.0,
elakra,hw0,t2_eda,a4,jk,0.0,
elakra,hw0,t2_eda,p4,jk,0.0,
elakra,hw0,t2_eda,q5,jk,0.0,
elakra,hw0,t2_eda,a5,jk,0.0,
elakra,hw0,t2_eda,p5,jk,0.0,
elakra,hw0,t3_ml,s1_train_test,jk,0.0,
elakra,hw0,t3_ml,s2_build_model,jk,0.0,
elakra,hw0,t3_ml,s3_calc_performance,jk,0.0,
elakra,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
elakra,hw0,t3_ml,s5_best_model,jk,0.0,
elakra,hw0,t4_data_story,c1_goal,jk,0.0,
elakra,hw0,t4_data_story,c2_stats_ml,jk,0.0,
elakra,hw0,t4_data_story,c3_viz,jk,0.0,
elakra,hw0,t4_data_story,c4_comm,jk,0.0,
elakra,hw0,t4_data_story,c5_data_limit,jk,0.0,
mlal,hw0,t1_data_clean,col1,jk,3.0,
mlal,hw0,t1_data_clean,col2,jk,3.0,
mlal,hw0,t1_data_clean,col3,jk,0.0,
mlal,hw0,t1_data_clean,col4,jk,0.0,
mlal,hw0,t1_data_clean,col5,jk,0.0,
mlal,hw0,t2_eda,q1,jk,3.0,
mlal,hw0,t2_eda,a1,jk,3.0,
mlal,hw0,t2_eda,p1,jk,3.0,
mlal,hw0,t2_eda,q2,jk,3.0,
mlal,hw0,t2_eda,a2,jk,3.0,
mlal,hw0,t2_eda,p2,jk,3.0,
mlal,hw0,t2_eda,q3,jk,3.0,
mlal,hw0,t2_eda,a3,jk,3.0,
mlal,hw0,t2_eda,p3,jk,3.0,
mlal,hw0,t2_eda,q4,jk,0.0,
mlal,hw0,t2_eda,a4,jk,0.0,
mlal,hw0,t2_eda,p4,jk,0.0,
mlal,hw0,t2_eda,q5,jk,0.0,
mlal,hw0,t2_eda,a5,jk,0.0,
mlal,hw0,t2_eda,p5,jk,0.0,
mlal,hw0,t3_ml,s1_train_test,jk,0.0,
mlal,hw0,t3_ml,s2_build_model,jk,0.0,
mlal,hw0,t3_ml,s3_calc_performance,jk,0.0,
mlal,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
mlal,hw0,t3_ml,s5_best_model,jk,0.0,
mlal,hw0,t4_data_story,c1_goal,jk,0.0,
mlal,hw0,t4_data_story,c2_stats_ml,jk,0.0,
mlal,hw0,t4_data_story,c3_viz,jk,0.0,
mlal,hw0,t4_data_story,c4_comm,jk,0.0,
mlal,hw0,t4_data_story,c5_data_limit,jk,0.0,
wenyili,hw0,t1_data_clean,col1,jk,3.0,
wenyili,hw0,t1_data_clean,col2,jk,3.0,
wenyili,hw0,t1_data_clean,col3,jk,3.0,
wenyili,hw0,t1_data_clean,col4,jk,3.0,
wenyili,hw0,t1_data_clean,col5,jk,3.0,
wenyili,hw0,t2_eda,q1,jk,0.0,
wenyili,hw0,t2_eda,a1,jk,3.0,
wenyili,hw0,t2_eda,p1,jk,3.0,
wenyili,hw0,t2_eda,q2,jk,3.0,
wenyili,hw0,t2_eda,a2,jk,0.0,
wenyili,hw0,t2_eda,p2,jk,0.0,
wenyili,hw0,t2_eda,q3,jk,0.0,
wenyili,hw0,t2_eda,a3,jk,0.0,
wenyili,hw0,t2_eda,p3,jk,0.0,
wenyili,hw0,t2_eda,q4,jk,0.0,
wenyili,hw0,t2_eda,a4,jk,0.0,
wenyili,hw0,t2_eda,p4,jk,0.0,
wenyili,hw0,t2_eda,q5,jk,0.0,
wenyili,hw0,t2_eda,a5,jk,0.0,
wenyili,hw0,t2_eda,p5,jk,0.0,
wenyili,hw0,t3_ml,s1_train_test,jk,0.0,
wenyili,hw0,t3_ml,s2_build_model,jk,0.0,
wenyili,hw0,t3_ml,s3_calc_performance,jk,0.0,
wenyili,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
wenyili,hw0,t3_ml,s5_best_model,jk,0.0,
wenyili,hw0,t4_data_story,c1_goal,jk,0.0,
wenyili,hw0,t4_data_story,c2_stats_ml,jk,0.0,
wenyili,hw0,t4_data_story,c3_viz,jk,0.0,
wenyili,hw0,t4_data_story,c4_comm,jk,0.0,
wenyili,hw0,t4_data_story,c5_data_limit,jk,0.0,
gracelia,hw0,t1_data_clean,col1,jk,1.0,"NaN values removed, conversion to int failed"
gracelia,hw0,t1_data_clean,col2,jk,0.0,
gracelia,hw0,t1_data_clean,col3,jk,0.0,
gracelia,hw0,t1_data_clean,col4,jk,0.0,
gracelia,hw0,t1_data_clean,col5,jk,0.0,
gracelia,hw0,t2_eda,q1,jk,1.0,
gracelia,hw0,t2_eda,a1,jk,3.0,
gracelia,hw0,t2_eda,p1,jk,2.0,Visualization incomplete
gracelia,hw0,t2_eda,q2,jk,2.0,
gracelia,hw0,t2_eda,a2,jk,2.0,No statistics
gracelia,hw0,t2_eda,p2,jk,3.0,
gracelia,hw0,t2_eda,q3,jk,0.0,
gracelia,hw0,t2_eda,a3,jk,0.0,
gracelia,hw0,t2_eda,p3,jk,0.0,
gracelia,hw0,t2_eda,q4,jk,0.0,
gracelia,hw0,t2_eda,a4,jk,0.0,
gracelia,hw0,t2_eda,p4,jk,0.0,
gracelia,hw0,t2_eda,q5,jk,0.0,
gracelia,hw0,t2_eda,a5,jk,0.0,
gracelia,hw0,t2_eda,p5,jk,0.0,
gracelia,hw0,t3_ml,s1_train_test,jk,0.0,
gracelia,hw0,t3_ml,s2_build_model,jk,0.0,
gracelia,hw0,t3_ml,s3_calc_performance,jk,0.0,
gracelia,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
gracelia,hw0,t3_ml,s5_best_model,jk,0.0,
gracelia,hw0,t4_data_story,c1_goal,jk,0.0,
gracelia,hw0,t4_data_story,c2_stats_ml,jk,0.0,
gracelia,hw0,t4_data_story,c3_viz,jk,0.0,
gracelia,hw0,t4_data_story,c4_comm,jk,0.0,
gracelia,hw0,t4_data_story,c5_data_limit,jk,0.0,
charlenl,hw0,t1_data_clean,col1,jk,3.0,
charlenl,hw0,t1_data_clean,col2,jk,0.0,
charlenl,hw0,t1_data_clean,col3,jk,1.0,Incomplete code
charlenl,hw0,t1_data_clean,col4,jk,0.0,
charlenl,hw0,t1_data_clean,col5,jk,0.0,
charlenl,hw0,t2_eda,q1,jk,3.0,
charlenl,hw0,t2_eda,a1,jk,3.0,
charlenl,hw0,t2_eda,p1,jk,1.0,
charlenl,hw0,t2_eda,q2,jk,0.0,
charlenl,hw0,t2_eda,a2,jk,0.0,
charlenl,hw0,t2_eda,p2,jk,0.0,
charlenl,hw0,t2_eda,q3,jk,0.0,
charlenl,hw0,t2_eda,a3,jk,0.0,
charlenl,hw0,t2_eda,p3,jk,0.0,
charlenl,hw0,t2_eda,q4,jk,0.0,
charlenl,hw0,t2_eda,a4,jk,0.0,
charlenl,hw0,t2_eda,p4,jk,0.0,
charlenl,hw0,t2_eda,q5,jk,0.0,
charlenl,hw0,t2_eda,a5,jk,0.0,
charlenl,hw0,t2_eda,p5,jk,0.0,
charlenl,hw0,t3_ml,s1_train_test,jk,0.0,
charlenl,hw0,t3_ml,s2_build_model,jk,3.0,
charlenl,hw0,t3_ml,s3_calc_performance,jk,1.0,
charlenl,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
charlenl,hw0,t3_ml,s5_best_model,jk,0.0,
charlenl,hw0,t4_data_story,c1_goal,jk,0.0,
charlenl,hw0,t4_data_story,c2_stats_ml,jk,0.0,
charlenl,hw0,t4_data_story,c3_viz,jk,0.0,
charlenl,hw0,t4_data_story,c4_comm,jk,0.0,
charlenl,hw0,t4_data_story,c5_data_limit,jk,0.0,
nmedaram,hw0,t1_data_clean,col1,jk,0.0,Renamed column
nmedaram,hw0,t1_data_clean,col2,jk,1.0,"standardizing ‘Accord EX-L’ to ‘Accord EXL’ but doesn’t exist in car model, removed missing values"
nmedaram,hw0,t1_data_clean,col3,jk,1.0,
nmedaram,hw0,t1_data_clean,col4,jk,0.0,
nmedaram,hw0,t1_data_clean,col5,jk,1.0,drops VIN column if it exists (?)
nmedaram,hw0,t2_eda,q1,jk,3.0,
nmedaram,hw0,t2_eda,a1,jk,2.0,No statistic
nmedaram,hw0,t2_eda,p1,jk,3.0,
nmedaram,hw0,t2_eda,q2,jk,3.0,
nmedaram,hw0,t2_eda,a2,jk,1.0,
nmedaram,hw0,t2_eda,p2,jk,3.0,
nmedaram,hw0,t2_eda,q3,jk,2.0,"Advanced, but doesn't apply to dataset"
nmedaram,hw0,t2_eda,a3,jk,1.0,"Good insight, no answer"
nmedaram,hw0,t2_eda,p3,jk,1.0,
nmedaram,hw0,t2_eda,q4,jk,3.0,
nmedaram,hw0,t2_eda,a4,jk,1.0,"Good insight, no answer"
nmedaram,hw0,t2_eda,p4,jk,3.0,
nmedaram,hw0,t2_eda,q5,jk,3.0,
nmedaram,hw0,t2_eda,a5,jk,1.0,"Good insight, no answer"
nmedaram,hw0,t2_eda,p5,jk,2.0,Incomplete
nmedaram,hw0,t3_ml,s1_train_test,jk,3.0,
nmedaram,hw0,t3_ml,s2_build_model,jk,3.0,
nmedaram,hw0,t3_ml,s3_calc_performance,jk,3.0,
nmedaram,hw0,t3_ml,s4_explain_feat_model,jk,3.0,
nmedaram,hw0,t3_ml,s5_best_model,jk,1.0,"Used dummy data, metrics all came out to 0"
nmedaram,hw0,t4_data_story,c1_goal,jk,3.0,
nmedaram,hw0,t4_data_story,c2_stats_ml,jk,2.0,"No statistics, good insights"
nmedaram,hw0,t4_data_story,c3_viz,jk,0.0,
nmedaram,hw0,t4_data_story,c4_comm,jk,3.0,
nmedaram,hw0,t4_data_story,c5_data_limit,jk,1.0,
somyameh,hw0,t1_data_clean,col1,jk,1.0,
somyameh,hw0,t1_data_clean,col2,jk,3.0,
somyameh,hw0,t1_data_clean,col3,jk,3.0,
somyameh,hw0,t1_data_clean,col4,jk,2.0,Not a valid column
somyameh,hw0,t1_data_clean,col5,jk,2.0,Not a valid column
somyameh,hw0,t2_eda,q1,jk,1.0,
somyameh,hw0,t2_eda,a1,jk,3.0,
somyameh,hw0,t2_eda,p1,jk,3.0,
somyameh,hw0,t2_eda,q2,jk,3.0,
somyameh,hw0,t2_eda,a2,jk,2.0,Correlation found but not included in answer
somyameh,hw0,t2_eda,p2,jk,2.0,Scatterplot but not correlation coefficient visualized
somyameh,hw0,t2_eda,q3,jk,3.0,
somyameh,hw0,t2_eda,a3,jk,2.0,Correlation found but not included in answer
somyameh,hw0,t2_eda,p3,jk,2.0,Scatterplot but not correlation coefficient visualized
somyameh,hw0,t2_eda,q4,jk,2.0,
somyameh,hw0,t2_eda,a4,jk,1.0,Trim column does not exist
somyameh,hw0,t2_eda,p4,jk,1.0,Graphing incorrect values
somyameh,hw0,t2_eda,q5,jk,2.0,
somyameh,hw0,t2_eda,a5,jk,1.0,Condition column does not exist
somyameh,hw0,t2_eda,p5,jk,1.0,Graphing incorrect values
somyameh,hw0,t3_ml,s1_train_test,jk,3.0,
somyameh,hw0,t3_ml,s2_build_model,jk,3.0,
somyameh,hw0,t3_ml,s3_calc_performance,jk,3.0,
somyameh,hw0,t3_ml,s4_explain_feat_model,jk,3.0,
somyameh,hw0,t3_ml,s5_best_model,jk,3.0,
somyameh,hw0,t4_data_story,c1_goal,jk,2.0,
somyameh,hw0,t4_data_story,c2_stats_ml,jk,2.0,
somyameh,hw0,t4_data_story,c3_viz,jk,0.0,
somyameh,hw0,t4_data_story,c4_comm,jk,2.0,
somyameh,hw0,t4_data_story,c5_data_limit,jk,3.0,
sohammon,hw0,t1_data_clean,col1,jk,2.0,does not convert to float or check for outliers
sohammon,hw0,t1_data_clean,col2,jk,2.0,"could check for missing values,  convert to float, check outliers"
sohammon,hw0,t1_data_clean,col3,jk,2.0,
sohammon,hw0,t1_data_clean,col4,jk,3.0,
sohammon,hw0,t1_data_clean,col5,jk,3.0,
sohammon,hw0,t2_eda,q1,jk,3.0,
sohammon,hw0,t2_eda,a1,jk,2.0,no statistics
sohammon,hw0,t2_eda,p1,jk,3.0,
sohammon,hw0,t2_eda,q2,jk,0.0,
sohammon,hw0,t2_eda,a2,jk,0.0,
sohammon,hw0,t2_eda,p2,jk,0.0,
sohammon,hw0,t2_eda,q3,jk,0.0,
sohammon,hw0,t2_eda,a3,jk,0.0,
sohammon,hw0,t2_eda,p3,jk,0.0,
sohammon,hw0,t2_eda,q4,jk,0.0,
sohammon,hw0,t2_eda,a4,jk,0.0,
sohammon,hw0,t2_eda,p4,jk,0.0,
sohammon,hw0,t2_eda,q5,jk,0.0,
sohammon,hw0,t2_eda,a5,jk,0.0,
sohammon,hw0,t2_eda,p5,jk,0.0,
sohammon,hw0,t3_ml,s1_train_test,jk,0.0,
sohammon,hw0,t3_ml,s2_build_model,jk,0.0,
sohammon,hw0,t3_ml,s3_calc_performance,jk,0.0,
sohammon,hw0,t3_ml,s4_explain_feat_model,jk,0.0,
sohammon,hw0,t3_ml,s5_best_model,jk,0.0,
sohammon,hw0,t4_data_story,c1_goal,jk,0.0,
sohammon,hw0,t4_data_story,c2_stats_ml,jk,0.0,
sohammon,hw0,t4_data_story,c3_viz,jk,0.0,
sohammon,hw0,t4_data_story,c4_comm,jk,0.0,
sohammon,hw0,t4_data_story,c5_data_limit,jk,0.0,
mnandiga,hw0,t1_data_clean,col1,yb,0.0,makes everything to NaN
mnandiga,hw0,t1_data_clean,col2,yb,1.0,"does not adjust for different types of ""mile"""
mnandiga,hw0,t1_data_clean,col3,yb,3.0,
mnandiga,hw0,t1_data_clean,col4,yb,2.0,not changing to int
mnandiga,hw0,t1_data_clean,col5,yb,0.0,same column as 2
mnandiga,hw0,t2_eda,q1,yb,3.0,advanced question
mnandiga,hw0,t2_eda,a1,yb,2.0,"would like to see more analysis with quantitative evidence, etc"
mnandiga,hw0,t2_eda,p1,yb,1.0,the graph only plots two points.
mnandiga,hw0,t2_eda,q2,yb,3.0,
mnandiga,hw0,t2_eda,a2,yb,2.0,would like to see more quantitative
mnandiga,hw0,t2_eda,p2,yb,1.0,"did not use the cleaned version of the dataset, so there's nothing on the graph"
mnandiga,hw0,t2_eda,q3,yb,3.0,
mnandiga,hw0,t2_eda,a3,yb,1.0,
mnandiga,hw0,t2_eda,p3,yb,1.0,
mnandiga,hw0,t2_eda,q4,yb,1.0,
mnandiga,hw0,t2_eda,a4,yb,1.0,
mnandiga,hw0,t2_eda,p4,yb,1.0,
mnandiga,hw0,t2_eda,q5,yb,3.0,
mnandiga,hw0,t2_eda,a5,yb,1.0,
mnandiga,hw0,t2_eda,p5,yb,1.0,
mnandiga,hw0,t3_ml,s1_train_test,yb,1.0,there was an effort to split
mnandiga,hw0,t3_ml,s2_build_model,yb,0.0,
mnandiga,hw0,t3_ml,s3_calc_performance,yb,0.0,
mnandiga,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
mnandiga,hw0,t3_ml,s5_best_model,yb,0.0,
mnandiga,hw0,t4_data_story,c1_goal,yb,0.0,
mnandiga,hw0,t4_data_story,c2_stats_ml,yb,2.0,
mnandiga,hw0,t4_data_story,c3_viz,yb,0.0,
mnandiga,hw0,t4_data_story,c4_comm,yb,1.0,
mnandiga,hw0,t4_data_story,c5_data_limit,yb,1.0,
jennyou,hw0,t1_data_clean,col1,yb,0.0,no submission
jennyou,hw0,t1_data_clean,col2,yb,0.0,
jennyou,hw0,t1_data_clean,col3,yb,0.0,
jennyou,hw0,t1_data_clean,col4,yb,0.0,
jennyou,hw0,t1_data_clean,col5,yb,0.0,
jennyou,hw0,t2_eda,q1,yb,0.0,
jennyou,hw0,t2_eda,a1,yb,0.0,
jennyou,hw0,t2_eda,p1,yb,0.0,
jennyou,hw0,t2_eda,q2,yb,0.0,
jennyou,hw0,t2_eda,a2,yb,0.0,
jennyou,hw0,t2_eda,p2,yb,0.0,
jennyou,hw0,t2_eda,q3,yb,0.0,
jennyou,hw0,t2_eda,a3,yb,0.0,
jennyou,hw0,t2_eda,p3,yb,0.0,
jennyou,hw0,t2_eda,q4,yb,0.0,
jennyou,hw0,t2_eda,a4,yb,0.0,
jennyou,hw0,t2_eda,p4,yb,0.0,
jennyou,hw0,t2_eda,q5,yb,0.0,
jennyou,hw0,t2_eda,a5,yb,0.0,
jennyou,hw0,t2_eda,p5,yb,0.0,
jennyou,hw0,t3_ml,s1_train_test,yb,0.0,
jennyou,hw0,t3_ml,s2_build_model,yb,0.0,
jennyou,hw0,t3_ml,s3_calc_performance,yb,0.0,
jennyou,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
jennyou,hw0,t3_ml,s5_best_model,yb,0.0,
jennyou,hw0,t4_data_story,c1_goal,yb,0.0,
jennyou,hw0,t4_data_story,c2_stats_ml,yb,0.0,
jennyou,hw0,t4_data_story,c3_viz,yb,0.0,
jennyou,hw0,t4_data_story,c4_comm,yb,0.0,
jennyou,hw0,t4_data_story,c5_data_limit,yb,0.0,
anshp,hw0,t1_data_clean,col1,yb,0.0,he wrote it as like text but not code code...?
anshp,hw0,t1_data_clean,col2,yb,0.0,
anshp,hw0,t1_data_clean,col3,yb,0.0,
anshp,hw0,t1_data_clean,col4,yb,0.0,
anshp,hw0,t1_data_clean,col5,yb,0.0,
anshp,hw0,t2_eda,q1,yb,0.0,no question
anshp,hw0,t2_eda,a1,yb,0.0,no insight
anshp,hw0,t2_eda,p1,yb,1.0,has something about price
anshp,hw0,t2_eda,q2,yb,0.0,no question
anshp,hw0,t2_eda,a2,yb,0.0,no insight
anshp,hw0,t2_eda,p2,yb,1.0,has something about distribution of price
anshp,hw0,t2_eda,q3,yb,0.0,
anshp,hw0,t2_eda,a3,yb,0.0,
anshp,hw0,t2_eda,p3,yb,0.0,
anshp,hw0,t2_eda,q4,yb,0.0,
anshp,hw0,t2_eda,a4,yb,0.0,
anshp,hw0,t2_eda,p4,yb,0.0,
anshp,hw0,t2_eda,q5,yb,0.0,
anshp,hw0,t2_eda,a5,yb,0.0,
anshp,hw0,t2_eda,p5,yb,0.0,
anshp,hw0,t3_ml,s1_train_test,yb,3.0,
anshp,hw0,t3_ml,s2_build_model,yb,3.0,
anshp,hw0,t3_ml,s3_calc_performance,yb,3.0,
anshp,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
anshp,hw0,t3_ml,s5_best_model,yb,0.0,
anshp,hw0,t4_data_story,c1_goal,yb,0.0,
anshp,hw0,t4_data_story,c2_stats_ml,yb,0.0,
anshp,hw0,t4_data_story,c3_viz,yb,0.0,
anshp,hw0,t4_data_story,c4_comm,yb,0.0,
anshp,hw0,t4_data_story,c5_data_limit,yb,0.0,
hpasha,hw0,t1_data_clean,col1,yb,0.0,
hpasha,hw0,t1_data_clean,col2,yb,0.0,
hpasha,hw0,t1_data_clean,col3,yb,0.0,
hpasha,hw0,t1_data_clean,col4,yb,0.0,
hpasha,hw0,t1_data_clean,col5,yb,0.0,
hpasha,hw0,t2_eda,q1,yb,2.0,intermediate question
hpasha,hw0,t2_eda,a1,yb,0.0,
hpasha,hw0,t2_eda,p1,yb,0.0,
hpasha,hw0,t2_eda,q2,yb,0.0,
hpasha,hw0,t2_eda,a2,yb,0.0,
hpasha,hw0,t2_eda,p2,yb,0.0,
hpasha,hw0,t2_eda,q3,yb,0.0,
hpasha,hw0,t2_eda,a3,yb,0.0,
hpasha,hw0,t2_eda,p3,yb,0.0,
hpasha,hw0,t2_eda,q4,yb,0.0,
hpasha,hw0,t2_eda,a4,yb,0.0,
hpasha,hw0,t2_eda,p4,yb,0.0,
hpasha,hw0,t2_eda,q5,yb,0.0,
hpasha,hw0,t2_eda,a5,yb,0.0,
hpasha,hw0,t2_eda,p5,yb,0.0,
hpasha,hw0,t3_ml,s1_train_test,yb,0.0,
hpasha,hw0,t3_ml,s2_build_model,yb,0.0,
hpasha,hw0,t3_ml,s3_calc_performance,yb,0.0,
hpasha,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
hpasha,hw0,t3_ml,s5_best_model,yb,0.0,
hpasha,hw0,t4_data_story,c1_goal,yb,0.0,
hpasha,hw0,t4_data_story,c2_stats_ml,yb,0.0,
hpasha,hw0,t4_data_story,c3_viz,yb,0.0,
hpasha,hw0,t4_data_story,c4_comm,yb,0.0,
hpasha,hw0,t4_data_story,c5_data_limit,yb,0.0,
kqasba,hw0,t1_data_clean,col1,yb,2.0,does not drop NaN
kqasba,hw0,t1_data_clean,col2,yb,2.0,fills NaN with median instead of dropping
kqasba,hw0,t1_data_clean,col3,yb,1.0,didn't really clean anything
kqasba,hw0,t1_data_clean,col4,yb,2.0,does not drop NaN
kqasba,hw0,t1_data_clean,col5,yb,2.0,does not drop NaN
kqasba,hw0,t2_eda,q1,yb,3.0,advanced question
kqasba,hw0,t2_eda,a1,yb,0.0,
kqasba,hw0,t2_eda,p1,yb,3.0,
kqasba,hw0,t2_eda,q2,yb,3.0,advanced question
kqasba,hw0,t2_eda,a2,yb,0.0,
kqasba,hw0,t2_eda,p2,yb,3.0,
kqasba,hw0,t2_eda,q3,yb,1.0,simple question; should have known only one car model
kqasba,hw0,t2_eda,a3,yb,0.0,
kqasba,hw0,t2_eda,p3,yb,3.0,
kqasba,hw0,t2_eda,q4,yb,1.0,has a visualization but no question
kqasba,hw0,t2_eda,a4,yb,0.0,
kqasba,hw0,t2_eda,p4,yb,0.0,does not run
kqasba,hw0,t2_eda,q5,yb,1.0,simple question
kqasba,hw0,t2_eda,a5,yb,0.0,
kqasba,hw0,t2_eda,p5,yb,3.0,
kqasba,hw0,t3_ml,s1_train_test,yb,3.0,
kqasba,hw0,t3_ml,s2_build_model,yb,3.0,
kqasba,hw0,t3_ml,s3_calc_performance,yb,3.0,
kqasba,hw0,t3_ml,s4_explain_feat_model,yb,3.0,
kqasba,hw0,t3_ml,s5_best_model,yb,3.0,
kqasba,hw0,t4_data_story,c1_goal,yb,2.0,lacks depth
kqasba,hw0,t4_data_story,c2_stats_ml,yb,2.0,does utilize the graph from previous question but not specific
kqasba,hw0,t4_data_story,c3_viz,yb,0.0,no visualization
kqasba,hw0,t4_data_story,c4_comm,yb,3.0,
kqasba,hw0,t4_data_story,c5_data_limit,yb,3.0,
ramkausr,hw0,t1_data_clean,col1,yb,2.0,does not drop NA values
ramkausr,hw0,t1_data_clean,col2,yb,2.0,does not drop NA values
ramkausr,hw0,t1_data_clean,col3,yb,2.0,does not drop NA values
ramkausr,hw0,t1_data_clean,col4,yb,2.0,does not drop NA values
ramkausr,hw0,t1_data_clean,col5,yb,2.0,does not drop NA values
ramkausr,hw0,t2_eda,q1,yb,2.0,
ramkausr,hw0,t2_eda,a1,yb,1.0,vague answer
ramkausr,hw0,t2_eda,p1,yb,3.0,
ramkausr,hw0,t2_eda,q2,yb,3.0,
ramkausr,hw0,t2_eda,a2,yb,1.0,vague answer
ramkausr,hw0,t2_eda,p2,yb,3.0,
ramkausr,hw0,t2_eda,q3,yb,3.0,
ramkausr,hw0,t2_eda,a3,yb,1.0,vague answer
ramkausr,hw0,t2_eda,p3,yb,2.0,hard to see the graph
ramkausr,hw0,t2_eda,q4,yb,2.0,
ramkausr,hw0,t2_eda,a4,yb,1.0,vague answer
ramkausr,hw0,t2_eda,p4,yb,2.0,
ramkausr,hw0,t2_eda,q5,yb,3.0,
ramkausr,hw0,t2_eda,a5,yb,1.0,vague answer
ramkausr,hw0,t2_eda,p5,yb,2.0,hard to see the graph
ramkausr,hw0,t3_ml,s1_train_test,yb,3.0,
ramkausr,hw0,t3_ml,s2_build_model,yb,3.0,
ramkausr,hw0,t3_ml,s3_calc_performance,yb,3.0,
ramkausr,hw0,t3_ml,s4_explain_feat_model,yb,3.0,
ramkausr,hw0,t3_ml,s5_best_model,yb,3.0,
ramkausr,hw0,t4_data_story,c1_goal,yb,2.0,
ramkausr,hw0,t4_data_story,c2_stats_ml,yb,2.0,
ramkausr,hw0,t4_data_story,c3_viz,yb,3.0,
ramkausr,hw0,t4_data_story,c4_comm,yb,1.0,
ramkausr,hw0,t4_data_story,c5_data_limit,yb,3.0,
krastogi,hw0,t1_data_clean,col1,yb,2.0,does not drop NA values
krastogi,hw0,t1_data_clean,col2,yb,2.0,does not drop NA values
krastogi,hw0,t1_data_clean,col3,yb,2.0,does not drop NA values
krastogi,hw0,t1_data_clean,col4,yb,2.0,does not drop NA values
krastogi,hw0,t1_data_clean,col5,yb,2.0,does not drop NA values
krastogi,hw0,t2_eda,q1,yb,2.0,
krastogi,hw0,t2_eda,a1,yb,1.0,vague answer
krastogi,hw0,t2_eda,p1,yb,3.0,
krastogi,hw0,t2_eda,q2,yb,3.0,
krastogi,hw0,t2_eda,a2,yb,1.0,vague answer
krastogi,hw0,t2_eda,p2,yb,3.0,
krastogi,hw0,t2_eda,q3,yb,3.0,
krastogi,hw0,t2_eda,a3,yb,1.0,vague answer
krastogi,hw0,t2_eda,p3,yb,2.0,hard to see the graph
krastogi,hw0,t2_eda,q4,yb,2.0,
krastogi,hw0,t2_eda,a4,yb,1.0,vague answer
krastogi,hw0,t2_eda,p4,yb,2.0,
krastogi,hw0,t2_eda,q5,yb,3.0,
krastogi,hw0,t2_eda,a5,yb,1.0,vague answer
krastogi,hw0,t2_eda,p5,yb,2.0,hard to see the graph
krastogi,hw0,t3_ml,s1_train_test,yb,3.0,
krastogi,hw0,t3_ml,s2_build_model,yb,3.0,
krastogi,hw0,t3_ml,s3_calc_performance,yb,3.0,
krastogi,hw0,t3_ml,s4_explain_feat_model,yb,3.0,
krastogi,hw0,t3_ml,s5_best_model,yb,3.0,
krastogi,hw0,t4_data_story,c1_goal,yb,2.0,
krastogi,hw0,t4_data_story,c2_stats_ml,yb,2.0,
krastogi,hw0,t4_data_story,c3_viz,yb,3.0,
krastogi,hw0,t4_data_story,c4_comm,yb,1.0,
krastogi,hw0,t4_data_story,c5_data_limit,yb,3.0,
ssagar2,hw0,t1_data_clean,col1,yb,3.0,
ssagar2,hw0,t1_data_clean,col2,yb,0.0,no answer
ssagar2,hw0,t1_data_clean,col3,yb,0.0,
ssagar2,hw0,t1_data_clean,col4,yb,0.0,
ssagar2,hw0,t1_data_clean,col5,yb,0.0,
ssagar2,hw0,t2_eda,q1,yb,0.0,
ssagar2,hw0,t2_eda,a1,yb,0.0,
ssagar2,hw0,t2_eda,p1,yb,0.0,
ssagar2,hw0,t2_eda,q2,yb,0.0,
ssagar2,hw0,t2_eda,a2,yb,0.0,
ssagar2,hw0,t2_eda,p2,yb,0.0,
ssagar2,hw0,t2_eda,q3,yb,0.0,
ssagar2,hw0,t2_eda,a3,yb,0.0,
ssagar2,hw0,t2_eda,p3,yb,0.0,
ssagar2,hw0,t2_eda,q4,yb,0.0,
ssagar2,hw0,t2_eda,a4,yb,0.0,
ssagar2,hw0,t2_eda,p4,yb,0.0,
ssagar2,hw0,t2_eda,q5,yb,0.0,
ssagar2,hw0,t2_eda,a5,yb,0.0,
ssagar2,hw0,t2_eda,p5,yb,0.0,
ssagar2,hw0,t3_ml,s1_train_test,yb,3.0,
ssagar2,hw0,t3_ml,s2_build_model,yb,1.0,does not run
ssagar2,hw0,t3_ml,s3_calc_performance,yb,1.0,effort
ssagar2,hw0,t3_ml,s4_explain_feat_model,yb,1.0,effort
ssagar2,hw0,t3_ml,s5_best_model,yb,0.0,does not provide final answer
ssagar2,hw0,t4_data_story,c1_goal,yb,0.0,
ssagar2,hw0,t4_data_story,c2_stats_ml,yb,0.0,
ssagar2,hw0,t4_data_story,c3_viz,yb,0.0,
ssagar2,hw0,t4_data_story,c4_comm,yb,0.0,
ssagar2,hw0,t4_data_story,c5_data_limit,yb,0.0,
vsakhark,hw0,t1_data_clean,col1,yb,2.0,"effort, but does not run and changes everything to NaN"
vsakhark,hw0,t1_data_clean,col2,yb,0.0,
vsakhark,hw0,t1_data_clean,col3,yb,0.0,
vsakhark,hw0,t1_data_clean,col4,yb,0.0,
vsakhark,hw0,t1_data_clean,col5,yb,0.0,
vsakhark,hw0,t2_eda,q1,yb,2.0,
vsakhark,hw0,t2_eda,a1,yb,2.0,"can put more analysis into it, describing the shape"
vsakhark,hw0,t2_eda,p1,yb,3.0,
vsakhark,hw0,t2_eda,q2,yb,3.0,
vsakhark,hw0,t2_eda,a2,yb,2.0,no correlation coefficient
vsakhark,hw0,t2_eda,p2,yb,3.0,
vsakhark,hw0,t2_eda,q3,yb,3.0,
vsakhark,hw0,t2_eda,a3,yb,1.0,vague answer
vsakhark,hw0,t2_eda,p3,yb,3.0,
vsakhark,hw0,t2_eda,q4,yb,3.0,
vsakhark,hw0,t2_eda,a4,yb,1.0,
vsakhark,hw0,t2_eda,p4,yb,1.0,
vsakhark,hw0,t2_eda,q5,yb,3.0,
vsakhark,hw0,t2_eda,a5,yb,1.0,does not know how to analyze the graph
vsakhark,hw0,t2_eda,p5,yb,1.0,graph does not show anything
vsakhark,hw0,t3_ml,s1_train_test,yb,3.0,
vsakhark,hw0,t3_ml,s2_build_model,yb,3.0,
vsakhark,hw0,t3_ml,s3_calc_performance,yb,3.0,
vsakhark,hw0,t3_ml,s4_explain_feat_model,yb,3.0,
vsakhark,hw0,t3_ml,s5_best_model,yb,3.0,
vsakhark,hw0,t4_data_story,c1_goal,yb,0.0,
vsakhark,hw0,t4_data_story,c2_stats_ml,yb,0.0,
vsakhark,hw0,t4_data_story,c3_viz,yb,0.0,
vsakhark,hw0,t4_data_story,c4_comm,yb,0.0,
vsakhark,hw0,t4_data_story,c5_data_limit,yb,0.0,
agshaik,hw0,t1_data_clean,col1,yb,2.0,does not drop NaN
agshaik,hw0,t1_data_clean,col2,yb,2.0,does not drop NaN
agshaik,hw0,t1_data_clean,col3,yb,2.0,does not drop NaN
agshaik,hw0,t1_data_clean,col4,yb,0.0,
agshaik,hw0,t1_data_clean,col5,yb,0.0,
agshaik,hw0,t2_eda,q1,yb,3.0,advanced question
agshaik,hw0,t2_eda,a1,yb,1.0,does not answer the question
agshaik,hw0,t2_eda,p1,yb,1.0,"the graph does not show anything, explaining why the insight does not even address the graph"
agshaik,hw0,t2_eda,q2,yb,0.0,no question
agshaik,hw0,t2_eda,a2,yb,0.0,no answer
agshaik,hw0,t2_eda,p2,yb,0.0,copy paste from question 3
agshaik,hw0,t2_eda,q3,yb,2.0,intermediate question
agshaik,hw0,t2_eda,a3,yb,1.0,does not answer the question
agshaik,hw0,t2_eda,p3,yb,3.0,
agshaik,hw0,t2_eda,q4,yb,3.0,advanced question
agshaik,hw0,t2_eda,a4,yb,1.0,does not answer the question
agshaik,hw0,t2_eda,p4,yb,3.0,
agshaik,hw0,t2_eda,q5,yb,0.0,
agshaik,hw0,t2_eda,a5,yb,0.0,
agshaik,hw0,t2_eda,p5,yb,0.0,
agshaik,hw0,t3_ml,s1_train_test,yb,3.0,
agshaik,hw0,t3_ml,s2_build_model,yb,3.0,
agshaik,hw0,t3_ml,s3_calc_performance,yb,3.0,
agshaik,hw0,t3_ml,s4_explain_feat_model,yb,3.0,
agshaik,hw0,t3_ml,s5_best_model,yb,3.0,
agshaik,hw0,t4_data_story,c1_goal,yb,0.0,
agshaik,hw0,t4_data_story,c2_stats_ml,yb,0.0,
agshaik,hw0,t4_data_story,c3_viz,yb,0.0,
agshaik,hw0,t4_data_story,c4_comm,yb,0.0,
agshaik,hw0,t4_data_story,c5_data_limit,yb,0.0,
naxs,hw0,t1_data_clean,col1,yb,2.0,does not drop NaN
naxs,hw0,t1_data_clean,col2,yb,2.0,does not drop NaN
naxs,hw0,t1_data_clean,col3,yb,0.0,entirely changed the entire dataframe by defining it to something else
naxs,hw0,t1_data_clean,col4,yb,0.0,"says mileage, and tries to clean price (which was last column)"
naxs,hw0,t1_data_clean,col5,yb,0.0,says VIN# column is not found because they defined the entire dataframe to something else
naxs,hw0,t2_eda,q1,yb,2.0,intermediate question
naxs,hw0,t2_eda,a1,yb,1.0,does not answer the question
naxs,hw0,t2_eda,p1,yb,1.0,does not use cleaned dataset and keeps using the wrong dataframe that they defined incorrectly
naxs,hw0,t2_eda,q2,yb,3.0,advanced question
naxs,hw0,t2_eda,a2,yb,1.0,does not answer the question
naxs,hw0,t2_eda,p2,yb,1.0,does not run
naxs,hw0,t2_eda,q3,yb,3.0,advanced question
naxs,hw0,t2_eda,a3,yb,1.0,does not answer the question
naxs,hw0,t2_eda,p3,yb,1.0,does not run
naxs,hw0,t2_eda,q4,yb,1.0,simple question
naxs,hw0,t2_eda,a4,yb,1.0,does not answer the question
naxs,hw0,t2_eda,p4,yb,1.0,does not run
naxs,hw0,t2_eda,q5,yb,2.0,intermediate question
naxs,hw0,t2_eda,a5,yb,1.0,does not answer the question
naxs,hw0,t2_eda,p5,yb,1.0,uses wrong dataset
naxs,hw0,t3_ml,s1_train_test,yb,3.0,
naxs,hw0,t3_ml,s2_build_model,yb,0.0,does not run
naxs,hw0,t3_ml,s3_calc_performance,yb,0.0,
naxs,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
naxs,hw0,t3_ml,s5_best_model,yb,0.0,
naxs,hw0,t4_data_story,c1_goal,yb,2.0,lacks depth
naxs,hw0,t4_data_story,c2_stats_ml,yb,1.0,weak explanation
naxs,hw0,t4_data_story,c3_viz,yb,0.0,no visualization
naxs,hw0,t4_data_story,c4_comm,yb,2.0,lacks flow
naxs,hw0,t4_data_story,c5_data_limit,yb,2.0,lacks depth
ankitshu,hw0,t1_data_clean,col1,yb,2.0,does not drop NaN
ankitshu,hw0,t1_data_clean,col2,yb,0.0,
ankitshu,hw0,t1_data_clean,col3,yb,0.0,
ankitshu,hw0,t1_data_clean,col4,yb,0.0,
ankitshu,hw0,t1_data_clean,col5,yb,0.0,
ankitshu,hw0,t2_eda,q1,yb,0.0,
ankitshu,hw0,t2_eda,a1,yb,0.0,
ankitshu,hw0,t2_eda,p1,yb,0.0,
ankitshu,hw0,t2_eda,q2,yb,0.0,
ankitshu,hw0,t2_eda,a2,yb,0.0,
ankitshu,hw0,t2_eda,p2,yb,0.0,
ankitshu,hw0,t2_eda,q3,yb,0.0,
ankitshu,hw0,t2_eda,a3,yb,0.0,
ankitshu,hw0,t2_eda,p3,yb,0.0,
ankitshu,hw0,t2_eda,q4,yb,0.0,
ankitshu,hw0,t2_eda,a4,yb,0.0,
ankitshu,hw0,t2_eda,p4,yb,0.0,
ankitshu,hw0,t2_eda,q5,yb,0.0,
ankitshu,hw0,t2_eda,a5,yb,0.0,
ankitshu,hw0,t2_eda,p5,yb,0.0,
ankitshu,hw0,t3_ml,s1_train_test,yb,3.0,
ankitshu,hw0,t3_ml,s2_build_model,yb,3.0,
ankitshu,hw0,t3_ml,s3_calc_performance,yb,0.0,"ultimately does not run, so cannot see the results"
ankitshu,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
ankitshu,hw0,t3_ml,s5_best_model,yb,0.0,
ankitshu,hw0,t4_data_story,c1_goal,yb,0.0,
ankitshu,hw0,t4_data_story,c2_stats_ml,yb,0.0,
ankitshu,hw0,t4_data_story,c3_viz,yb,0.0,
ankitshu,hw0,t4_data_story,c4_comm,yb,0.0,
ankitshu,hw0,t4_data_story,c5_data_limit,yb,0.0,
csoska,hw0,t1_data_clean,col1,yb,0.0,
csoska,hw0,t1_data_clean,col2,yb,0.0,
csoska,hw0,t1_data_clean,col3,yb,0.0,
csoska,hw0,t1_data_clean,col4,yb,0.0,
csoska,hw0,t1_data_clean,col5,yb,0.0,
csoska,hw0,t2_eda,q1,yb,3.0,advanced question
csoska,hw0,t2_eda,a1,yb,0.0,
csoska,hw0,t2_eda,p1,yb,0.0,does not run
csoska,hw0,t2_eda,q2,yb,0.0,
csoska,hw0,t2_eda,a2,yb,0.0,
csoska,hw0,t2_eda,p2,yb,0.0,
csoska,hw0,t2_eda,q3,yb,0.0,
csoska,hw0,t2_eda,a3,yb,0.0,
csoska,hw0,t2_eda,p3,yb,0.0,
csoska,hw0,t2_eda,q4,yb,0.0,
csoska,hw0,t2_eda,a4,yb,0.0,
csoska,hw0,t2_eda,p4,yb,0.0,
csoska,hw0,t2_eda,q5,yb,0.0,
csoska,hw0,t2_eda,a5,yb,0.0,
csoska,hw0,t2_eda,p5,yb,0.0,
csoska,hw0,t3_ml,s1_train_test,yb,0.0,"does something, but only just thinking of which feature to use"
csoska,hw0,t3_ml,s2_build_model,yb,0.0,
csoska,hw0,t3_ml,s3_calc_performance,yb,0.0,
csoska,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
csoska,hw0,t3_ml,s5_best_model,yb,0.0,
csoska,hw0,t4_data_story,c1_goal,yb,0.0,
csoska,hw0,t4_data_story,c2_stats_ml,yb,0.0,
csoska,hw0,t4_data_story,c3_viz,yb,0.0,
csoska,hw0,t4_data_story,c4_comm,yb,0.0,
csoska,hw0,t4_data_story,c5_data_limit,yb,0.0,
ashwin2,hw0,t1_data_clean,col1,yb,2.0,does not remove NaN
ashwin2,hw0,t1_data_clean,col2,yb,0.0,"does not actually clean, just checks for unique values"
ashwin2,hw0,t1_data_clean,col3,yb,0.0,
ashwin2,hw0,t1_data_clean,col4,yb,0.0,
ashwin2,hw0,t1_data_clean,col5,yb,0.0,
ashwin2,hw0,t2_eda,q1,yb,2.0,Intermediate question
ashwin2,hw0,t2_eda,a1,yb,0.0,
ashwin2,hw0,t2_eda,p1,yb,0.0,
ashwin2,hw0,t2_eda,q2,yb,0.0,
ashwin2,hw0,t2_eda,a2,yb,0.0,
ashwin2,hw0,t2_eda,p2,yb,0.0,
ashwin2,hw0,t2_eda,q3,yb,0.0,
ashwin2,hw0,t2_eda,a3,yb,0.0,
ashwin2,hw0,t2_eda,p3,yb,0.0,
ashwin2,hw0,t2_eda,q4,yb,0.0,
ashwin2,hw0,t2_eda,a4,yb,0.0,
ashwin2,hw0,t2_eda,p4,yb,0.0,
ashwin2,hw0,t2_eda,q5,yb,0.0,
ashwin2,hw0,t2_eda,a5,yb,0.0,
ashwin2,hw0,t2_eda,p5,yb,0.0,
ashwin2,hw0,t3_ml,s1_train_test,yb,0.0,"does something, but it's not ML (he copied the data analysis part into ML part for some reason)"
ashwin2,hw0,t3_ml,s2_build_model,yb,0.0,
ashwin2,hw0,t3_ml,s3_calc_performance,yb,0.0,
ashwin2,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
ashwin2,hw0,t3_ml,s5_best_model,yb,0.0,
ashwin2,hw0,t4_data_story,c1_goal,yb,0.0,
ashwin2,hw0,t4_data_story,c2_stats_ml,yb,0.0,
ashwin2,hw0,t4_data_story,c3_viz,yb,0.0,
ashwin2,hw0,t4_data_story,c4_comm,yb,0.0,
ashwin2,hw0,t4_data_story,c5_data_limit,yb,0.0,
yiwentan,hw0,t1_data_clean,col1,yb,0.0,"does not actually clean, just checks for unique values"
yiwentan,hw0,t1_data_clean,col2,yb,3.0,
yiwentan,hw0,t1_data_clean,col3,yb,0.0,does not run
yiwentan,hw0,t1_data_clean,col4,yb,0.0,
yiwentan,hw0,t1_data_clean,col5,yb,0.0,
yiwentan,hw0,t2_eda,q1,yb,1.0,simple question; there's only one car model in here
yiwentan,hw0,t2_eda,a1,yb,0.0,
yiwentan,hw0,t2_eda,p1,yb,3.0,
yiwentan,hw0,t2_eda,q2,yb,3.0,advanced question
yiwentan,hw0,t2_eda,a2,yb,0.0,
yiwentan,hw0,t2_eda,p2,yb,1.0,not a solid visualization
yiwentan,hw0,t2_eda,q3,yb,1.0,simple question
yiwentan,hw0,t2_eda,a3,yb,0.0,
yiwentan,hw0,t2_eda,p3,yb,3.0,
yiwentan,hw0,t2_eda,q4,yb,0.0,
yiwentan,hw0,t2_eda,a4,yb,0.0,
yiwentan,hw0,t2_eda,p4,yb,0.0,
yiwentan,hw0,t2_eda,q5,yb,0.0,
yiwentan,hw0,t2_eda,a5,yb,0.0,
yiwentan,hw0,t2_eda,p5,yb,0.0,
yiwentan,hw0,t3_ml,s1_train_test,yb,0.0,k cluster
yiwentan,hw0,t3_ml,s2_build_model,yb,0.0,
yiwentan,hw0,t3_ml,s3_calc_performance,yb,0.0,
yiwentan,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
yiwentan,hw0,t3_ml,s5_best_model,yb,0.0,
yiwentan,hw0,t4_data_story,c1_goal,yb,0.0,
yiwentan,hw0,t4_data_story,c2_stats_ml,yb,0.0,
yiwentan,hw0,t4_data_story,c3_viz,yb,0.0,
yiwentan,hw0,t4_data_story,c4_comm,yb,0.0,
yiwentan,hw0,t4_data_story,c5_data_limit,yb,0.0,
dtayal,hw0,t1_data_clean,col1,yb,0.0,"does not actually clean, just checks for unique values"
dtayal,hw0,t1_data_clean,col2,yb,0.0,"does not actually clean, just checks for unique values"
dtayal,hw0,t1_data_clean,col3,yb,0.0,
dtayal,hw0,t1_data_clean,col4,yb,0.0,
dtayal,hw0,t1_data_clean,col5,yb,0.0,
dtayal,hw0,t2_eda,q1,yb,2.0,intermediate question
dtayal,hw0,t2_eda,a1,yb,0.0,
dtayal,hw0,t2_eda,p1,yb,0.0,
dtayal,hw0,t2_eda,q2,yb,1.0,simple question; there's only one car model in here
dtayal,hw0,t2_eda,a2,yb,0.0,
dtayal,hw0,t2_eda,p2,yb,0.0,
dtayal,hw0,t2_eda,q3,yb,0.0,
dtayal,hw0,t2_eda,a3,yb,0.0,
dtayal,hw0,t2_eda,p3,yb,0.0,
dtayal,hw0,t2_eda,q4,yb,0.0,
dtayal,hw0,t2_eda,a4,yb,0.0,
dtayal,hw0,t2_eda,p4,yb,0.0,
dtayal,hw0,t2_eda,q5,yb,0.0,
dtayal,hw0,t2_eda,a5,yb,0.0,
dtayal,hw0,t2_eda,p5,yb,0.0,
dtayal,hw0,t3_ml,s1_train_test,yb,3.0,
dtayal,hw0,t3_ml,s2_build_model,yb,3.0,
dtayal,hw0,t3_ml,s3_calc_performance,yb,0.0,ultimately does not run; cannot see the metrics so cannot move forward
dtayal,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
dtayal,hw0,t3_ml,s5_best_model,yb,0.0,
dtayal,hw0,t4_data_story,c1_goal,yb,0.0,does not do this part
dtayal,hw0,t4_data_story,c2_stats_ml,yb,0.0,does not do this part
dtayal,hw0,t4_data_story,c3_viz,yb,2.0,
dtayal,hw0,t4_data_story,c4_comm,yb,1.0,
dtayal,hw0,t4_data_story,c5_data_limit,yb,1.0,
etripath,hw0,t1_data_clean,col1,yb,3.0,
etripath,hw0,t1_data_clean,col2,yb,3.0,
etripath,hw0,t1_data_clean,col3,yb,0.0,
etripath,hw0,t1_data_clean,col4,yb,2.0,does not change to integer
etripath,hw0,t1_data_clean,col5,yb,0.0,
etripath,hw0,t2_eda,q1,yb,1.0,simple question
etripath,hw0,t2_eda,a1,yb,1.0,"vague answer, only addresses the mean"
etripath,hw0,t2_eda,p1,yb,3.0,
etripath,hw0,t2_eda,q2,yb,1.0,simple question
etripath,hw0,t2_eda,a2,yb,1.0,should have known during dataset cleaning stage
etripath,hw0,t2_eda,p2,yb,1.0,
etripath,hw0,t2_eda,q3,yb,1.0,simple question
etripath,hw0,t2_eda,a3,yb,1.0,should have known during dataset cleaning stage
etripath,hw0,t2_eda,p3,yb,1.0,
etripath,hw0,t2_eda,q4,yb,0.0,
etripath,hw0,t2_eda,a4,yb,0.0,
etripath,hw0,t2_eda,p4,yb,0.0,
etripath,hw0,t2_eda,q5,yb,0.0,
etripath,hw0,t2_eda,a5,yb,0.0,
etripath,hw0,t2_eda,p5,yb,0.0,
etripath,hw0,t3_ml,s1_train_test,yb,3.0,
etripath,hw0,t3_ml,s2_build_model,yb,3.0,
etripath,hw0,t3_ml,s3_calc_performance,yb,3.0,
etripath,hw0,t3_ml,s4_explain_feat_model,yb,3.0,
etripath,hw0,t3_ml,s5_best_model,yb,3.0,
etripath,hw0,t4_data_story,c1_goal,yb,1.0,
etripath,hw0,t4_data_story,c2_stats_ml,yb,1.0,just explains what ML topics are
etripath,hw0,t4_data_story,c3_viz,yb,1.0,don't understand what this visualization is supposed to prove
etripath,hw0,t4_data_story,c4_comm,yb,1.0,
etripath,hw0,t4_data_story,c5_data_limit,yb,1.0,gives improvements for the code and not relating to dataset at all
kviknesh,hw0,t1_data_clean,col1,yb,0.0,only counts how many duplicates
kviknesh,hw0,t1_data_clean,col2,yb,0.0,"only a function, but does not actually clean"
kviknesh,hw0,t1_data_clean,col3,yb,0.0,
kviknesh,hw0,t1_data_clean,col4,yb,0.0,
kviknesh,hw0,t1_data_clean,col5,yb,0.0,
kviknesh,hw0,t2_eda,q1,yb,3.0,advanced question
kviknesh,hw0,t2_eda,a1,yb,3.0,
kviknesh,hw0,t2_eda,p1,yb,3.0,
kviknesh,hw0,t2_eda,q2,yb,0.0,
kviknesh,hw0,t2_eda,a2,yb,0.0,
kviknesh,hw0,t2_eda,p2,yb,0.0,
kviknesh,hw0,t2_eda,q3,yb,0.0,
kviknesh,hw0,t2_eda,a3,yb,0.0,
kviknesh,hw0,t2_eda,p3,yb,0.0,
kviknesh,hw0,t2_eda,q4,yb,0.0,
kviknesh,hw0,t2_eda,a4,yb,0.0,
kviknesh,hw0,t2_eda,p4,yb,0.0,
kviknesh,hw0,t2_eda,q5,yb,0.0,
kviknesh,hw0,t2_eda,a5,yb,0.0,
kviknesh,hw0,t2_eda,p5,yb,0.0,
kviknesh,hw0,t3_ml,s1_train_test,yb,0.0,
kviknesh,hw0,t3_ml,s2_build_model,yb,0.0,
kviknesh,hw0,t3_ml,s3_calc_performance,yb,0.0,
kviknesh,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
kviknesh,hw0,t3_ml,s5_best_model,yb,0.0,
kviknesh,hw0,t4_data_story,c1_goal,yb,0.0,
kviknesh,hw0,t4_data_story,c2_stats_ml,yb,0.0,
kviknesh,hw0,t4_data_story,c3_viz,yb,0.0,
kviknesh,hw0,t4_data_story,c4_comm,yb,0.0,
kviknesh,hw0,t4_data_story,c5_data_limit,yb,0.0,
nivedity,hw0,t1_data_clean,col1,yb,2.0,does not drop NaN
nivedity,hw0,t1_data_clean,col2,yb,2.0,does not drop NaN
nivedity,hw0,t1_data_clean,col3,yb,2.0,does not change to integer
nivedity,hw0,t1_data_clean,col4,yb,0.0,
nivedity,hw0,t1_data_clean,col5,yb,0.0,
nivedity,hw0,t2_eda,q1,yb,1.0,simple question
nivedity,hw0,t2_eda,a1,yb,0.0,
nivedity,hw0,t2_eda,p1,yb,3.0,
nivedity,hw0,t2_eda,q2,yb,2.0,intermediate question
nivedity,hw0,t2_eda,a2,yb,0.0,
nivedity,hw0,t2_eda,p2,yb,3.0,
nivedity,hw0,t2_eda,q3,yb,2.0,intermediate question
nivedity,hw0,t2_eda,a3,yb,0.0,
nivedity,hw0,t2_eda,p3,yb,1.0,does not really capture the question
nivedity,hw0,t2_eda,q4,yb,3.0,advanced question
nivedity,hw0,t2_eda,a4,yb,0.0,
nivedity,hw0,t2_eda,p4,yb,3.0,
nivedity,hw0,t2_eda,q5,yb,0.0,
nivedity,hw0,t2_eda,a5,yb,0.0,
nivedity,hw0,t2_eda,p5,yb,0.0,
nivedity,hw0,t3_ml,s1_train_test,yb,0.0,
nivedity,hw0,t3_ml,s2_build_model,yb,0.0,
nivedity,hw0,t3_ml,s3_calc_performance,yb,0.0,
nivedity,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
nivedity,hw0,t3_ml,s5_best_model,yb,0.0,
nivedity,hw0,t4_data_story,c1_goal,yb,0.0,
nivedity,hw0,t4_data_story,c2_stats_ml,yb,0.0,
nivedity,hw0,t4_data_story,c3_viz,yb,0.0,
nivedity,hw0,t4_data_story,c4_comm,yb,0.0,
nivedity,hw0,t4_data_story,c5_data_limit,yb,0.0,
hyanaman,hw0,t1_data_clean,col1,yb,3.0,
hyanaman,hw0,t1_data_clean,col2,yb,3.0,
hyanaman,hw0,t1_data_clean,col3,yb,3.0,
hyanaman,hw0,t1_data_clean,col4,yb,2.0,does not change to int
hyanaman,hw0,t1_data_clean,col5,yb,0.0,
hyanaman,hw0,t2_eda,q1,yb,2.0,intermediate
hyanaman,hw0,t2_eda,a1,yb,0.0,
hyanaman,hw0,t2_eda,p1,yb,3.0,
hyanaman,hw0,t2_eda,q2,yb,0.0,
hyanaman,hw0,t2_eda,a2,yb,0.0,
hyanaman,hw0,t2_eda,p2,yb,0.0,
hyanaman,hw0,t2_eda,q3,yb,0.0,
hyanaman,hw0,t2_eda,a3,yb,0.0,
hyanaman,hw0,t2_eda,p3,yb,0.0,
hyanaman,hw0,t2_eda,q4,yb,0.0,
hyanaman,hw0,t2_eda,a4,yb,0.0,
hyanaman,hw0,t2_eda,p4,yb,0.0,
hyanaman,hw0,t2_eda,q5,yb,0.0,
hyanaman,hw0,t2_eda,a5,yb,0.0,
hyanaman,hw0,t2_eda,p5,yb,0.0,
hyanaman,hw0,t3_ml,s1_train_test,yb,0.0,
hyanaman,hw0,t3_ml,s2_build_model,yb,0.0,
hyanaman,hw0,t3_ml,s3_calc_performance,yb,0.0,
hyanaman,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
hyanaman,hw0,t3_ml,s5_best_model,yb,0.0,
hyanaman,hw0,t4_data_story,c1_goal,yb,1.0,
hyanaman,hw0,t4_data_story,c2_stats_ml,yb,0.0,
hyanaman,hw0,t4_data_story,c3_viz,yb,0.0,
hyanaman,hw0,t4_data_story,c4_comm,yb,1.0,
hyanaman,hw0,t4_data_story,c5_data_limit,yb,3.0,
hengyuez,hw0,t1_data_clean,col1,yb,3.0,
hengyuez,hw0,t1_data_clean,col2,yb,3.0,
hengyuez,hw0,t1_data_clean,col3,yb,1.0,
hengyuez,hw0,t1_data_clean,col4,yb,3.0,
hengyuez,hw0,t1_data_clean,col5,yb,3.0,
hengyuez,hw0,t2_eda,q1,yb,0.0,
hengyuez,hw0,t2_eda,a1,yb,0.0,
hengyuez,hw0,t2_eda,p1,yb,0.0,
hengyuez,hw0,t2_eda,q2,yb,0.0,
hengyuez,hw0,t2_eda,a2,yb,0.0,
hengyuez,hw0,t2_eda,p2,yb,0.0,
hengyuez,hw0,t2_eda,q3,yb,0.0,
hengyuez,hw0,t2_eda,a3,yb,0.0,
hengyuez,hw0,t2_eda,p3,yb,0.0,
hengyuez,hw0,t2_eda,q4,yb,0.0,
hengyuez,hw0,t2_eda,a4,yb,0.0,
hengyuez,hw0,t2_eda,p4,yb,0.0,
hengyuez,hw0,t2_eda,q5,yb,0.0,
hengyuez,hw0,t2_eda,a5,yb,0.0,
hengyuez,hw0,t2_eda,p5,yb,0.0,
hengyuez,hw0,t3_ml,s1_train_test,yb,3.0,
hengyuez,hw0,t3_ml,s2_build_model,yb,3.0,
hengyuez,hw0,t3_ml,s3_calc_performance,yb,0.0,
hengyuez,hw0,t3_ml,s4_explain_feat_model,yb,0.0,
hengyuez,hw0,t3_ml,s5_best_model,yb,0.0,
hengyuez,hw0,t4_data_story,c1_goal,yb,0.0,
hengyuez,hw0,t4_data_story,c2_stats_ml,yb,0.0,
hengyuez,hw0,t4_data_story,c3_viz,yb,0.0,
hengyuez,hw0,t4_data_story,c4_comm,yb,1.0,"made visualization, but bad"
hengyuez,hw0,t4_data_story,c5_data_limit,yb,0.0,
