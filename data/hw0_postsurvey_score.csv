andrewid,perceived_success_data_cleaning_score,perceived_success_eda_score,perceived_success_ml_score,perceived_success_data_storytelling_score,perceived_difficulty_data_cleaning_score,perceived_difficulty_eda_score,perceived_difficulty_ml_score,perceived_difficulty_data_storytelling_score,perceived_effort_data_cleaning_score,perceived_effort_eda_score,perceived_effort_ml_score,perceived_effort_data_storytelling_score,perceived_stress_data_cleaning_score,perceived_stress_eda_score,perceived_stress_ml_score,perceived_stress_data_storytelling_score,perceived_engagement_data_cleaning_score,perceived_engagement_eda_score,perceived_engagement_ml_score,perceived_engagement_data_storytelling_score,helpseek_data_cleaning_score,helpseek_eda_score,helpseek_ml_score,helpseek_data_storytelling_score,confidence_data_cleaning_score,confidence_eda_score,confidence_ml_score,confidence_data_storytelling_score,task_used_llm
rupalc,3,3,3,3,2,3,5,4,4,4,4,4,4,4,4,4,3,3,3,3,2,2,2,2,3,3,3,3,"['Part 2. Exploratory data analysis', 'Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
ishengc,5,3,2,2,1,2,4,4,1,4,5,5,2,5,5,5,4,4,3,4,1,1,1,1,5,5,5,5,['Part 4. Data-driven storytelling']
barora,2,2,2,2,5,5,5,5,5,5,5,5,5,5,5,5,3,2,2,2,4,3,4,2,1,1,1,1,"['Part 2. Exploratory data analysis', 'Part 3. Machine learning']"
gracelia,2,2,3,2,2,2,3,2,2,2,3,2,1,1,1,1,4,4,4,4,1,1,1,1,2,2,5,2,['Part 3. Machine learning']
ankitshu,3,3,3,3,5,5,5,5,4,4,4,4,2,2,2,2,3,3,3,3,2,2,2,2,3,3,3,3,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
anshp,3,2,2,3,2,3,5,2,4,4,5,3,4,4,4,1,5,5,5,3,2,1,1,1,4,3,2,3,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning']"
hyanaman,2,3,4,3,3,4,2,3,3,3,2,3,3,5,2,2,3,3,2,2,3,3,3,3,3,3,3,3,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
alexding,2,2,4,4,4,4,3,3,3,3,4,4,4,4,4,3,3,3,4,4,4,4,3,3,2,2,3,3,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
rkkim,1,1,1,1,5,5,5,5,5,5,5,5,5,5,5,5,4,5,3,3,1,1,2,1,1,1,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
sohammon,3,3,4,3,3,3,5,4,4,4,4,4,3,3,4,3,3,3,4,3,3,3,4,3,3,3,4,3,['Part 3. Machine learning']
ssagar2,3,2,4,1,3,4,4,4,2,3,4,4,3,4,2,2,3,2,4,5,4,4,4,2,4,2,4,3,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
kviknesh,3,2,1,1,2,2,5,5,2,2,5,5,2,2,4,4,4,4,4,4,2,2,5,5,2,2,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
sjashnan,1,2,2,2,3,2,4,2,3,2,1,1,2,2,1,1,2,3,2,2,2,1,1,1,1,2,2,3,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
wenyili,4,4,1,1,1,1,4,4,3,3,4,4,2,2,2,2,3,3,3,3,2,2,4,4,3,3,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
vsakhark,2,2,2,2,4,5,5,4,4,4,4,4,5,5,4,4,3,3,3,3,4,5,4,3,1,1,2,2,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning']"
hpasha,1,1,1,1,3,5,5,5,4,5,5,5,4,5,5,5,5,5,5,5,4,4,4,4,1,1,1,1,['Part 3. Machine learning']
tsohani,1,2,3,1,4,4,2,5,3,3,3,3,3,3,3,3,4,4,4,4,2,2,1,1,1,1,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
dtayal,1,1,1,2,4,5,5,4,5,5,5,5,4,5,5,3,4,4,4,4,2,1,1,1,1,1,1,1,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
sijiaf,3,3,2,2,3,1,3,3,4,2,3,3,4,1,3,3,3,3,4,4,1,1,1,1,4,4,3,3,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
usa,3,3,3,3,2,2,2,2,2,2,2,2,2,2,2,2,3,3,3,3,3,2,3,2,3,3,3,3,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
yiwentan,1,1,1,1,5,5,4,5,5,5,4,5,5,5,4,5,1,3,5,3,1,1,1,1,1,1,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning']"
achavess,3,3,1,1,2,3,4,4,3,3,4,4,3,3,4,4,3,3,4,4,1,1,2,2,3,3,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
elianah,2,2,1,1,5,5,5,5,3,3,4,4,4,4,5,5,2,2,2,2,1,1,1,1,1,1,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
mnandiga,3,3,2,2,4,4,5,4,4,4,5,4,2,2,3,2,4,4,4,4,1,1,2,1,3,3,1,2,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
aafonja,2,2,1,1,5,5,5,5,5,5,5,5,2,2,2,2,3,3,3,3,1,2,2,1,2,2,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis', 'Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
charlenl,1,1,1,1,5,5,5,5,5,5,5,5,5,5,5,5,1,1,1,1,5,5,5,5,1,1,1,1,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
gsumukh,3,3,3,3,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,2,3,2,1,1,1,1,1,['Part 1. Data cleaning']
mlal,3,3,1,1,3,3,5,5,2,3,5,5,3,4,5,5,3,3,2,1,1,2,4,4,3,1,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
ramkausr,4,4,4,4,2,3,3,3,3,3,2,2,3,3,3,3,4,4,4,4,1,1,1,1,4,4,4,4,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
kqasba,1,1,1,1,2,2,5,5,2,2,5,5,2,2,5,5,1,1,1,1,4,4,4,4,1,1,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
csoska,1,2,1,1,3,3,4,3,3,3,3,3,3,3,3,3,4,4,5,4,3,2,1,1,1,1,1,1,"['Part 2. Exploratory data analysis', 'Part 3. Machine learning']"
nmedaram,3,3,3,3,3,3,3,3,4,4,4,4,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
nivedity,2,3,1,1,3,2,4,4,4,2,4,4,4,2,3,3,4,4,4,4,2,2,2,2,1,1,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
ashwin2,2,1,3,1,4,5,4,4,4,5,5,5,5,5,4,4,3,2,3,3,3,2,2,2,3,1,2,1,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
elakra,3,3,1,1,3,3,5,5,5,5,5,5,5,5,5,5,4,4,3,3,3,3,3,3,3,3,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
somyameh,2,2,2,2,2,3,3,3,3,3,3,3,3,2,1,1,2,2,2,2,2,2,2,2,2,2,2,2,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
abhimava,3,2,4,4,3,2,4,4,4,3,3,3,2,2,2,2,4,4,4,4,1,1,1,1,4,2,4,4,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
radhikaj,2,2,2,2,4,4,5,5,5,5,5,5,3,3,3,3,5,5,5,5,4,3,2,2,2,2,1,1,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
etripath,2,2,3,3,3,4,4,4,3,4,1,1,3,3,1,1,5,5,5,5,1,1,1,1,1,1,2,2,"['Part 3. Machine learning', 'Part 4. Data-driven storytelling']"
suhailk,3,3,3,3,2,2,3,3,2,2,3,3,2,1,3,3,2,2,3,2,1,1,1,1,3,4,4,3,"['Part 1. Data cleaning', 'Part 2. Exploratory data analysis']"
