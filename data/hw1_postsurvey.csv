<PERSON><PERSON><PERSON>,Email Address,Name,used_llm,perceived_difficulty_data_cleaning,perceived_effort_data_cleaning,perceived_stress_data_cleaning,perceived_engagement_data_cleaning,perceived_success_data_cleaning,confidence_data_cleaning,helpseek_data_cleaning,challenge_used_llm,llm_use,llm_helpful,help_needed,help_peer,comment,andrewid,perceived_success_data_cleaning_score,perceived_difficulty_data_cleaning_score,perceived_effort_data_cleaning_score,perceived_stress_data_cleaning_score,perceived_engagement_data_cleaning_score,helpseek_data_cleaning_score,confidence_data_cleaning_score
3/20/2025 9:53:43,<EMAIL>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>'s innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,getting the correct code,to get the code,yes as i needec help with the code,if <PERSON><PERSON><PERSON> could suggest ideas for cleaning the data,NA,,rupalc,4,3,3,2,3,1,4
3/21/2025 17:11:56,yi<PERSON><PERSON>@andrew.cmu.edu,<PERSON>,<PERSON><PERSON>'s innate GenAI (Gemini),Moderately difficult,Very effortful,Moderately frustrating,Not interesting at all,Moderately successful/ satisfied,Moderately confident,Not at all,I still cannot convert the dates that were in string into a date datatype.,Ask it to write me the python code that I needed to do the data cleaning.,"Yes. Even though I know abit of python coding, I don't remember the exact syntax for the specific data cleaning tasks so the GenAI was quite helpful in providing the correct python syntax. ",I could not change the object datatype to string. Asking Gemini did not really help so I still cannot understand why I couldn't change the relevant object to string datatype.,I asked Alex the above question and he said it is because the values probably contain a mix of datatypes so I cannot force it to change it into a string datatype for these columns. ,"Perhaps to help the non-technical MSPM folks, there could be one example given to show what is needed to complete the task. The recorded demo by Prof Wu helped though so thanks!",yiwentan,3,3,4,3,1,1,3
3/22/2025 14:57:06,<EMAIL>,Angelica Chaves,Colab's innate GenAI (Gemini),Slightly difficult,Moderately effortful,Slightly frustrating,Moderately interesting,Very successful/ satisfied,Moderately confident,Not at all,"When using chat Gemini panel, i have to remember to be very specific during prompting to include the exact df and column key to get a more direct code ","to provide guidence on what type of data cleaning steps can i do, for troubleshooting errors and code generation","Yes, because it provide the technical syntax that its new to me","Tips on how to be successful with prompting, i understand there is a specific structure but it would be great to understand how to provide instructions more effectively (if needed)","i didn't ask my peers or classmates, just use google or GenAI",,achavess,4,2,3,2,3,1,3
3/22/2025 18:08:22,<EMAIL>,grace,Colab's innate GenAI (Gemini),Not difficult at all,I do not need to work hard at all,Not frustrating at all,Not interesting at all,Not successful/ satisfied at all,Not confident at all,Not at all,na,ask questions about syntax,yes it gave good explanations,na,na,,gracelia,1,1,1,1,1,1,1
3/22/2025 20:12:36,<EMAIL>,Sumukh,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Not frustrating at all,Moderately interesting,Extremely successful/ satisfied,Very confident,Very much,To be able to define clearly what your question is and get the answer the way you want it.,Did well if you are clear with your instructions,"Yes, clear and quick clarifications",I think I did well with what i had,Understanding each code and general framework,,gsumukh,5,3,3,1,3,4,4
3/22/2025 20:58:00,<EMAIL>,Charlene Lin,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Slightly interesting,Moderately successful/ satisfied,Moderately confident,All the time,To understand the high-level concepts of how to approach it. You don't know what you don't know.,Ask it to generate codes and for clarifying some concepts.,Yes. I won't be able to write all the code from the ground up within hours. ,"Understand the common steps toward a certain situation. For example, for checking if there are identical duplicate entries, there are some common ways to do it fast.","The overall concept and steps of how to go about cleaning a dataset. They explained to me how they would do with examples, and I asked follow up questions to better imagine how I would do with the task.",,charlenl,3,3,3,3,2,5,3
3/23/2025 0:51:31,<EMAIL>,radhika ,Colab's innate GenAI (Gemini),Very difficult,I need to work extremely hard,Very frustrating,Very interesting,Moderately successful/ satisfied,Moderately confident,Not at all,There was a column for which I wasn't able to assess how should I proceed with the data cleaning and found it difficult to prompt what should I do next. ,I would prompt gemini with the exacts steps which I would want to follow when doing some cleaning ,Yes extremely helpful since I didn't code prior to this gemini is very very important for me ,if I could refine my prompts I guess it would help me better with Gemini ,I didnt go to my classmates this time ,I wish we did such exercises in class also so its easier to understand ,radhikaj,3,4,5,4,4,1,3
3/23/2025 16:03:42,<EMAIL>,Ashwin Swaminathan,I mostly used prior C/Java knowledge and did Google search for Python syntaxes,Slightly difficult,Moderately effortful,Not frustrating at all,Very interesting,Extremely successful/ satisfied,Extremely confident,Moderately,"I did not use GenAI prompts, I only used the autofill feature after I typed partially. I mostly used prior C/Java knowledge, but it took me some time to familiarize myself with pandas functions and match C/Java syntax to Python",I did not use GenAI prompts except for autofilling the code after I started typing.,I did not require it for this exercise. The autofill feature was very convenient though.,"I could have written shorter and more efficient code. I tried to use basic logic and wrote step by step code, but I possibly could have merged multiple lines.","I asked my classmate to explain pandas library and the inbuilt functions as they were new to me. My classmate was able to assess my prior knowledge in C/Java and hence explained the Python functions in a comparative fashion, which GenAI might not have done efficiently","I did not require GenAI for this exercise, as the logic was simple and C/Java experience was easily transferrable. But I might need for future assignments as I have not done more complex operations like data visualization.",ashwin2,5,2,3,1,4,3,5
3/23/2025 16:39:20,<EMAIL>,Ansh Pandey,"Colab's innate GenAI (Gemini), Perplexity",Slightly difficult,Slightly effortful,Slightly frustrating,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,Debugging the code generated by Gemini,Evaluate if I have covered all inconsistencies using perplexity after completing the cleanup. Used Gemini predominantly to process the data and generate code to cleanup columns,Extremely helpful,Python Awareness would've helped debug easily,NO help offered or received- GenAI was more than enough,,anshp,4,2,2,2,3,1,4
3/23/2025 17:36:22,<EMAIL>,Upasna Ahuja,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,when i asked it to clean for the 1st time i got ok prompts but i wanted to clean the data even more but AI couldn't understand and it kept giving the same prompt over and over again. I suppose it is because of the default model GPT now transmits the requests to.,Used it to form and validate prompts,"yes, to an extent. Because it stops giving correct responses after the 1st attempt. and if I use Collab's AI it keeps changing the entire prompt which sometimes is completely wrong",How to use the AI effectively in these tasks.,I offered guidance on how completed my assignment and told them to explore how more effectively they could use the tool.,Just in general how to smartly use AI in these tasks. Examples of the prompts was a good strategy but helping with identifying what cleaning needs to be done and even if we don't know say the correct cleaning prompts how best to use AI to get the cleanest possible data. Some generic one's etc.,usa,4,3,3,3,3,1,4
3/23/2025 17:43:42,<EMAIL>,Manasa Nandigama,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Not at all,Giving the prompts,Asking what can I do,"yes, because it guided me on what to do",giving right prompts,I offered how I gave prompts,,mnandiga,3,3,3,2,3,1,3
3/23/2025 19:43:03,<EMAIL>,Karan Qasba,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Not at all,Not every time it gave the correct code. I had to do changes to make it work,I gave GenAI prompts as per the assignment and gave the validations and instructions on how to clean the data so that it can write the code for me,Yes it is helpful if you don't know how to code. It did most the work with little instructions,if it could understand what is there on the screen and what variables to use in the code and give less errors in the code,I didn't ask anyone about the assignment as everyone was doing trial and error.,No,kqasba,3,3,3,3,3,1,3
3/23/2025 19:46:37,<EMAIL>,Roni Kim,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Slightly effortful,Very frustrating,Moderately interesting,Extremely successful/ satisfied,Very confident,A little bit,"If I got an error, it was hard to decipher what went wrong and what point. It became difficult since I have very little understanding how coding works. ",Yes,Very helpful. I don't know how to code so to have the GenAI write the code for me was amazing. ,Nothing I can think of at the moment. ,I was having a hard time with the TamperMonkey because I was using Safari initially. So my peers had to tell me to switch to Chrome. ,NA,rkkim,5,3,2,4,3,2,4
3/23/2025 20:21:19,<EMAIL>,Devisha Tayal,"Colab's innate GenAI (Gemini), ChatGPT",Very difficult,Very effortful,Extremely frustrating,Very interesting,Slightly successful/ satisfied,Moderately confident,Moderately,The most challenging part was how to use a better prompt. I compared two models and the answers were different. ,I asked on how to clean and give me the steps to clean the data,"Yes, it was very useful because it gave me the code to clean the data.",I think how to better prompt.,I asked on what prompts they were using,"On one column, I tried the steps that Gemini told me and it ruined the data and I couldn't figure out how to get the data back.",dtayal,2,4,4,5,4,3,3
3/23/2025 21:18:19,<EMAIL>,Cody Soska,Colab's innate GenAI (Gemini),Not difficult at all,Very effortful,Very frustrating,Extremely interesting,Slightly successful/ satisfied,Very confident,A little bit,"Gemini simply does not remember earlier prompts or have robust ""fast simulation"" for running code before writing it as an output. I repeatedly gave it prompts that were generating errors (that I understood), but it was not updating it's methods or ""simulating"" what the code might do (converting float64 to int64 is obviously not possible the way it was doing it). It also didn't seem to understand the data types. I literally had to pretrain it by saying, ""the number in row 1 under days_since_release is 1120.0. What is the data type of this number?"" It correctly said, ""float"", but could not see this in it's code output. As someone who know how to program, doing this assignment ""by the books"" and using the integrated AI, actually made it harder. Though, if you cannot program, this is by far a better alternative.","I formulated the idea of what I wanted it to do and then asked explicitly. This will never work with Gemini currently. I was able to get the correct outputs by code tracing what it was writing vs what i wanted it to do. Short of feeding it pseudocoe line-by-line, this was the only way.","I would only be able to do this with AI or 3 times as long combing forums for answers, but Gemini aint it!",Maybe a look at Gemini's backend or using it as my go-to genAI would help. I normally use ChatGPT and DeepSeek.,I asked my partner (not a CMU student) who is a professional data analysist for help in what the task was. We really need to be more clear and explicit in what you want us to do. Maybe an example response would help?,,csoska,2,1,4,4,5,2,4
3/23/2025 22:51:38,<EMAIL>,Hassaan Pasha,"Colab's innate GenAI (Gemini), ChatGPT",Slightly difficult,Moderately effortful,Moderately frustrating,Very interesting,Very successful/ satisfied,Very confident,A little bit,Ensuring that the code actually delivers the result,"Just asked it for certain validations rule, but the examples it quoted made things easy","Yes... Do not know anything about using python, but GenAI made it very easy",N/A,"I was not sure about the columns that I needed to clean, so I asked my peers about that.. but later found that everyone was using their own 5 columns.. so I did the same","I could not find the Download History button, so I had to reinstall the extension again to get it back.. therefore I guess it did not record my history/task execution",hpasha,4,2,3,3,4,2,4
3/23/2025 23:32:28,<EMAIL>,Soham Mondal,Colab's innate GenAI (Gemini),Not difficult at all,Slightly effortful,Slightly frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Not at all,"Which data cleaning rules to incorporate and finding out the final goal of the assignment, how much data to clean.",Used Gemini in Colab,"Yes it is helpful, for finding out meaning of error messages. It reduces the need to go to stack exchange or other platforms to identify error messages.","Because the assignments are broken down into ambiguous steps to reach a final goal, providing accurate prompts was a challenge.",No help taken. I know python and Google Gemini was enough for me.,"No comments. Data Cleaning is an essential step and according to me, this is how it should be done in Google Colab but unfortunately many monolithic companies and their employees believe that Excel is better and subsequently visualization tools like PowerBI, Tableau for mostly product marketing roles.",sohammon,3,1,2,2,3,1,3
3/23/2025 23:36:04,<EMAIL>,Abdul Gaffoor Shaik,Colab's innate GenAI (Gemini),Slightly difficult,Moderately effortful,Slightly frustrating,Moderately interesting,Extremely successful/ satisfied,Extremely confident,Not at all,different versions everytime. complex code for simple stuff.,to generate syntax. i used my logic.,yes. for base code.,accurate prompts,I offered help for struggling follks. I havent taken any help ,,agshaik,5,2,3,2,3,1,5
3/23/2025 23:37:43,<EMAIL>,Bhavya Arora,"Colab's innate GenAI (Gemini), I have no knowledge of programming and learning python for the first time for this course. So i used gemini for generating specific lines based on what my intention was and also used gemini if i couldnt figure out anything. i also took help of a friend only at the places where i was stuck in a looped error and was unable to proceed even after using help from google. ",Extremely difficult,I need to work extremely hard,Extremely frustrating,Moderately interesting,Extremely successful/ satisfied,Very confident,Moderately,most difficult part was prompting. I like to chunk down problems and i tried to do the same with the code also. For example. i would ask only for removing spaces first and then i saw some special characters in pokemon so asked to removed that. it was frustrating to club them together,as mentioned above ,yes it was definetely very helpful. I have started learning python only 2 days back so i dont know much. GenAi explained me the code very neatly ,I am not very sure ,I mentioned before ,NA,barora,5,5,5,5,3,3,4
3/24/2025 0:43:03,<EMAIL>,Esha Lakra,Colab's innate GenAI (Gemini),Not difficult at all,Slightly effortful,Not frustrating at all,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,I did not find it that challenging specially with GenAI used,"I used it for the codes only- to view sample data per column, for cleaning as per my steps and also codes for validation through random entries after running my cleaning up codes","Yes, as mentioned above for coding","None that I think of, from this assignment's perspective specially","This was straightforward, did not use help apart from colab's genAI",,elakra,4,1,2,1,3,1,4
3/24/2025 8:33:30,<EMAIL>,Somya Mehta,ChatGPT,Very difficult,I need to work extremely hard,Very frustrating,Very interesting,Extremely successful/ satisfied,Extremely confident,Not at all,for some syntax for eg - How split function can be written etc,I used genAI to understand the exact syntax that can be used for performing tasks like splitting and replacing,Yes it was helpful because I was able to get the exact syntax quickly,Knowing couple of common used syntax for data cleaning would fasten the process,I didn't take help from peers,None,somyameh,5,4,5,4,4,1,5
3/24/2025 8:36:28,<EMAIL>,Nikhil Medarametla,Perplexity,Not difficult at all,Slightly effortful,Not frustrating at all,Very interesting,Extremely successful/ satisfied,Extremely confident,A little bit,"Making sure the cleaning logic aligned with the instructor’s expectations, not just what GenAI suggested.","I used it to guide my approach, generate clean and optimized code, and validate my steps.","Yes, it helped me save time, avoid common mistakes, and stay organized throughout the task.",Having clearer feedback on what the grader is specifically looking for when using GenAI.,"I checked in with classmates about formatting, rubric interpretation, and how they handled specific columns.",It was a really practical task that pushed me to combine data skills with smart tool usage.,nmedaram,5,1,2,1,4,2,5
3/24/2025 10:54:26,<EMAIL>,Aminat Afonja,Colab's innate GenAI (Gemini),Not difficult at all,Moderately effortful,Not frustrating at all,Moderately interesting,Moderately successful/ satisfied,Very confident,Not at all,GenAI gave some codes that led to error. ,I asked questions to understand the concepts and got codes from GenAI,"Yes, it was helpful because it explained the concepts to me and made the coding easy.",Understanding the basics and asking the right questions,None,"1. I was kind-of confused on the 5 required columns as seen in the colab sheet and canvas rubic. 
2. I was not sure if to start the data cleaning by checking the info and table description or just go directly to cleaning the columns.",aafonja,3,1,3,1,3,1,4
3/24/2025 11:07:13,<EMAIL>,Wenyi Li,Colab's innate GenAI (Gemini),Slightly difficult,Slightly effortful,Slightly frustrating,Slightly interesting,Moderately successful/ satisfied,Very confident,Not at all,Sometimes GenAi would miss # this kind of symbol,use prompt in colab,Helpful cause I don't familiar with coding.,Share more about what is the right prompt,No,,wenyili,3,2,2,2,2,1,4
3/24/2025 11:47:56,<EMAIL>,venkata himakar yanamandra,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Very frustrating,Very interesting,Extremely successful/ satisfied,Extremely confident,Not at all,gemini was not able to understand my intent and was giving errors ,"I used genAI to generate the code, then debugged the code using google when it gave errors",it was useful ,knowing what clearly you want from gemini could have made the task easier,N/A,,hyanaman,5,3,4,4,4,1,5
3/24/2025 11:56:58,<EMAIL>,Eliana,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,Not sure if I was doing it right,to code,"yes because it helps me generate ideas, because without it i'll be lost",understanding the question better,i didnt,,elianah,3,3,3,3,3,3,3
3/24/2025 12:03:29,<EMAIL>,Tejas,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Moderately confident,A little bit,"Sometimes the prompt did not generate expected code, it was slightly challenging. ",I used GentAI to generate code and help with finding error,GenAI was very helpful as it would have taken a lot of time to write the code manually.,"If I had more knowledge about python libraries, it might further help me improve.   ","I had helped them with figuring out initial task, whereas i got some insights on how can we proceed with the setup.",,tsohani,4,3,4,2,4,2,3
3/24/2025 12:23:28,<EMAIL>,Ankit Shukla,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Not frustrating at all,Extremely interesting,Extremely successful/ satisfied,Extremely confident,Not at all,"Sometimes, the code generated wasn't working; there were many compilation and execution errors. Sometimes, the logic did not work , so I had to debug it by using print statements and visualizations.",Just asked it questions wherever I needed help,"Yes - it helped me in multiple ways, both debugging issues and writing code",Not sure. I think its good.,No questions,,ankitshu,5,4,4,1,5,1,5
3/24/2025 12:25:33,<EMAIL>,Siddhant Sagar,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Very effortful,Moderately frustrating,Very interesting,Very successful/ satisfied,Extremely confident,A little bit,To get the appropriate answers with regards to the task. Sometimes the LLMs provided divergent responses.,Yes,"Bit helpful, but some answers were out of context",Getting better understanding of the context and subsequently writing the prompts.,Specifically on the different approaches they took while refining the dat,,ssagar2,4,3,4,3,4,2,5
3/24/2025 12:57:06,<EMAIL>,Hengyue Zhao,Colab's innate GenAI (Gemini),Moderately difficult,Slightly effortful,Slightly frustrating,Moderately interesting,Slightly successful/ satisfied,Slightly confident,Not at all,none,"I'll try to write the code myself first, and let genai correct the errors and generate new code for me","it‘s very helpful, it's would give me appropriate suggestions","It is more important to master the code writing itself, ai is more to help us accomplish some syntax correction",none,none,hengyuez,2,3,2,2,3,1,2
3/24/2025 18:49:06,<EMAIL>,Nakshatra Sharma,"Colab's innate GenAI (Gemini), Basic VsCode to compile the whole code then input to notebook",Slightly difficult,Slightly effortful,Not frustrating at all,Very interesting,Slightly successful/ satisfied,Slightly confident,Not at all,N/A,For understanding and recommendations on what data cleaning entails overall,Yes it helped me come up with what areas of the data can be improved,Not sure,N/A,Would be great to have more clearly defined data cleaning steps or requirements for the task.,naxs,2,2,2,1,4,1,2
3/24/2025 20:36:07,<EMAIL>,khanishkaa,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Very frustrating,Very interesting,Not successful/ satisfied at all,Not confident at all,Very much,understanding the code,asked help to data set,yes,learning and understanding basics of code,explaining code,,kviknesh,1,4,4,4,4,4,1
3/25/2025 14:42:45,<EMAIL>,Shriya Jashnani,Colab's innate GenAI (Gemini),Slightly difficult,Slightly effortful,Not frustrating at all,Moderately interesting,Very successful/ satisfied,Extremely confident,Not at all,Explanation of my logic in the prompt,I formed my own logic to clean the data and used GenAI for the code and syntax,"Yes, It really helped me with the python coding syntax",Learning to explain my logic in the prompt more clearly,Explaining my logic for cleaning the data,NA,sjashnan,4,2,2,1,3,1,5
3/25/2025 14:46:03,<EMAIL>,Ram Kaushik Ramalingan,None,Slightly difficult,Slightly effortful,Not frustrating at all,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,N/A,N/A,N/A,N/A,"I helped them understand how Python works, as well as help them engineer prompts in the ideal way possible to get desired code/","The class will benefit immensely if they understand how to structure prompts for chatgpt/gemini. Otherwise, they're finding it difficult to understand the problem and use python",ramkausr,4,2,2,1,3,1,4
3/25/2025 14:47:41,<EMAIL>,Caroline Fan,Colab's innate GenAI (Gemini),Not difficult at all,Moderately effortful,Not frustrating at all,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,Describe the task,When I have coding problems,yes. Good at problem targeting,none,none,,sijiaf,4,1,3,1,3,1,4
3/25/2025 14:55:03,<EMAIL>,Aditya Teja Bhimavarapu,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,I didn't use gen AI for all columns. But when I used Gemini to clean couple of columns - it referenced incorrect column names in the code (semantic error not syntax),"For few columns, I was not able to remember syntax, so used Gemini to do that part for me","Yes, it was helpful. But not so much, I took a look at the CSV file in excel and observed what cleaning was required so that we don't overlook any steps",Give more accurate instructions by first understanding the task fully and then guiding it to code it for us.,None.,,abhimava,4,3,4,2,4,1,4
3/25/2025 15:40:30,<EMAIL>,Kritika,Colab's innate GenAI (Gemini),Slightly difficult,I need to work extremely hard,Not frustrating at all,Extremely interesting,Very successful/ satisfied,Very confident,Not at all,"I just struggled initially while navigating. But after that it became easy. Using Gen AI made it very easy. Also, i tried learning how to code based on the answers Gen AI gave me. ",I basically gave instructions and asked Gen AI to code. ,Yesss I found it extremely helpful. I am not sure how correctly have I done the task. I am somewhat confident but it did help me through the entire task. Towards the end I was actually able to understand and write the code myself. ,I was pretty comfortable using it after the first time. As we go through the course I'll be able to understand more in detail the kind of tasks I would have to do. ,"I helped a few peers through the navigation and told them how they could use it. But otherwise, it was pretty manageable. ",NA,krastogi,4,2,5,1,5,1,4
3/25/2025 15:53:38,<EMAIL>,Alex Ding,Colab's innate GenAI (Gemini),Slightly difficult,Moderately effortful,Moderately frustrating,Very interesting,Moderately successful/ satisfied,Slightly confident,A little bit,"sometime the code run, but still shows up with some unreasonable command",frequently just ask for code ,"really help, no worry about debug",the accuracy for data clean process,the idea on how to clean data,,alexding,3,2,3,3,4,2,2
3/25/2025 18:42:12,<EMAIL>,Mukul Lal,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Very effortful,Moderately frustrating,Very interesting,Moderately successful/ satisfied,Moderately confident,A little bit,Using the correct prompt,Prompting it to do the task that I identified,I am not sure because I don't know if it was able to run the tasks just like I wanted it to,Knowledge of Python,Approach for cleaning a particular column,,mlal,3,3,4,3,4,2,3
3/27/2025 0:49:27,<EMAIL>,Jonathan Gu,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Slightly interesting,Very successful/ satisfied,Moderately confident,Not at all,"GenAI can't see the actual data, only we can",I used the gemini button in the top right of the notebook,I found GenAI helpful particularly with syntax.,I think that letting GenAI see the entire files would let it quickly identify inconsistencies that I may miss with my human eye.,I did not offer or receive help from peers nor classmates.,,jgu2,4,3,3,2,2,1,3
4/6/2025 20:15:12,<EMAIL>,Vrushal Sakharkar,"Colab's innate GenAI (Gemini), Perplexity",Moderately difficult,Very effortful,Very frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,"To explain and then get results as per expectation, sometimes it felt more clear in mind than the results I was getting",I used to query first with the result I am looking for and then understand the output given,"Yes, it helped me decode each step very intuitively",I am building my understanding based on questions and output and then reverse engineering to understand what the code is actually doing. I was thinking a overall summary at the end of the task would help connect the dots throughout the activities performed in each task,I was looking to understand their perspectives on the given task ad how did they achieved the outputs,,vsakhark,3,3,4,4,3,3,3
