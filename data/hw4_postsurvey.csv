<PERSON>tamp,<PERSON><PERSON>,Name,used_llm,perceived_difficulty_data_storytelling,perceived_effort_data_storytelling,perceived_stress_data_storytelling,perceived_engagement_data_storytelling,perceived_success_data_storytelling,confidence_data_storytelling,helpseek_data_storytelling,challenge_used_llm,llm_use,llm_helpful,help_needed,help_peer,comment,andrewid,perceived_success_data_storytelling_score,perceived_difficulty_data_storytelling_score,perceived_effort_data_storytelling_score,perceived_stress_data_storytelling_score,perceived_engagement_data_storytelling_score,helpseek_data_storytelling_score,confidence_data_storytelling_score
4/17/2025 23:27:30,<EMAIL>,<PERSON><PERSON>,"<PERSON><PERSON>'s innate GenAI (Gemini), ChatGPT",Moderately difficult,Very effortful,Very frustrating,Extremely interesting,Very successful/ satisfied,Moderately confident,A little bit,<PERSON> doesn't give direct answers and I feel overwhelmed by the content.,"I used GenA<PERSON> to understand what can I write in the report, what visualizations I can use and help me code. ","Yes absolutely. Without it, I would be staring at my screen with probably just a nice introduction.",Better prompt engineering,Same as before where I asked the methods they were using and some prompts that were helpful,This was a nice conclusion to the whole assignment. Thank you!,dtayal,4,3,4,4,5,2,3
4/18/2025 15:56:32,<EMAIL>,<PERSON> <PERSON>ushik <PERSON>ling<PERSON>,<PERSON><PERSON>'s innate GenAI (<PERSON>),Very difficult,Very effortful,Very frustrating,Very interesting,Very successful/ satisfied,Very confident,Very much,NA,Generating code in specific places,Yes,NA,Helped them structure prompts for <PERSON>,,ramkausr,4,4,4,4,4,4,4
4/18/2025 15:56:48,<EMAIL>,Nivedita Yadav,ChatGPT,Slightly difficult,Slightly effortful,Slightly frustrating,Slightly interesting,Slightly successful/ satisfied,Slightly confident,A little bit,None,Prompts and got the code ,"Yes, it gave me a general outline ",Not sure ,None,,nivedity,2,2,2,2,2,2,2
4/18/2025 21:46:57,<EMAIL>,Yi Wen Tan,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Very effortful,Very frustrating,Very interesting,Moderately successful/ satisfied,Slightly confident,A little bit,I didn't really know how to write the prompt for the type of complex data analyses to generate the charts that I wanted so I had to keep to simple analyses and charts.,To generate the charts.,"Yes, because I didn't have enough of python programming skills alone to generate the charts that I needed for the analyses.",Some sample template or suggestions on the different types of charts that I can generate for the assignment.,I only asked my classmates on how they intended to present their report but didn't get much consensus so I just did my report to the best of my ability.,NIL.,yiwentan,3,3,4,4,4,2,2
4/19/2025 18:36:57,<EMAIL>,Mukul Lal,Colab's innate GenAI (Gemini),Extremely difficult,Very effortful,Very frustrating,Not interesting at all,Not successful/ satisfied at all,Not confident at all,Not at all,Wasn't clear on the expectations of the task,Generating charts,Don't know as I don't know anything about Python. But hopefully the output was correct,Learn Python,N/A,,mlal,1,5,4,4,1,1,1
4/19/2025 19:15:58,<EMAIL>,Angelica,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,Keep track of my story and objectives for this assignment within same chat window,"To provide explanations, refresh concepts and generate code","Yes, accelerate my workflows to complete my assignment",N/A,Didnt ask,,achavess,4,3,3,2,4,1,4
4/20/2025 12:44:57,<EMAIL>,bhavya arora,"ChatGPT, self",Slightly difficult,Very effortful,Not frustrating at all,Very interesting,Very successful/ satisfied,Very confident,Very much,i didnt use it much,i didnt use it ,i didnt use it ,i didnt use it ,i helped my peers understand the assignment ,,barora,4,2,4,1,4,4,4
4/20/2025 14:37:13,<EMAIL>,Manasa Nandigama,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,Giving prompts,Just asked for some limitations,Yes,Asking right prompts,Nothing,,mnandiga,4,3,4,2,4,1,4
4/20/2025 20:46:38,<EMAIL>,Shriya Jashnani,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Not frustrating at all,Very interesting,Extremely successful/ satisfied,Extremely confident,A little bit,"Giving context, as this was related to the last 3 assignments",Codifying my approach for story-telling,"Yes, I helped to with the analysis of the different approached i had adopted","Context generation, incase of assignments where we have a lot of data",Understanding how they were leveraging their previous work,,sjashnan,5,3,3,1,4,2,5
4/20/2025 21:35:17,<EMAIL>,Aditya Teja B,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Slightly frustrating,Extremely interesting,Very successful/ satisfied,Very confident,Not at all,"To allign different sections, relating them to the previous assigments with insights and ML models",for code generation and checking the cohesiveness of the reports,"yes, as a code assistant and also to critique the final report",explaining better what we did in assignment 2 & 3,None,,abhimava,4,3,4,2,5,1,4
4/21/2025 0:59:55,<EMAIL>,Charlene Lin,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,"Since this assignment follows a particular order, in the last assignment I feel like need to explain or make sense of what I did, but I imagine this won't be how I do the task in real world senario.",Ask ChatGPT to make sense of the prior analysis.,"Yes. Because I didn't think through what model to use and what's the purpose of using model in this feature suggestion, I ask GenAI to help make sense of combining the analysis and the model creation.","Before doing the series of assignments, having a clear sense of how I would use different techniques like EDA or building models to complete this task.",No.,,charlenl,3,3,3,3,3,3,3
4/21/2025 1:11:29,<EMAIL>,Radhika Jain,Colab's innate GenAI (Gemini),Extremely difficult,I need to work extremely hard,Extremely frustrating,Moderately interesting,Slightly successful/ satisfied,Slightly confident,Not at all,It was difficult to prompt to get the right answer. There were multiple times where there were errors in the code because gemini took the wrong columns also there were times when gemini dint understand and took random data sets and suggested answers ,I prompted everything to reach to an answer ,ya ,for sure there is a lot of scope for improvement and it starts from matching the col name ,nothing I only relied on gemini ,,radhikaj,2,5,5,5,3,1,2
4/21/2025 1:38:25,<EMAIL>,Abdul Gaffoor Shaik,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,NA,NA,NA,NA,NA,,agshaik,3,3,3,3,3,3,3
4/21/2025 2:36:09,<EMAIL>,Suhail Khan,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,fixing variable names and copy pasting code and error together for more accurate response.,Majorly for brainstorming and for writing code,"Yes, with GenAI, it's easy and fast to write code","instead of prompt seeing my cursor, it should brainstorm together and give nudges to think in right direction",I shared my thought and approach and listen theirs how to approach task,NA,suhailk,4,3,3,2,3,1,4
4/21/2025 4:17:36,<EMAIL>,Jonathan Gu,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Not at all,The AI I used for this pretty much did exactly what I asked it to do,I prompted GenAI to help me create the altair charts.,I found using GenAI extremely helpful during the task.,I think that if GenAI could actually see my charts it would be helpful.,I did not offer nor receive help from peers,,jgu2,3,4,4,3,3,1,3
4/21/2025 4:57:37,<EMAIL>,Ashwin,ChatGPT,Slightly difficult,I need to work extremely hard,Extremely frustrating,Slightly interesting,Slightly successful/ satisfied,Slightly confident,Moderately,Explaining the requirement and aligning them to previous work,For coding,"Yes, helped me write code",better guidelines for clearer instructions ,Suggestions for avenues of analysis,Very lengthy assinmnent,ashwin2,2,2,5,5,2,3,2
4/21/2025 7:07:50,<EMAIL>,Aminat Afonja,Colab's innate GenAI (Gemini),Extremely difficult,I need to work extremely hard,Moderately frustrating,Slightly interesting,Extremely successful/ satisfied,Extremely confident,Not at all,Giving the correct answer to my prompt,To get the right code for execution,"Yes, it helped with the code",Asking the right questions and understanding the code,N/A,No,aafonja,5,5,5,3,2,1,5
4/21/2025 8:05:49,<EMAIL>,Grace,Claude,Not difficult at all,I do not need to work hard at all,Not frustrating at all,Moderately interesting,Not successful/ satisfied at all,Not confident at all,Not at all,No,Summarize my thoughts ,Yes because I didn't have to write repetitive information I already analyzed in previous homeworks,NA,No,,gracelia,1,1,1,1,3,1,1
4/21/2025 9:18:19,<EMAIL>,Somya Mehta,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,A little bit,I was able to give the insights and generate code easily so nothing challenging,Generate codes based on insights,Yes,Some example prompts to get started with the assignment,Double checking my understanding of the assignment,-,somyameh,4,3,3,2,4,2,4
4/21/2025 10:41:08,<EMAIL>,Roni Kim,"Colab's innate GenAI (Gemini), ChatGPT",Very difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Very successful/ satisfied,Slightly confident,Not at all,Not really challenging,Used it so that it would write the code,Very helpful because I do not know how to code,NA - It did everything that I need it to,No I did not. ,NA,rkkim,4,4,3,3,3,1,2
4/21/2025 10:50:39,<EMAIL>,Esha Lakra,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,No much challenge for this task. As this was about building a story from the previous work on EDA and modeling.," As this was about building a story from the previous work on EDA and modeling, there was limited use except when I wanted to pick critical insights from EDA to build the narrative and possibly refine the insights at some place.",Yes. For the satisfactory help for the above.,Not much w.r.t this assignment,None. Used the task description and expectations from rubric to build the report!,,elakra,4,3,3,3,3,1,4
4/21/2025 11:00:48,<EMAIL>,Kritika Ratsogi ,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Slightly frustrating,Extremely interesting,Extremely successful/ satisfied,Moderately confident,Not at all,"I was confused about how I would go about asking Gen AI. I had so many thoughts and issues, and didn't understand how I could ask Gen AI how solve it. ",I used it mainly for paraphrasing and detailing my points. Rest I did all the thinking myself. ,I think it could help me better if I could understand better on how to use it. ,Understand how to prompt here/ ,"I tried asking, but I got varied opinions on what needs to be done so I stuck to my approach ",,krastogi,5,4,4,2,5,1,3
4/21/2025 11:04:54,<EMAIL>,Soham Mondal,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Moderately frustrating,Very interesting,Very successful/ satisfied,Moderately confident,Not at all,"Understanding how to use the data from the past 2 assignments to help create a narrative/story which can help in product recommendations. Also, since the code is simpler the focus was not resolving bugs but whether to focus upon again recreating the code to generate results.",To debug errors in the code as it was Google Gemini inbuilt within colab.,I have not used GenAI in this assignment much at all.,For this assignment GenAI will not help much except for structuring.,I helped Ankit solve one query about how to approach the problem.,,sohammon,4,4,4,3,4,1,3
4/21/2025 11:39:50,<EMAIL>,Nakshatra (Nax) Sharma,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Not frustrating at all,Extremely interesting,Moderately successful/ satisfied,Slightly confident,Not at all,It won't fix the errors,Gemini to fix errors,Yes to fix errors,Better error fix,N/A,,naxs,3,3,4,1,5,1,2
4/21/2025 12:06:08,<EMAIL>,Hassaan Pasha,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Very effortful,Very frustrating,Slightly interesting,Very successful/ satisfied,Very confident,Not at all,Writing the prompts correctly according to the context to get the right structure of the report... struggled a bit with the right presentation techniques/elements.,Gave the right context after several tries to get the right outputs that I was looking for,"Not very much since this was a report. I had a few ideas of how to present it, but i think colabs doesnt do a good job at the final report. I would use some other tool to do the same after copying visuals from colab.",N/A,None,N/A,hpasha,4,3,4,4,2,1,4
4/21/2025 12:09:54,<EMAIL>,Caroline Fan,Colab's innate GenAI (Gemini),Slightly difficult,Slightly effortful,Slightly frustrating,Slightly interesting,Moderately successful/ satisfied,Moderately confident,Moderately,NA,Code,Useful,NA,NA,,sijiaf,3,2,2,2,2,3,3
4/21/2025 12:12:18,<EMAIL>,Tejas Sohani,Colab's innate GenAI (Gemini),Slightly difficult,Very effortful,Not frustrating at all,Very interesting,Very successful/ satisfied,Very confident,Very much,I tried using Idyll for story telling but could not integrate ,To know how to use Idyll for story telling,I find it difficult to get help on Idyll on GenAI,GenAI can be improved in terms of platform integration . Here idyll integration with collab help was very limited,In understanding what task has to be completed and the format of assignment ,,tsohani,4,2,4,1,4,4,4
4/21/2025 12:30:40,<EMAIL>,WenyiLi,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,Ask the right question,help me with coding,"Yes, because I don't know how to coding",/,/.,,wenyili,3,3,3,3,3,3,3
4/21/2025 12:36:35,<EMAIL>,Siddhant Sagar,Colab's innate GenAI (Gemini),Moderately difficult,Slightly effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,Having insights was not straightforward,To get a starting point,Moderately helpful as we needed a good starting point,More objectivity,To give a starting point.,,ssagar2,3,3,2,3,3,3,3
4/21/2025 14:52:11,<EMAIL>,Nikhil Medarametla ,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Not frustrating at all,Very interesting,Very successful/ satisfied,Extremely confident,Not at all,"Sometimes, it was hard to judge whether the AI's suggestions were fully accurate or aligned with best practices in data science.","I used GenAI to brainstorm code logic, debug issues, and clarify concepts like model evaluation metrics.
","Yes, it helped speed up my workflow and provided quick explanations when I was stuck or unsure about something",Better prompting skills and clearer instructions could help me get more accurate and relevant outputs from GenAI.,"I discussed approaches and model choices with peers, especially when I wanted a second opinion or real-world context that AI might miss.
","Using GenAI was like having a helpful assistantI still had to make the final calls, but it made the process smoother and less stressful.",nmedaram,4,3,3,1,4,1,5
4/21/2025 16:37:42,<EMAIL>,venkata himakar yanamandra,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Very frustrating,Very interesting,Very successful/ satisfied,Very confident,Very much,finding unique features that give orthogonally benificial data ,"to go in-depth with my thought process, visualization ideas","kinda , but it was giving a lot of errors ","1) to have more clarity on the initial thought process
2) the number of errors if reduced in next gemini models, 
3) if gemini gives percent of accuracy",I told them how to connect features to ML modelling in general deriving from my professional work,,hyanaman,4,3,4,4,4,4,4
