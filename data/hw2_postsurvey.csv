Timestamp,Email Address,Name,used_llm,perceived_difficulty_eda,perceived_effort_eda,perceived_stress_eda,perceived_engagement_eda,perceived_success_eda,confidence_eda,helpseek_eda,challenge_used_llm,llm_use,llm_helpful,help_needed,help_peer,comment,andrewid,perceived_success_eda_score,perceived_difficulty_eda_score,perceived_effort_eda_score,perceived_stress_eda_score,perceived_engagement_eda_score,helpseek_eda_score,confidence_eda_score
3/24/2025 22:53:28,<EMAIL>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>'s innate GenAI (Gemini),Moderately difficult,Very effortful,Moderately frustrating,Very interesting,Moderately successful/ satisfied,Moderately confident,Not at all,asking the correct prompts,for code,yes as it could provide me the code,correct prompts,none,,rupalc,3,3,4,3,4,1,3
3/26/2025 14:52:50,<EMAIL>,<PERSON><PERSON>,<PERSON><PERSON>'s innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,N<PERSON>,To write the code for me. ,Very helpful - I don't know how to code. ,<PERSON><PERSON>,Didn't ask. ,,rkkim,4,3,3,2,4,1,4
3/27/2025 17:02:29,<EMAIL>,grace,<PERSON>b's innate GenAI (Gemini),Not difficult at all,Slightly effortful,Not frustrating at all,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,na,to ask questions about syntax,"yes, it had good explanations",na,no,,gracelia,4,1,2,1,3,1,4
4/5/2025 9:57:35,<EMAIL>,Devisha Tayal,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Moderately confident,A little bit,"This time it got easier because of the lectures, I knew what to expect and what sort of questions I could ask GenAI. The challenging part was to understand the visualizations and generate insights.",I used GenAI to help me come up with questions and code for visualization. I also asked its help in summarizing the insights.,Yes 100%. GenAI helped me uncover a lot more insights and questions that I couldn't even think of.,How to prompt better would help.,I asked about some of the prompts they were using. I also reached out to 2 people who have experience to gain more insights into the type of questions we can explore.,This was a fun task because visualization is fun!,dtayal,4,3,3,2,4,2,3
4/5/2025 16:34:32,<EMAIL>,Nivedita,Colab's innate GenAI (Gemini),Moderately difficult,Slightly effortful,Not frustrating at all,Very interesting,Moderately successful/ satisfied,Slightly confident,Not at all,probably getting the right promptd ,Giving prompts for code and visualization ,"Absolutely, it generates the code and does most of the work for me ",Not sure at this time ,What kind of prompts to give to the LLM ,,nivedity,3,3,2,1,4,1,2
4/5/2025 17:06:04,<EMAIL>,Manasa Nandigama,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,Giving the prompts,Asked for questions and code,"Yes, it was helpful",Giving right prompts,None,,mnandiga,4,3,3,3,3,1,4
4/6/2025 15:17:33,<EMAIL>,Nikhil Medarametla,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Not frustrating at all,Very interesting,Extremely successful/ satisfied,Very confident,A little bit,"Figuring out how to frame complex, insightful EDA questions that align with the rubric was a bit challenging.",I used it to generate and write clean code for analysis and visualizations.,"Yes, it helped save time, kept my code clean, and made the insights more focused and relevant.",Having more examples of top-notch EDA questions and visualizations for inspiration would be helpful.,We discussed which EDA questions were worthy and helped each other debug Altair or data formatting issues.,"This task felt practical and closely tied to real product management work, I really enjoyed it.",nmedaram,5,3,3,1,4,2,4
4/6/2025 15:54:47,<EMAIL>,Somya Mehta,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Extremely confident,A little bit,What prompts to give ChatGpt was initially difficult,Mainly used it to improve the clarity of my visualizations and insights.,"Yes, found it very helpful. I was easily able to spot errors and gave me ideas which I may have not been able to do since I was doing it first time",A prompt checklist to guide what we can input in chatgpt to get responses,I checked with peers mainly if we needed to generate statistics as well and how they did it.,The lecture and exercise on visualization was interesting and i can see how it will apply in my job later.,somyameh,4,3,4,2,4,2,5
4/6/2025 17:10:38,<EMAIL>,Charlene Lin,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,A little bit,Had to pivot the questions and answers a lot.,Ask for suggestions for what EDA questions to ask; ask for if I want to know something what is the right questions to analyze and how to analyze them; ask for insights writing draft,Yes. Can help with exploring what questions to ask,Define what my end goal is before analyzing,"Explore what my end goal should be, and what questions I might want to analyze",,charlenl,3,3,3,3,3,2,3
4/6/2025 19:43:45,<EMAIL>,Jonathan Gu,Colab's innate GenAI (Gemini),Very difficult,Very effortful,Moderately frustrating,Very interesting,Very successful/ satisfied,Moderately confident,Not at all,"I couldn't get GenAI to understand exactly what I wanted. Also, it really screwed up my line of linear regression entirely.",I used GenAI to help me code the ideas that I created myself.,"I found GenAI very helpful because I have never done data visualization before, and it helped me both with syntax and writing.","I think that if I was a bit more specific with the prompts I was asking, maybe it could have done what I wanted more effectively. However, I think that because GenAI was not able to do everything for me, I was able to learn much more by figuring things out on my own.",I did not communicate with peers/classmates on this assignment.,,jgu2,4,4,4,3,4,1,3
4/6/2025 20:49:55,<EMAIL>,Aminat Afonja,Colab's innate GenAI (Gemini),Not difficult at all,Moderately effortful,Not frustrating at all,Very interesting,Extremely successful/ satisfied,Extremely confident,Not at all,"It was pretty straight forward, just that I need to understand the code in detail.",To get the correct code to run,"Yes, it was helpful. it gave the right code for execution",Understanding of the code,N/A,,aafonja,5,1,3,1,4,1,5
4/6/2025 21:08:50,<EMAIL>,Mukul Lal,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Very effortful,Moderately frustrating,Slightly interesting,Slightly successful/ satisfied,Slightly confident,Not at all,"Knowing if the output was correct, and correlating the output to the statistical analysis",Generate the python code,"Not sure, because I don't know if the output generated by GenAI was correct","Learning python, statistics",N/A,,mlal,2,3,4,3,2,1,2
4/6/2025 21:33:09,<EMAIL>,Vrushal Sakharkar,"Colab's innate GenAI (Gemini), Perplexity",Very difficult,Very effortful,Very frustrating,Very interesting,Moderately successful/ satisfied,Moderately confident,Not at all,It sometimes provided outputs in the format which was not expected for certain visualisation function to build those and have to rephrase my query again and again to refine the results,I used to help me give insights into understanding and getting queries for the particular scenario for which I wanted the data and then used to build on those queries if I needed anything more from that,It was helpful as it provided direct queries in some cases and also explain what each line meant in the query,"Instead of loading everything in the single query, I can break my query into multiple parts to exactly get what I need",I helped classmates to understand the deliverables and how to approach on visualisation part,,vsakhark,3,4,4,4,4,1,3
4/6/2025 23:37:41,<EMAIL>,Aditya Teja B,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Slightly frustrating,Extremely interesting,Very successful/ satisfied,Very confident,Not at all,to clearly communicate the required analysis,"to generate visualization, primarily","instead of relying on sytax and semantics, I could focus on questions",clearly communicate the required analysis,None,,abhimava,4,3,4,2,5,1,4
4/6/2025 23:48:03,<EMAIL>,Shriya Jashnani,Colab's innate GenAI (Gemini),Slightly difficult,Moderately effortful,Not frustrating at all,Very interesting,Extremely successful/ satisfied,Extremely confident,A little bit,Deciding between the best statistical methods,Generating code for the statistical analysis,"Yes, using Numpy was helpful with AI",NA,Understanding what types of statistical methods they were using.,,sjashnan,5,2,3,1,4,2,5
4/7/2025 1:36:19,<EMAIL>,Ankit Shukla,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Slightly frustrating,Extremely interesting,Extremely successful/ satisfied,Extremely confident,Not at all,Some code compilation and build errors. Had to fix them,I used GenAI to ask for code to perform some tasks and fix some issues related to build and compilation.,It was helpful as it saved a lot of time and effort.,If accuracy can be improved - give reliable (error free) code,None,,ankitshu,5,3,4,2,5,1,5
4/7/2025 1:40:28,<EMAIL>,Soham Mondal,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Not frustrating at all,Very interesting,Moderately successful/ satisfied,Very confident,Not at all,I have used Gemini a little bit to solve errors in the code which was challenging as my code was complex.,I have not used much in this assignment except for removing any bugs in my code.,Yes it was really in solving the bugs or typos in my code faster.,GenAI can definitely help and improve performance on these tasks and accelerate the visualization process by improving accuracy of error solving.,I did not take any help from peers or classmates. Have done it on my own.,Have framed a little bit more complex question than that was required. So have put to a lot more effort than initially estimated to complete assignment.,sohammon,3,3,4,1,4,1,4
4/7/2025 2:19:41,<EMAIL>,ESHA LAKRA,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Moderately interesting,Slightly successful/ satisfied,Very confident,Not at all,"Need to explain more for visualization, doesn't directly decipher how to use the statistics efficiently",For generating codes and debugging errors. Some help on the charts too on how to use,"Yes, for the above mentioned tasks",None for this,None,-,elakra,2,3,3,2,3,1,4
4/7/2025 2:55:12,<EMAIL>,himakar yanamandra,Colab's innate GenAI (Gemini),Slightly difficult,Moderately effortful,Very frustrating,Moderately interesting,Extremely successful/ satisfied,Very confident,Not at all,errors and hallucination,to help with the initial code and fixing errors,it was useful,if the gen AI gives confidence rating or gets the column names correct,I asked them level of complexity expected in the assignment,,hyanaman,5,2,3,4,3,1,4
4/7/2025 2:56:20,<EMAIL>,Abdul Gaffoor Shaik,Colab's innate GenAI (Gemini),Slightly difficult,Slightly effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,Very much,NA,NA,NA,NA,NA,,agshaik,4,2,2,2,4,4,4
4/7/2025 3:37:52,<EMAIL>,Sumukh,Colab's innate GenAI (Gemini),Moderately difficult,Slightly effortful,Not frustrating at all,Extremely interesting,Extremely successful/ satisfied,Extremely confident,All the time,Finding the weighted averages,Only in collaboration with,"Sure, it was helpful to quickly ask python to do the minimum",Understanding the code a little/syntax,Ensure that we are supposed to work with the same data set,,gsumukh,5,3,2,1,5,5,5
4/7/2025 11:20:47,<EMAIL>,Caroline Fan,Colab's innate GenAI (Gemini),Not difficult at all,Slightly effortful,Not frustrating at all,Moderately interesting,Very successful/ satisfied,Extremely confident,Not at all,Not challenging at all,Generate outlines for visualization codes,It's faster than manually writing codes,Nan,Nan,,sijiaf,4,1,2,1,3,1,5
4/7/2025 11:49:02,<EMAIL>,Tejas Sohani,Colab's innate GenAI (Gemini),Slightly difficult,Very effortful,Not frustrating at all,Very interesting,Very successful/ satisfied,Extremely confident,Not at all,Python code writing could be challenging but I have used GenAI prompt.,For creating statistics and visualization,Yes it was very helpful for writing python code,"It will be better , if i could get complete results , when dealing with around 300 rows in output",This time I was aware of the process so did not ask any question and helped peers to understand the what to be done in the assignment.,,tsohani,4,2,4,1,4,1,5
4/9/2025 1:15:52,<EMAIL>,Yi Wen Tan,Colab's innate GenAI (Gemini),Moderately difficult,Very effortful,Moderately frustrating,Slightly interesting,Slightly successful/ satisfied,Not confident at all,Not at all,I had issues with plotting the axes and to get it to the right scale that I wanted.,To generate the code for the plotting of the charts.,"Yes, as I otherwise wouldn't have been about to code the charts needed for the visualization.",Better prompt suggestions to get what I am trying to do.,None.,NIL,yiwentan,2,3,4,3,2,1,1
4/9/2025 17:53:52,<EMAIL>,Ram Kaushik Ramalingan,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Extremely successful/ satisfied,Extremely confident,Not at all,Framing the correct prompt,Used it to generate certain pieces of code involving libraries I didn't use,Yes very helpful,Being very specific with prompts,"I helped them structure their prompts correctly to get the desired results, and helped them understand workings of Python due to my expertise",,ramkausr,5,3,3,2,4,1,5
4/10/2025 14:42:58,<EMAIL>,Ansh Pandey,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Very confident,Not at all,Debugging code,Generate code for visualizations,Gemini is very bad at debugging,understand basics of coding to correct errors,No help taken from peers,,anshp,4,3,3,2,4,1,4
4/10/2025 14:44:12,<EMAIL>,Hassaan Pasha,Colab's innate GenAI (Gemini),Slightly difficult,Moderately effortful,Slightly frustrating,Moderately interesting,Very successful/ satisfied,Very confident,Not at all,Ensuring the correct visualization techniques are being used,I asked it to perform the EDA based on what I thought was needed,"Yes very helpful, especially with the coding. Once you translate your knowledge to GenAI, things get relatively easier.",Knowing the right things/prompts to ask the GenAI to do.,Nothing,N/A,hpasha,4,2,3,2,3,1,4
4/10/2025 15:34:53,<EMAIL>,Alex Ding,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Very effortful,Moderately frustrating,Extremely interesting,Very successful/ satisfied,Very confident,All the time,Giving code is not correct ,Asking code for different test and visionary,"really helpful, but need to adjust by extention",deep understanding on what test do I need and correct type of visionary,NA,,alexding,4,3,4,3,5,5,4
4/10/2025 17:08:27,<EMAIL>,radhika  jain,"Colab's innate GenAI (Gemini), ChatGPT",Very difficult,Moderately effortful,Very frustrating,Very interesting,Very successful/ satisfied,Moderately confident,Not at all,there were times when I couldn't understand GenAI's answers and how can I resolve when GenAI suggested Tasks dont work ,yes ,Once I had to go to ChatGPT since I found GenAI useless in that one task but otherwise it was great ,if it could be as conversation as chat GPT ,non at all ,I am good ,radhikaj,4,4,3,4,4,1,3
4/10/2025 23:57:31,<EMAIL>,Ashwin,"Colab's innate GenAI (Gemini), ChatGPT",Moderately difficult,Moderately effortful,Moderately frustrating,Very interesting,Slightly successful/ satisfied,Slightly confident,A little bit,"Graphs weren't as per my expectations, so I had to refine the prompt",Generated code with prompt and refined the prompt multiple times to get desired output,Very helpful as it was able to write code efficiently,Suggestions of insights so that prompts could be structured better,Validation of my results and changing graph style to improve visualization,Please share more specific instructions on the tasks from grading perspective.,ashwin2,2,3,3,3,4,2,2
4/11/2025 12:39:42,<EMAIL>,Nakshatra Sharma ,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Moderately successful/ satisfied,Moderately confident,Not at all,Getting what i needed in terms of response,To remove errors and know the meanings of certain terms.,Very helpful in combatting errors and making code compile,If I spent more time ,No,,naxs,3,3,3,2,4,1,3
4/11/2025 20:08:20,<EMAIL>,Bhavya Arora,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Slightly frustrating,Very interesting,Very successful/ satisfied,Slightly confident,Not at all,I think i am fairly comfortable now ,Yes I did ,Yes it was helpful for finding errors ,I can prompt them better ,I helped them with understanding what is the right hypothesis or data they can test based on the lecture,no,barora,4,3,3,2,4,1,2
4/12/2025 21:05:55,<EMAIL>,Hengyue Zhao,Claude,Moderately difficult,Moderately effortful,Moderately frustrating,Moderately interesting,Moderately successful/ satisfied,Moderately confident,Moderately,"Making sure the GenAI-generated insights were both accurate and aligned with the specific data context, especially when interpreting trends or correlations","I used GenAI to help formulate analytical questions, generate and refine Python code for statistics and visualizations, and interpret data patterns to draw meaningful insights efficiently.","Yes, using GenAI was helpful because it accelerated my analysis process and provided clear guidance on both coding and interpreting data insights.",Having more tailored prompts and clearer task objectives would help me better guide GenAI and refine its outputs more effectively for complex data analysis tasks.,"I discussed data trends and checked assumptions with classmates, asking them about interpretation and assignment-specific expectations instead of using Google or GenAI.",None,hengyuez,3,3,3,3,3,3,3
4/13/2025 11:20:10,<EMAIL>,Kritika Rastogi ,"Colab's innate GenAI (Gemini), ChatGPT",Not difficult at all,Slightly effortful,Not frustrating at all,Extremely interesting,Extremely successful/ satisfied,Extremely confident,A little bit,Nothing really ,I used both Chatgpt and Gemini. I put in the question and ask it to create the code and the prompt ,"It was super simple. I did get scared once I got an error, but that was also fixable through the check error feature",I think this is good!,I did get a little psyched on the error bit and then one of my peers taught me how to fix it so it was sorted. ,Its good!,krastogi,5,1,2,1,5,2,5
4/14/2025 9:13:01,<EMAIL>,Siddhant Sagar,Colab's innate GenAI (Gemini),Moderately difficult,Moderately effortful,Moderately frustrating,Very interesting,Very successful/ satisfied,Very confident,Very much,A bit challenging since we didn't have pre-defined goals,To explore potential methods of EDA,"Yes, found helpful to an extent of providing a starting point",More assistant on potential steps to take,"Yes, asked them about the general approach",,ssagar2,4,3,3,3,4,4,4
